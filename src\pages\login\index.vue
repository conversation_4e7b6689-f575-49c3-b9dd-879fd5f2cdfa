<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-03-04 11:36:04
 * @LastEditors: wjb
 * @LastEditTime: 2025-04-14 08:25:06
-->
<template>
  <div class="container">
    <div class="title_box">
      <div class="title">金华开发区一网统管驾驶舱</div>
      <div class="text">
        以一体化智能化公共数据平台为支撑，基于区级视频平台、市级GIS平台，加强物联感知系统和城市运行生命体征指标体系建设，整合开发区城市治理各领域的事件信息、业务系统和应用场景，贯通基层智治系统，建全区“感、防、控、治”大闭环“一网统管”新格局，实现“一图感知、一屏全观、一体联动”，提升城市治理效能。
      </div>
    </div>
    <iframe
      id="mainIframe"
      style="margin-left: 440px"
      width="400px"
      height="350px"
      frameborder="0"
      scrolling="0"
      :src="scanUrl"
      sandbox="allow-scripts allow-top-navigation allow-same-origin"
    ></iframe>
  </div>
</template>

<script>
import { login } from '@/api/common/index'
export default {
  name: 'index',
  components: {},
  data() {
    return {
      scanUrl: `${process.env.VUE_APP_SAOMA_CODE}/oauth2/auth.htm?response_type=code&client_id=${process.env.VUE_APP_CLIENT_ID}&redirect_uri=${process.env.VUE_APP_REDIRECT_URI}&scope=get_user_info&authType=QRCODE&embedMode=true`,
    }
  },
  mounted() {
    window.addEventListener('message', this.loginZZD)
  },
  computed: {},
  methods: {
    // 浙政钉登录
    loginZZD(event) {
      const code = event.data && event.data.code
      if (code) {
        console.log('code1111', code)
        login(code).then((res) => {
          console.log('接口数据', res)
          if (res.code == '200') {
            localStorage.setItem('Admin-Token', res.token)
            this.$router.push('/home')
          }else{
            this.$message.error(res.msg);
          }
        })
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('message', this.loginZZD)
  },
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('@/assets/img/login_bg.png') 0 0 no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-content: center;
  align-items: center;
  .title_box {
    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 80px;
      color: #1c83f9;
      line-height: 116px;
    }
    .text {
      width: 1264px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #667085;
      line-height: 60px;
      margin-top: 64px;
    }
  }
}
</style>
