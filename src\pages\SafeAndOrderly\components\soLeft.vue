<template>
  <div class="SafeAndOrderlyLeft">
    <ktitle :titleText="'安全有序'"></ktitle>
    <population class="population"></population>
    <ktitle :titleText="'卫生医疗'"></ktitle>
    <healthCare class="healthCare"></healthCare>
  </div>
</template>

<script>
import ktitle from "@/components/title.vue";
import population from "./leftComponents/population";
import healthCare from "@/pages/SafeAndOrderly/components/leftComponents/healthCare";
export default {
  name: "soLeft",
  components:{
    ktitle,
    population,
    healthCare
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .SafeAndOrderlyLeft {
    margin: 80px 68px 0 68px;
    overflow: hidden;
    .population {
      height: 1025px
    }
    .healthCare {
      height: 525px
    }
  }
</style>