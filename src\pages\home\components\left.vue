<template>
  <div class="leftContainer">
    <ktitle :titleText="'指标中心'" @titleClick="titleClick('/IndicatorCenter')"></ktitle>
    <div class="container1">
      <div class="item" v-for="(item,i) in indexCenter" :key="i">
        <titleTwo :title-text="item.name"></titleTwo>
        <div class="table">
          <div class="tableLine1">
            <div class="tableTab" v-for="(item2,j) in item.info" :key="j">{{item2.name}}</div>
          </div>
          <div class="tableLine2">
            <div class="tableTab" v-for="(item3,k) in item.info" :key="k">{{item3.num + item3.unit}}</div>
          </div>
        </div>
      </div>
    </div>
    <ktitle :titleText="'事件中心'" @titleClick="titleClick('/EventTaskCenter')" style="margin-top: 95px"></ktitle>
    <div class="container2">
      <div class="event">
        <titleTwo :title-text="'事件趋势分析'"></titleTwo>
        <charts1></charts1>
      </div>
      <div class="event">
        <titleTwo :title-text="'事件组成'"></titleTwo>
        <charts2></charts2>
      </div>
      <div class="event2">
        <titleTwo :title-text="'事件处置情况'"></titleTwo>
        <charts3></charts3>
      </div>
      <div class="event2">
        <titleTwo :title-text="'今日事件'"></titleTwo>
        <div class="table">
          <div class="tableLine title">
            <div class="tableTab" v-for="(item,i) in tableTitle" :key="i" :class="{indexNum:i == 0}" style="color: #77B3F1">{{item}}</div>
          </div>
          <div class="tableContainer">
            <div class="tableLine" v-for="(item,i) in tableContainer" :key="i">
              <div class="tableTab indexNum numContainer">{{i + 1}}</div>
              <div class="tableTab" :title="item.value1">{{item.value1}}</div>
              <div class="tableTab" :title="item.value2">{{item.value2}}</div>
              <div class="tableTab" :title="item.value3">{{item.value3}}</div>
              <div class="tableTab" :title="item.value4">{{item.value4}}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import titleTwo from "@/components/titleTwo";
import charts1 from "@/pages/home/<USER>/leftCharts/charts1"
import charts2 from "@/pages/home/<USER>/leftCharts/charts2"
import charts3 from "@/pages/home/<USER>/leftCharts/charts3"
import {getEventCurrentDate} from "@/api/EventTask";
export default {
  name: "left",
  data() {
    return {
      indexCenter:[
        {
          name:"党建统领",
          info:[
            {
              name:"党员总数",
              num:19074,
              unit:"人"
            },
            {
              name:"党组织总数",
              num:981,
              unit:"个"
            },
            {
              name:"本年发展党员数",
              num:134,
              unit:"人"
            }
          ]
        },
        {
          name:"经济生态",
          info:[
            {
              name:"地区生产总值",
              num:284.5,
              unit:"亿元"
            },
            {
              name:"人均可支配收入",
              num:48876,
              unit:"元"
            },
            {
              name:"市场主体总量",
              num:59444,
              unit:"户"
            }
          ]
        },
        {
          name:"安全有序",
          info:[
            {
              name:"全区常驻人口",
              num:48.7,
              unit:"万人"
            },
            {
              name:"重大危险源数量",
              num:4,
              unit:"个"
            },
            {
              name:"本年刑事案件数量",
              num:1693,
              unit:"件"
            }
          ]
        },
        {
          name:"公共服务",
          info:[
            {
              name:"小学数量",
              num:15,
              unit:"所"
            },
            {
              name:"中学数量",
              num:15,
              unit:"所"
            },
            {
              name:"大学数量",
              num:1,
              unit:"所"
            }
          ]
        }
      ],
      tableTitle:['序号','事件类型','事件来源','事件内容','事件阶段'],
      tableContainer:[
        {
          value1:"公共安全",
          value2:"居民举报",
          value3:"工厂排放有害废气导致周边空气质量下降",
          value4:"发现",
        },
        {
          value1:"城市治理",
          value2:"居民反映",
          value3:"江南小区夜间施工噪音扰民问题",
          value4:"处置",
        },
        {
          value1:"环境监测",
          value2:"自动监测系统",
          value3:"公园内喷泉水泵损坏停止工作",
          value4:"发现",
        },
        {
          value1:"市政设施",
          value2:"巡查人员",
          value3:"商业街区存在乱摆卖现象影响市容",
          value4:"发现",
        },
        {
          value1:"交通管理",
          value2:"交通监控系统",
          value3:"主干道因施工未设置临时交通指示导致严重拥堵",
          value4:"结案",
        },
        {
          value1:"应急响应",
          value2:"企业报告",
          value3:"一家化工厂发生小规模化学品泄漏",
          value4:"发现",
        },
        {
          value1:"市场监管",
          value2:"卫生部门",
          value3:"公共厕所因清洁不及时出现卫生问题",
          value4:"分派",
        },
        {
          value1:"政务服务",
          value2:"项目负责单位",
          value3:"新修路段出现沉降需要紧急修复",
          value4:"处置",
        },
        {
          value1:"城市规划与发展",
          value2:"园林部门",
          value3:"开发区多个绿化带出现枯死植物需更换",
          value4:"发现",
        },
        {
          value1:"市民投诉与建议",
          value2:"警方通报",
          value3:"社区发生小规模治安纠纷需警方介入",
          value4:"处置",
        }
      ]
    }
  },
  components:{
    ktitle,
    titleTwo,
    charts1,
    charts2,
    charts3
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getEventCurrentDate().then(res => {
        this.tableContainer = res.data.data.map(item => ({
          value1:item.eventType,
          value2:item.eventSource,
          value3:item.content,
          value4:item.stage,
        }))
      })
    },
    titleClick(val) {
      this.$router.push(val)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
::-webkit-scrollbar {
  width: 0;
}
  .leftContainer {
    margin: 80px 0 0 68px;
    .container1 {
      width: 1934px;
      height: 510px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
      .item {
        width: 941px;
        height: 224px;
        .table {
          .tableLine1 {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            width: 941px;
            height: 60px;
            background: #00396F;
            margin-top: 37px;
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #D6E7F9;
          }
          .tableLine2 {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            width: 941px;
            height: 62px;
            background: #0F2B4D;
            margin-top: 4px;
          }
          .tableTab {
            width: calc(941px/3);
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            text-align: center;
            background: linear-gradient(180deg, #FFFFFF 0%, #00C2FF 50.244140625%, #FFFFFF 53.0029296875%, #CCF4FF 99.365234375%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
    .container2 {
      width: 1934px;
      height: 964px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
      .event {
        width: 941px;
        height: 480px;
        position: relative;
      }
      .event2 {
        width: 941px;
        height: 462px;
        .table {
          width: 936px;
          height: 315px;
          margin-top: 49px;
          .tableContainer {
            height: 308px;
            overflow-y: scroll;
          }
          .tableLine {
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            width: 936px;
            height: 63px;
            background: #0F2B4D;
            margin-bottom: 14px;
            .tableTab {
              text-align: left;
              width: 187.2px;
              font-size: 34px;
              font-family: Source Han Sans SC;
              font-weight: 400;
              color: #ffffff;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .indexNum {
              width: 120px;
            }
            .numContainer {
              font-size: 34px;
              font-family: Source Han Sans SC;
              font-weight: 400;
              color: #D6E7F9;
              background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .title {
            background: #035B86;
            color: #77B3F1;
          }
        }
      }
    }
  }
</style>