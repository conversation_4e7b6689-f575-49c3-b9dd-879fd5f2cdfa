<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts3">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="charts13" id="charts13"></div>
  </div>
</template>

<script>
import {getTaskProgress} from "@/api/EventTask";
export default {
  name: 'charts3',
  data() {
    return {
      charts2Data: [
        {
          name: '2:00',
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
        },
        {
          name: '4:00',
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
        },
        {
          name: '6:00',
          value1: 0,
          value2: 0,
          value3: 0,
          value4: 0,
        },
        {
          name: '8:00',
          value1: 1,
          value2: 2,
          value3: 3,
          value4: 1,
        },
        {
          name: '10:00',
          value1: 2,
          value2: 1,
          value3: 2,
          value4: 2,
        },
        {
          name: '12:00',
          value1: 3,
          value2: 3,
          value3: 1,
          value4: 1,
        },
        {
          name: '14:00',
          value1: 4,
          value2: 5,
          value3: 3,
          value4: 0,
        },
        {
          name: '16:00',
          value1: 5,
          value2: 4,
          value3: 4,
          value4: 1,
        },
        {
          name: '18:00',
          value1: 4,
          value2: 3,
          value3: 2,
          value4: 2,
        },
        {
          name: '20:00',
          value1: 4,
          value2: 2,
          value3: 1,
          value4: 1,
        },
        {
          name: '22:00',
          value1: 4,
          value2: 1,
          value3: 2,
          value4: 1,
        },
      ],
      value:"日",
      options:[
        {label:"日",value:"日"},
        {label:"月",value:"月"},
        {label:"年",value:"年"},
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts3()
  },
  methods: {
    initCharts3() {
      const that = this;
      getTaskProgress({type: this.value}).then(res => {
        //分拨 认领 处理 完成
        this.charts2Data = res.data.data.map(item => ({
          name:item.label,
          value1:item.dfb,
          value2:item.drl,
          value3:item.dcl,
          value4:item.ywc,
        }))
        let myChart = this.$echarts.init(document.getElementById('charts13'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          legend: {
            orient: 'horizontal',
            // icon: "circle",
            icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
            itemWidth: 14, // 设置宽度
            itemHeight: 14, // 设置高度
            itemGap: 68, // 设置间距
            textStyle: {
              color: '#D6E7F9',
              fontSize: 18,
            },
            left: '28%',
            top: '13%',
          },
          grid: {
            left: '8%',
            right: '6%',
            top: '30%',
            bottom: '1%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: this.charts2Data.map((item) => item.name),
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: that.value == "月"?40:0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: '单位:件',
              type: 'value',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [10, -10, 50, 10],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '待分拨',
              type: 'line',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#00C0FF',
                },
              },
              data: this.charts2Data.map((item) => item.value1),
            },
            {
              name: '待认领',
              type: 'line',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#22E8E8',
                },
              },
              data: this.charts2Data.map((item) => item.value2),
            },
            {
              name: '待处理',
              type: 'line',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#FFC460',
                },
              },
              data: this.charts2Data.map((item) => item.value3),
            },
            {
              name: '已完成',
              type: 'line',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#A9DB52',
                },
              },
              data: this.charts2Data.map((item) => item.value4),
            },
          ]
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })

    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  //right: 48px;
  //top: 80px;
  left: 1760px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}
.charts13 {
  width: 931px;
  height: 400px;
  margin-top: 26px;
}
</style>
