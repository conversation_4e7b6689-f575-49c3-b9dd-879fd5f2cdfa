<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts1">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="charts1" id="charts1"></div>
  </div>
</template>

<script>
import {getEventQs} from "@/api/EventTask";

export default {
  name: "charts1",
  data() {
    return {
      charts1Data:[
        {date:"2019",value:19},
        {date:"2020",value:12},
        {date:"2021",value:17},
        {date:"2022",value:9},
        {date:"2023",value:11},
      ],
      value:"月", //事件趋势分析默认
      options:[
        {label:"年度",value:"年"},
        {label:"季度",value:"季"},
        {label:"月度",value:"月"},
      ], //事件下拉列表
    }
  },
  computed: {},
  mounted() {
    this.initCharts1()
  },
  methods: {
    initCharts1() {
      getEventQs({type: this.value}).then(res => {
        this.charts1Data = res.data.data.map(item => ({
          date: item.label,
          value: item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById("charts1"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: "8%",
            right: "6%",
            top: "22%",
            bottom: "1%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts1Data.map(item => item.date),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "事件总量/万件",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: [10,-100,10,10],
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: "事件数",
              type: "line", // 直线ss
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                },
              },
              data: this.charts1Data.map(item => item.value),
            }
          ],
        };
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 80px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}

.charts1 {
  width: 941px;
  height: 380px;
}
</style>