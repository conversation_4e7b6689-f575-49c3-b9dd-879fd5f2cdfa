<template>
  <div class="airAndWater">
    <TabTitle :titleText="['空气质量趋势','断面水质超标个数']" :choose="choose" @leftClick="choose = 0" @rightClick="choose = 1"></TabTitle>
    <air v-show="choose == 0"></air>
    <water v-show="choose == 1"></water>
  </div>
</template>

<script>
import TabTitle from "@/components/TabTitle";
import air from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/airAndWaterComponents/air";
import water from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/airAndWaterComponents/water";
export default {
  name: "airAndWater",
  data() {
    return {
      choose:0
    }
  },
  components:{
    TabTitle,
    air,
    water
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>
.airAndWater {
  margin-top: 75px;
}
</style>