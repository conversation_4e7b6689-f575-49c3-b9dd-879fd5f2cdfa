<template>
  <div class="centerContainer">
    <Ktitle :titleText="'政务服务'" :width="'3210'" />
    <div class="mainContainer">
      <div class="partOne">
        <zdpyc />
      </div>
      <div class="partTwo">
        <TitleTwo :title-text="'消费者投诉'" :width="'3210'" />
        <div class="partTwoContent">
          <div class="cc content_l">
            <div class="select">
              <el-select v-model="valueO" placeholder="请选择">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </div>
            <SlbjCharts />
          </div>
          <div class="cc content_r">
            <!-- <BjlChart /> -->
            <div class="nybjl">
              <div class="chart">
                <div class="info">
                  <div class="value gold">99.93%</div>
                  <div class="name">农业投诉举报办结率</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="partThree">
        <div class="cc content_l">
          <div class="selectwo">
            <el-select v-model="valueT" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <CjfdjChart />
        </div>
        <div class="cc content_r">
          <XfztsPie3D :id="'xfztsPieCharts'" :options="chartsData" :legendCustom="legend" :internalDiameterRatio="1.8"
            :scaleCustom="1.4" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Ktitle from "@/components/title.vue"
import TitleTwo from "@/components/titleTwo.vue"
import SlbjCharts from "@/pages/PublicService/components/chartsComponents/SlbjCharts.vue"
import CjfdjChart from "@/pages/PublicService/components/chartsComponents/CjfdjChart.vue"
import XfztsPie3D from "@/pages/PublicService/components/chartsComponents/XfztsPie3D.vue"
import BjlChart from "@/pages/PublicService/components/chartsComponents/BjlChart.vue"
import zdpyc from "@/pages/PublicService/components/centerComponents/zdpyc";

export default {
  components: {
    Ktitle,
    SlbjCharts,
    CjfdjChart,
    XfztsPie3D,
    BjlChart,
    TitleTwo,
    zdpyc
  },
  data () {
    return {
      valueO: 3,
      valueT: 1,
      options: [
        { label: "年度", value: 1 },
        { label: "季度", value: 2 },
        { label: "月度", value: 3 },
      ],
      chartsData: [
        {
          name: "本年线上消费者投诉举报量",
          value: 59
        },
        {
          name: "本年线下消费者投诉举报量",
          value: 127
        }
      ],
      legend: {
        itemWidth: 30,
        itemHeight: 15,
        itemGap: 20,
        left: '50%',
        top: '40%',
      }
    }
  }
}
</script>
<style scoped lang="less">
.centerContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mainContainer {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr 1fr;

    .partOne {
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;
      padding-top: 60px;
    }

    .partTwo {
      display: flex;
      flex-direction: column;
      align-items: center;
      box-sizing: border-box;

      .partTwoContent {
        flex: 1;
        width: 100%;
        display: grid;
        grid-template-columns: 4fr 3fr;
        grid-template-rows: 1fr;

        // background-color: beige;

      }
    }

    .partThree {
      display: grid;
      grid-template-columns: 4fr 3fr;
      grid-template-rows: 1fr;
      box-sizing: border-box;
      // background-color: beige;
    }
  }
}

.cc {
  width: 100%;
  height: 100%;
}

.content_l {
  // background-color: aqua;
  position: relative;
}

.content_r {
  // background-color: aqua;

}

/deep/ .select {
  position: absolute;
  right: 96px;
  top: 45px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}

/deep/ .selectwo {
  position: absolute;
  right: 96px;
  top: 115px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}

.nybjl {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .chart {
    // width: 498px;
    // height: 498px;
    height: 100%;
    aspect-ratio: 1 / 1;
    background: url("@/assets/safe/chart.png");
    background-size: cover;
    margin: 60px 0 0 220px;
    overflow: hidden;

    .info {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      // background-color: antiquewhite;
      margin-top: 142px;

      .name {
        // width: 252px;
        height: 48px;
        font-size: 51px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        white-space: nowrap;
      }

      .value {
        font-size: 105px;
        font-family: BN;
        font-weight: 400;
      }
    }
  }
}
</style>