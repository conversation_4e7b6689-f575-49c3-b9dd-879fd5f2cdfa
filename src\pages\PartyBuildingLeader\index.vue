<template>
  <div class="container">
    <div class="filterContainer">
      <TimeFilter ref="timeFilter" v-model="parentVal" />
    </div>
    <div class="innerLeft">
      <wrapbox bg="left">
        <pblLeft :filterTime="parentVal" class="animate__animated animate__fadeInLeft"></pblLeft>
      </wrapbox>
    </div>
    <div class="innerCenter animate__animated animate__fadeIn">
      <centerWrapBox>
        <pbCenter></pbCenter>
      </centerWrapBox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <pblRight class="animate__animated animate__fadeInRight"></pblRight>
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import centerWrapBox from "@/components/mapComponent/centerWrapBox"
import pblLeft from "@/pages/PartyBuildingLeader/components/pblLeft"
import pblRight from "@/pages/PartyBuildingLeader/components/pblRight"
import pbCenter from "@/pages/PartyBuildingLeader/components/pbCenter"
import TimeFilter from "@/components/TimeFilter.vue"
export default {
  name: "index",
  data () {
    return {
      monthTime: '',
      parentVal: ''
    }
  },
  components: {
    pblLeft,
    pblRight,
    wrapbox,
    centerWrapBox,
    pbCenter,
    TimeFilter
  },
  computed: {},
  mounted () {

  },

  // watch: {
  //   parentVal: {
  //     handler (newVal, oldVal) {
  //       console.log('timefileter绑定值发生了变化', newVal, oldVal)
  //     }
  //   }
  // }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  // height: 100%;
  height: calc(100% - 230px);
  z-index: 999;
  background: url("@/assets/home/<USER>");
  background-size: cover;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 230px;


  .filterContainer {
    position: absolute;
    top: -3.3%;
    right: 1.5%;
  }

  .innerLeft {
    // margin-top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }

  .innerCenter {
    // margin-top: 229px;
    z-index: 2;
  }

  .innerRight {
    // margin-top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
}
</style>