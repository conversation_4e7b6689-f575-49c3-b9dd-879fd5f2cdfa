<!--预测预警-->
<template>
  <div>
    <titleTwo :title-text="'预测预警'"></titleTwo>
    <div class="box">
      <div class="item" v-for="(item,i) in indexs" :key="i">
        <div class="info">
          <div class="value gold">{{item.value}}</div>
          <div class="name" :class="{name1:i == 0,name2:i == 1}">{{item.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "warning",
  data() {
    return {
      indexs:[
        {
          name:"消防预警 设施数",
          value:150
        },
        {
          name:"链接远程监控 单位数",
          value:158
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.box {
  width: 935px;
  height: 460px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  .item {
    width: 292px;
    height: 340px;
    background: url("@/assets/safe/预警.png");
    background-size: cover;
    .info {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      .name {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        word-break: break-all;
        text-align: center;
        margin-top: 98px;
      }
      .name1 {
        width: 152px;
      }
      .name2 {
        width: 234px;
      }
      .value {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        margin-top: 45px;
      }
    }
  }
}
</style>