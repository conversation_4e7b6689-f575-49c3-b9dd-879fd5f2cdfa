<template>
  <div class="container">
      <div class="date">
        <div class="date-item" v-for="(item,i) in dates" :key="i" @click="dateChoice = i">
          <div class="point" :class="{pointactive:i == dateChoice}"></div>
          <div class="value" :class="{active:i == dateChoice}">{{item}}</div>
        </div>
      </div>
      <div class="box">
        <div class="box-left">
          <div class="item" v-for="(item,i) in eventData" :key="i">
            <div class="icon" :style="{backgroundImage:'url('+item.icon+')'}"></div>
            <div class="name">{{item.name}}</div>
            <div class="value">{{item.value}} <span class="unit">件</span> </div>
          </div>
        </div>
        <div class="box-right">
          <div class="tabs">
            <div class="tab" v-for="(item,i) in ['事件类型排行','高发网格排行']" :key="i" :class="{tabActive:i == tabChoose}" @click="tabChoose = i">{{item}}</div>
          </div>
          <div class="charts" id="eventCharts1"></div>
        </div>
      </div>
  </div>
</template>

<script>
export default {
  name: "KeyEvents",
  data() {
    return {
      dateChoice:0, //日期默认选中
      dates:['2022.3','2022.4','2022.5','2022.6','2022.7','2022.8','2022.9'], //date Tab
      eventData:[
        {
          name:"高频事件",
          value:123,
          icon:require("@/assets/home/<USER>/event1.png")
        },
        {
          name:"飙升事件",
          value:134,
          icon:require("@/assets/home/<USER>/event2.png")
        },
        {
          name:"多人同诉",
          value:132,
          icon:require("@/assets/home/<USER>/event3.png")
        }
      ],
      tabChoose:0, //重点事件tab默认选中
      arr:[
        {
          name:"火灾扑救",
          value:80
        },
        {
          name:"抢险救援",
          value:70
        },
        {
          name:"公务执勤",
          value:60
        },
        {
          name:"社会救助",
          value:50
        },
        {
          name:"其他出动",
          value:40
        },
        {
          name:"抢险救援",
          value:30
        },
        {
          name:"公务执勤",
          value:20
        },
        {
          name:"社会救助",
          value:10
        },
        {
          name:"其他出动",
          value:8
        }
      ], //重点事件图表数据
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById("eventCharts1"));
      const that = this
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
          formatter: '{b}: <br/> {c}次',
        },
        legend: {
          selectedMode: false,
          data: ["人"],
          right: "4%",
          textStyle: {
            fontSize: 16,
            color: "#fff",
          },
        },
        grid: {
          top: "0",
          left: 132,
          right: "15%",
          bottom: "0",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisLabel: {
            inside: true,
            textStyle: {
              color: "#fff",
              // verticalAlign: "bottom",
              align: "left",
              padding: [0, 0, 0, -140],
              fontSize:32
            },
          },
          axisTick: {
            show: false,
            length: 10,
          },
          data: that.arr.map(item => item.name),
        },
        series: [{
          // cursor:"auto",
          type: "bar",
          name: "",
          showBackground: true,
          backgroundStyle: {
            color: '#00526C'
          },
          itemStyle: {
            barBorderRadius: [0, 0, 0, 0],
            normal:{
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                {
                  offset: 0,
                  color: "#00D1FF",
                },
                {
                  offset: 1,
                  color: "rgba(0,209,255,0)",
                },
              ]),
            }
          },
          label: {
            show: true,
            position: [450, -5],
            color: "#fff",
            formatter: function (params) {
              return params.value + '次';
            },
            fontSize: 32,
            textStyle:{
              color:'#FFF',
              fontWeight:400,
              fontFamily:'Source Han Sans CN-Regular, Source Han Sans CN'
            }
          },
          barWidth: 20,
          color: "#539FF7",
          data: that.arr.map(item => item.value),
        },{
          name: "max",
          type: "bar",
          barWidth: 20,
          barGap: "-100%",
          data: that.arr.map(item => 100), //柱图背景长度值
          itemStyle: {
            normal: {
              color: "#313A51",
              borderWidth: 1,
              borderColor:'#085877'
            },
          },
          z: 0,
        }]
      }

      myChart.setOption(option)
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
.unit {
  font-size: 40px;
  margin: -5px 0 0 -10px;
}
  .container {
    margin-top: 22px;
    .date {
      width: 938px;
      height: 3px;
      background: rgba(53, 101, 150, 0.3);
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .date-item {
        cursor: pointer;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-direction: column;
        margin-top: 40px;
        .point {
          width: 16px;
          height: 16px;
          background: #7F93A8;
          border-radius: 50%;
        }
        .value {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #7F93A8;
        }
        .pointactive {
          width: 35px;
          height: 35px;
          background: url("@/assets/home/<USER>/datePoint.png");
          background-size: cover;
        }
        .active {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #D6E7F9;
          text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
          background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          position: relative;
          bottom: 9px;
        }
      }
    }
    .box {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .box-left {
        margin: 60px 0 0 27px;
        .item {
          width: 153px;
          text-align: center;
          margin-bottom: 42px;
          .icon {
            width: 153px;
            height: 122px;
            background-size: cover;
          }
          .name {
            font-size: 34px;
            font-family: Adobe Heiti Std;
            font-weight: normal;
            color: #D6E7F9;
          }
          .value {
            font-size: 86px;
            font-family: BN;
            font-weight: 400;
            color: #9AA9BF;
            background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      .box-right {
        margin: 0 0 0 68px;
        .tabs {
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          margin-top: 50px;
          .tab {
            width: 253.9px;
            height: 67.2px;
            background-size: cover;
            background: url("@/assets/home/<USER>");
            font-size: 32px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #D6E7F9;
            line-height: 67.2px;
            text-align: center;
            cursor: pointer;
          }
          .tabActive {
            background: url("@/assets/home/<USER>");
          }
        }
        .charts {
          width: 678px;
          height: 754px;
          margin-top: 30px;
        }
      }
    }
  }
</style>