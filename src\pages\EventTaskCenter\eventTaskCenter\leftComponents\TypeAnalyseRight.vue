<template>
  <div>
    <table-component :thConfig="thConfig" :tableData="tableData" @infoClick="infoClick" />
    <!-- 事件详情弹框 -->
    <CommonDialog :dialog-flag="showFlag" title="事件详情" :dialogWidth="'1300px'" @close="showFlag = false">
      <EventDetails :showFlag="showFlag" :id="id" v-if="showFlag"/>
    </CommonDialog>
  </div>
</template>

<script>
import TableComponent from '@/components/TableComponent'
import CommonDialog from '@/components/Commondialog/index.vue' //弹框
import EventDetails from '@/components/EventDetails' //事件详情
import {getEventCurrentDate} from "@/api/EventTask"; //今日事件
export default {
  data() {
    return {
      tableData: [
        {
          id: '1',
          type: '公共安全',
          desc: '市中心发现可疑包裹',
          time: '2024-4-29 11:12:11',
          jd: '发现',
          color: '#5BB4FF',
          jdindex: 0,
          sjly: '市民报告',
          sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
          sjbh: '2024-EVT-0456',
          sjsj: '2024年5月1日 10:00',
          sjdd: '双溪西路',
          sjlx: '公共安全',
          xm: '陈武兴',
          lxdh: '15021781092',
          cldw: '金华市综合行政执法局金华经济技术开发区分局',
          jasj: '暂无',
          jzxx: '暂无',
          tpxx: '暂无',
        },
        {
          id: '2',
          type: '城市治理',
          desc: '居民区非法摊贩扰民问题',
          time: '2024-4-30 12:14:23',
          jd: '处置',
          color: '#5BB4FF',
          jdindex: 2,
          sjly: '市民报告',
          sjnr: '2024年5月5日晚上19点，位于全村小区南门入口处，居民李婉婷女士报告长期有非法摊贩占道经营，噪音扰民，且阻碍交通，影响了居民的正常生活秩序。',
          sjbh: '2024-EVT-0457',
          sjsj: '2024年5月5日 19:00',
          sjdd: '全村小区南门入口',
          sjlx: '城市治理',
          xm: '李婉婷',
          lxdh: '13812345678',
          cldw: '金华市城市管理行政执法局',
          jasj: '暂无',
          jzxx: '暂无',
          tpxx: '暂无',
        },
        {
          id: '3',
          type: '城市治理',
          desc: '更新户籍转移处理流程',
          time: '2024-4-30 15:12:34',
          jd: '处置',
          color: '#5BB4FF',
          jdindex: 2,
          sjly: '政府公告',
          sjnr: '2024年6月1日，市政府办公厅发布了关于更新户籍转移处理流程的通知，旨在简化手续，提升服务效率，更好地满足市民需求。',
          sjbh: '2024-EVT-0458',
          sjsj: '2024年5月3日',
          sjdd: '全市范围',
          sjlx: '城市治理',
          xm: '无',
          lxdh: '13812345678',
          cldw: '市政府办公厅协同公安局、民政局、人力资源和社会保障局等相关部门',
          jasj: '暂无',
          jzxx: '暂无',
          tpxx: '暂无',
        },
        {
          id: '4',
          type: '环境监测',
          desc: '工业区排放废气超标',
          time: '2024-4-30 17:22:45',
          jd: '结案',
          color: '#5BB4FF',
          jdindex: 3,
          sjly: '市民举报',
          sjnr: '2024年5月10日上午8点，位于东郊工业园区内的化工厂，市民张先生举报该工厂夜间排放大量刺鼻废气，怀疑严重超标，影响了周边居民的健康和生活质量。',
          sjbh: '2024-EVT-0459',
          sjsj: '2024年5月10日 8:00',
          sjdd: '南二环西路',
          sjlx: '环境监测',
          xm: '张先生',
          lxdh: '13912345679',
          cldw: '金华市生态环境局',
          jasj: '2024年5月15日',
          jzxx: '经过环境监测站的专业检测，证实华美化工厂排放的废气确实超过国家规定的排放标准。生态环境局依法对该厂下达了整改通知书，要求其在限期内完成设备升级，确保排放达标，并处以相应罚款。同时，环保部门将持续监督其整改进程，确保整改措施得到有效落实，保护周边居民的环境权益。',
          tpxx: '暂无',
        },
        {
          id: '5',
          type: '城市治理',
          desc: '市区主要道路井盖损坏',
          time: '2024-5-01 9:30:12',
          jd: '分派',
          color: '#5BB4FF',
          jdindex: 1,
          sjly: '市民报告',
          sjnr: '2024年5月15日上午7点，市民王先生在上班途中发现位于双溪西路交叉口处，一个井盖损坏严重，存在极大的安全隐患，对过往车辆和行人造成威胁。',
          sjbh: '2024-EVT-0460',
          sjsj: '2024年5月15日 7:00',
          sjdd: '双溪西路交叉口',
          sjlx: '市政设施',
          xm: '王先生',
          lxdh: '13700001234',
          cldw: '金华市城乡建设局',
          jasj: '2024年5月15日 10:00',
          jzxx: '暂无',
          tpxx: '暂无',
        },
        {
          id: '6',
          type: '交通管理',
          desc: '多辆车在高速公路追尾',
          time: '2024-5-01 10:20:12',
          jd: '结案',
          color: '#5BB4FF',
          jdindex: 3,
          sjly: '市民报警',
          sjnr: '2024年5月2日上午8点，多位驾驶者报警称，在近10公里路段内发生了多起车辆追尾事故，涉及车辆超过10辆，现场交通严重受阻，且有人员受伤。',
          sjbh: '2024-EVT-0461',
          sjsj: '2024年5月2日 8:00',
          sjdd: '海棠东路',
          sjlx: '交通管理',
          xm: '张强',
          lxdh: '13900012345',
          cldw: '金华市公安局交警支队',
          jasj: '2024年5月2日 11:00',
          jzxx: '经过交警、消防及急救部门的紧密合作，高速公路上的连环追尾事故现场得到了有效控制和处理。所有伤员均被及时送往医院接受治疗，无生命危险。事故车辆被迅速拖离，交通管理部门部署了临时交通管制措施，确保了后续车辆的安全通行。事故原因正在进一步调查中，相关部门提醒驾驶员雨天行车要保持安全距离，谨慎驾驶。',
          tpxx: '暂无',
        },
        {
          id: '7',
          type: '市政设施',
          desc: '三江街道供水中断',
          time: '2024-5-01 14:22:17',
          jd: '处置',
          color: '#5BB4FF',
          jdindex: 2,
          sjly: '市民报告',
          sjnr: '2024年5月5日上午7点，三江街道多个社区的居民反映家中自来水供应突然中断，严重影响日常生活，需紧急排查并恢复供水。',
          sjbh: '2024-EVT-0462',
          sjsj: '2024年5月25日 7:00',
          sjdd: '三江街道',
          sjlx: '市政设施',
          xm: '赵敏',
          lxdh: '13600009876',
          cldw: '金华市水务集团',
          jasj: '2024年5月5日 13:00',
          jzxx: '针对三江街道的大面积停水事件，金华市水务集团迅速响应，组织专业抢修队伍对受损管道进行了紧急抢修。经过数小时的努力，破裂的水管得以修复，供水服务恢复正常。水务集团表示将加强对城市供水管网的日常巡检，以防类似事件再次发生，并对受影响居民造成的不便表达了歉意。',
          tpxx: '暂无',
        },
      ],
      thConfig: [
        {
          th: '事件类型',
          field: 'type',
          width: '23%',
          hover: false,
        },
        {
          th: '事件描述',
          field: 'desc',
          width: '24%',
          hover: true,
        },
        {
          th: '发生时间',
          field: 'time',
          width: '28%',
          hover: true,
        },
        {
          th: '事件阶段',
          field: 'jd',
          width: '25%',
          hover: false,
        },
      ],
      //事件类型弹框
      showFlag: false,
      id: "",
    }
  },
  components: {
    TableComponent,
    CommonDialog,
    EventDetails,
  },
  mounted() {
    this.getList()
  },
  methods: {
    infoClick(item) {
      this.id = item.id?item.id:""
      this.showFlag = true
    },
    getList() {
      getEventCurrentDate().then(res => {
        this.tableData = res.data.data.map(item => ({
          id: item.id,
          type:item.eventType,
          desc:item.content,
          time:item.createdate,
          jd:item.stage
        }))
      })
    },
  },
}
</script>

<style lang="less" scoped>
// .table {
//   .table-header {
//     display: flex;
//     width: 1049px;
//     height: 62px;
//     background: url('@/assets/eventTaskCenter/table-header-bg.png') 0px 30px no-repeat;
//     background-size: 100% 100%;
//     .th {
//       width: 25%;
//       color: #00f0ff;
//       font-size: 32px;
//       text-align: center;
//       box-sizing: border-box;
//       padding: 0 24px;
//     }
//   }
//   .table-body {
//     overflow-y: auto;
//     height: 375px;
//     .tr {
//       display: flex;
//       width: 1049px;
//       height: 62px;
//       .td {
//         width: 25%;
//         box-sizing: border-box;
//         height: 68px;
//         line-height: 68px;
//         padding: 0 24px;
//         text-align: center;
//         font-size: 28px;
//         color: #fff;
//         overflow: hidden;
//         text-overflow: ellipsis;
//         white-space: nowrap;
//       }
//     }
//     .tr-bg {
//       background: rgba(91, 180, 255, 0.2);
//     }
//   }
// }
</style>
