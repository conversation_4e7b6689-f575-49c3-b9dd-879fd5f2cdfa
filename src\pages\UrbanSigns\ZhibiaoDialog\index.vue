<template>
  <div class="dialogContainer">
    <div class="dialogContainer-title">{{detailData.name}}指数: {{detailData.number}}</div>
    <div class="dialogContainer-inner" :style="{width: width +'px'}">
      <div class="dialogContainer-inner-left">
        <div class="dialogContainer-inner-item" v-for="(item,i) in keyDesc" :key="i" :class="{isHalf:item.isHalf}" :style="{width: item.isHalf?width/2 + 'px':width + 'px'}">
          <div class="key">{{item.label}}</div>
          <div class="value" :title="detailData[item.key]">{{detailData[item.key]}}</div>
        </div>
      </div>
      <div class="dialogContainer-inner-right">
        <div class="chartsZhibiao" id="chartsZhibiao"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "index",
  props: {
    detailData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    keyDesc: {
      type: Array,
      default: () => {
        return []
      }
    },
    width: {
      type: Number,
      default: () => {
        return 900
      }
    }
  },
  data() {
    return {
      charts1Data:[]
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
        this.charts1Data = this.detailData.chartList.map(item => ({
          date:item.name,
          value:item.value
        }))
        let myChart = this.$echarts.init(document.getElementById("chartsZhibiao"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: "8%",
            right: "6%",
            top: "10%",
            bottom: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts1Data.map(item => item.date),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "单位/个",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: [10,-100,10,10],
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: "指标值",
              type: "line", // 直线ss
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                },
              },
              data: this.charts1Data.map(item => item.value),
            }
          ],
        };
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .dialogContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .dialogContainer-title {
      font-size: 40px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #FFFFFF;
      background: -webkit-gradient(linear, left bottom, left top, from(#CAFFFF), color-stop(0%, #CAFFFF), color-stop(0%, #FFFFFF), to(#00C0FF));
      background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin: 70px 0 40px 0;
    }
    .dialogContainer-inner {
      margin: 32px;
      display: flex;
      justify-content: space-evenly;
      align-items: flex-start;
      .dialogContainer-inner-left {
        flex: 1;
        max-height: 500px;
        overflow-y: scroll;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        .dialogContainer-inner-item {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          margin-bottom: 16px;
          .key {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 32px;
            color: #CEF2FF;
            line-height: 40px;
            text-align: left;
            font-style: normal;
            white-space: nowrap;
          }
          .value {
            font-family: Source Han Sans, Source Han Sans;
            font-weight: 400;
            font-size: 32px;
            color: #FFFFFF;
            line-height: 40px;
            text-align: left;
            font-style: normal;
            margin-left: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-right: 10px;
          }
        }
        .isHalf {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .dialogContainer-inner-right {
        flex: 1;
        .chartsZhibiao {
          width: 100%;
          height: 500px;
        }
      }
    }
  }
</style>