<!--预警部门统计TOP5-->
<template>
  <div>
    <titleTwo :titleText="'预警部门统计TOP5'"></titleTwo>
    <div class="warningCharts2" id="warningCharts2"></div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import {getWarningTop} from "@/api/EventWarning";
export default {
  name: "top",
  data() {
    return {
      chartsData:[
        {
          name:"综合行政执法分局",
          value:80
        },
        {
          name:"江南公安分局",
          value:70
        },
        {
          name:"消防救援大队",
          value:60
        },
        {
          name:"经济发展局",
          value:50
        },
        {
          name:"社会发展局",
          value:40
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      getWarningTop().then(res => {
        this.chartsData = res.data.data.map(item => ({
          name:item.label,
          value:item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById("warningCharts2"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
            formatter: '{b}: <br/> {c}条',
          },
          legend: {
            selectedMode: false,
            data: ["人"],
            right: "4%",
            textStyle: {
              fontSize: 28,
              color: "#fff",
            },
          },
          grid: {
            top: "15%",
            left: "5%",
            right: "4%",
            bottom: "0",
            containLabel: true,
          },
          xAxis: {
            type: "value",
            show: false,
          },
          yAxis: {
            name: "",
            type: "category",
            triggerEvent: false,
            inverse: true,
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              inside: true,
              textStyle: {
                color: "#D6E7F9",
                verticalAlign: "bottom",
                align: "left",
                padding: [0, 0, 9, 0],
                fontSize:32
              },
              formatter: function (value, index) {
                // 处理name文本超出宽度时的隐藏显示
                let displayName = value;
                if (value.length > 19) {
                  displayName = value.substring(0, 19) + '...';
                }

                if (index == 0) {
                  return '{titleOne|TOP' + (index + 1) + '}' + '  ' + '{value|'+displayName+'}'
                } else if (index == 1) {
                  return '{titleTwo|TOP' + (index + 1) + '}' + '  ' + '{value|'+displayName+'}'
                } else if (index == 2) {
                  return '{titleThree|TOP' + (index + 1) + '}' + '  '  + '{value|'+displayName+'}'
                } else {
                  return '{title|TOP' + (index + 1) + '}' + '  '  + '{value|'+displayName+'}'
                }
              },
              rich: {
                value: {
                  width: 200, // 增加宽度以适应截断后的文本
                  align: 'left',
                  color: '#B5DAFF',
                  fontSize: 32,
                  fontWeight: '500',
                  overflow: 'hidden' // 确保超出部分隐藏
                },
                titleOne: {
                  width: 90,
                  align: 'left',
                  color: '#FF4949',
                  fontSize: 32,
                  fontWeight: '500'
                },
                titleTwo: {
                  width: 90,
                  align: 'left',
                  color: '#DF8F30',
                  fontSize: 32,
                  fontWeight: '500'
                },
                titleThree: {
                  width: 90,
                  align: 'left',
                  color: '#B76FD8',
                  fontSize: 32,
                  fontWeight: '500'
                },
                title: {
                  width: 90,
                  align: 'left',
                  color: '#30B8D5',
                  fontSize: 32,
                  fontWeight: '500'
                }
              }
            },
            axisTick: {
              show: false,
              length: 10,
            },
            data: this.chartsData.map(item => item.name),
          },
          series: [{
            // cursor:"auto",
            type: "bar",
            name: "",
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(206,5,5,0.2)'
            },
            itemStyle: {
              barBorderRadius: [0, 10, 10, 0],
              color: function (params) {
                // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                if (params.dataIndex == 0) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FF9434", //指0%处的颜色
                    },
                      {
                        offset: 1,
                        color: "#F90808", //指100%处的颜色
                      },
                    ],
                    false
                  );
                } else if (params.dataIndex == 1) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FFF220", //指0%处的颜色
                    },
                      {
                        offset: 1,
                        color: "#F98508", //指100%处的颜色
                      },
                    ],
                    false
                  );
                } else if (params.dataIndex == 2) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#2DF09F", //指0%处的颜色
                    },
                      {
                        offset: 1,
                        color: "#0EB1E5", //指100%处的颜色
                      },
                    ],
                    false
                  );
                } else {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#2BDAFF", //指0%处的颜色
                    },
                      {
                        offset: 1,
                        color: "#078FF7", //指100%处的颜色
                      },
                    ],
                    false
                  );
                }
                // return colors[params.dataIndex];
              },
            },
            label: {
              show: true,
              position: [760, -40],
              color: "#fff",
              formatter: function (params) {
                return params.value + "次";
              },
              fontSize: 32,
              distance: 5, // 与柱子的距离
              textStyle:{
                color:'#B5DAFF',
                fontFamily:'Source Han Sans CN'
              }
            },
            barWidth: 8,
            color: "#539FF7",
            data: this.chartsData.map(item => item.value),
          },{
            name: "",
            type: "bar",
            barWidth: 8,
            barGap: "-100%",
            data: this.chartsData.map(() => 150),
            itemStyle: {
              normal: {
                color: "#2E5171",
              },
            },
            z: 0,
          }]
        }
        myChart.setOption(option)
      })
    },
  },
  watch: {}
}
</script>

<style scoped>
.warningCharts2 {
  width: 935px;
  height: 390px;
}
</style>