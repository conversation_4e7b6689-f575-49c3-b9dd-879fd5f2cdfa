<template>
  <div class="msfwRight4">
    <div id="msfwEcharts"></div>
    <div class="box">
      <div v-for="(item, i) in data1" :key="i">
        <div>{{ item.name }}</div>
        <div>
          <div>{{ item.value }}</div>
          <div>{{ item.dw }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "right4",
  data() {
    return {
      msfwData: [
        {
          name: "小学",
          value: 15,
          // value1: 200,
          // value2: 1100,

        },
        {
          name: "中学",
          value: 15,
          // value1: 400,
          // value2: 900,
        },
        {
          name: "大学",
          value: 1,
          // value1: 800,
          // value2: 1020,
        }
      ],
      data1: [
        { name: "国家3A级景区数量", value: 3, dw: "个" },
        { name: "全年累计接待游客", value: 96.3, dw: "万人" },
        { name: "全年累计接待游客收入", value: 16.1, dw: "亿元" },
      ]
    }
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.initCharts2()
    })
  },
  methods: {
    initCharts2() {
      let myChart = this.$echarts.init(document.getElementById("msfwEcharts"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 14, // 设置宽度
          itemHeight: 14, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
          left: "30%"
        },
        grid: {
          left: "0%",
          right: "6%",
          top: "22%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.msfwData.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位: 数量",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
          {
            name: "",
            type: "value",
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "数量",
            type: "bar",
            barWidth: 30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00C0FF",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,192,255,0)",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.msfwData.map(item => item.value),
          },
          // {
          //   name: "学生",
          //   type: "bar",
          //   barWidth: 30,
          //   smooth: false,
          //   symbolSize: 10,
          //   itemStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //         {
          //           offset: 0,
          //           color: "#00C0FF",
          //         },
          //         {
          //           offset: 1,
          //           color: "rgba(0,192,255,0)",
          //         },
          //       ]),
          //       barBorderRadius: 4,
          //     },
          //   },
          //   data: this.msfwData.map(item => item.value),
          // },
          // {
          //   name: "教师",
          //   type: "bar",
          //   barWidth: 30,
          //   smooth: false,
          //   symbolSize: 10,
          //   itemStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //         {
          //           offset: 0,
          //           color: "#FFC460",
          //         },
          //         {
          //           offset: 1,
          //           color: "rgba(255, 196, 96,0)",
          //         },
          //       ]),
          //       barBorderRadius: 4,
          //     },
          //   },
          //   data: this.msfwData.map(item => item.value2),
          // },
          // {
          //   name: "职工",
          //   type: "bar",
          //   barWidth: 30,
          //   smooth: false,
          //   symbolSize: 10,
          //   itemStyle: {
          //     normal: {
          //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //         {
          //           offset: 0,
          //           color: "#22E8E8",
          //         },
          //         {
          //           offset: 1,
          //           color: "rgba(34, 232, 232,0)",
          //         },
          //       ]),
          //       barBorderRadius: 4,
          //     },
          //   },
          //   data: this.msfwData.map(item => item.value2),
          // },
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
.msfwRight4 {
  #msfwEcharts {
    width: 930px;
    height: 440px;
    margin-top: 50px;
  }

  .box {
    padding: 0 66px;
    margin-top: 24px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    >div {
      background-image: url("@/assets/home/<USER>");
      background-size: cover;
      width: 227px;
      height: 135px;
      margin-right: 83px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      >div:first-child {
        font-size: 22px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
      }

      >div:nth-child(2) {
        display: flex;
        align-items: baseline;

        >div:first-child {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        >div:last-child {
          font-size: 32px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
}</style>