<template>
  <div>
    <tabBox :tabList="tabList" v-model="tabIndex" class="tab" @titleClick="tabClick"></tabBox>
    <div class="charts18" id="charts18"></div>
  </div>
</template>

<script>
import tabBox from '@/components/tabBox'
import {getTaskTop} from "@/api/EventTask";
export default {
  name: 'charts5',
  data() {
    return {
      //tab
      tabList: ['接收任务量', '响应速度'],
      chartsData: [
        {
          name: '综合行政执法局',
          value: 47,
        },
        {
          name: '行政服务中心',
          value: 65,
        },
        {
          name: '建设局',
          value: 82,
        },
        {
          name: '网格办',
          value: 98,
        },
        {
          name: '金华市公安局江南分局',
          value: 112,
        },
      ],
      tabIndex: 0,
    }
  },
  components: { tabBox },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.getChart(this.tabIndex)
    })
  },
  methods: {
    tabClick() {
      this.getChart(this.tabIndex)
    },
    getChart(idx) {
      getTaskTop({type: this.tabList[idx]}).then(res => {
        this.chartsData = res.data.data.map(item => ({
          name:item.disposal_department,
          value:item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById('charts18'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
              var tar = params[0]
              return tar.name + ' : ' + tar.value
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          // legend: {
          //   icon: 'roundRect', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          //   itemWidth: 14, // 设置宽度
          //   itemHeight: 14, // 设置高度
          //   itemGap: 36, // 设置间距
          //   left: 170,
          //   top: 5,
          //   // icon: "circle",
          //   textStyle: {
          //     color: "#D6E7F9",
          //     fontSize: 16,
          //   },
          // },
          grid: {
            left: '4%',
            right: '4%',
            top: '10%',
            bottom: '1%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'value',
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
                show: true,
              },
            },
          ],
          yAxis: [
            {
              name: '',
              type: 'category',
              data: this.chartsData.map((item) => item.name),
              nameTextStyle: {
                fontSize: 28,
                color: '#D6E7F9',
                padding: [0, 0, 20, 50],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '人口数',
              type: 'bar',
              barWidth: 18,
              itemStyle: {
                barBorderRadius: [0, 0, 0, 0],
                normal: {
                  color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                    {
                      offset: 0,
                      color: '#00D1FF',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,209,255,0)',
                    },
                  ]),
                },
              },
              data: this.chartsData.map((item) => item.value),
              label: {
                show: false,
                position: 'top',
                textStyle: {
                  color: '#fff',
                  fontSize: 28,
                },
              },
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.tab {
  margin-left: 48px;
  margin-top: 12px;
}
.charts18 {
  width: 931px;
  height: 330px;
  margin-top: 10px;
}
</style>
