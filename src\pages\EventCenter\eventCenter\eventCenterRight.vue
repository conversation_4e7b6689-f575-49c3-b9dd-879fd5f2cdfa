<template>
  <div class="rightContainer">
    <div class="box1">
      <div class="box1-left">
        <ktitle :titleText="'重点事件'" width="939.6"></ktitle>
        <keyEvents></keyEvents>
      </div>
      <div class="box1-right">
        <ktitle :titleText="'事件热词'" width="939.6"></ktitle>
        <EventHotWords></EventHotWords>
      </div>
    </div>
    <div class="box2">
      <div class="box2-left">
        <ktitle :titleText="'部门事件排行'" width="939.6"></ktitle>
        <DepartmentEventRanking></DepartmentEventRanking>
      </div>
      <div class="box2-right">
        <ktitle :titleText="'部门事件分析'" width="939.6"></ktitle>
        <DepartmentEventAnalysis></DepartmentEventAnalysis>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import keyEvents from "@/pages/EventCenter/eventCenter/rightComponents/KeyEvents";
import EventHotWords from "@/pages/EventCenter/eventCenter/rightComponents/EventHotWords";
import DepartmentEventRanking from "@/pages/EventCenter/eventCenter/rightComponents/DepartmentEventRanking";
import DepartmentEventAnalysis from "@/pages/EventCenter/eventCenter/rightComponents/DepartmentEventAnalysis";
export default {
  name: "eventCenterLeft",
  data() {
    return {}
  },
  components:{
    ktitle,
    keyEvents,
    EventHotWords,
    DepartmentEventRanking,
    DepartmentEventAnalysis
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .rightContainer {
    margin: 80px 0 0 0;
    .box1 {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .box1-left,.box1-right {
        width: 939.6px;
        height: 1070px;
      }
    }
    .box2 {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 30px;
      .box2-left,.box2-right {
        width: 939.6px;
        height: 500px;
      }
    }
  }
</style>