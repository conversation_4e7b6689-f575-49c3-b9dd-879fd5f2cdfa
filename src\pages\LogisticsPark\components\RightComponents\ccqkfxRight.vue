<template>
  <div class="container">
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="275"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "ccqkfxRight",
  data() {
    return {
      tableData: [
        {
          index: "1",
          value1:"食材",
          value2:"100",
          value3:"金华市金江物资有限公司仓库",
          value4:"40",
          value5:"50",
        },
        {
          index: "2",
          value1:"石材",
          value2:"300",
          value3:"金华市大地物流有限公司仓储基地",
          value4:"200",
          value5:"200",
        },
        {
          index: "3",
          value1:"钢筋",
          value2:"150",
          value3:"建佳物流中心",
          value4:"95",
          value5:"100",
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '储藏物',
          key: 'value1',
          flex: 1,
        },
        {
          label: '存量',
          key: 'value2',
          flex: 1,
        },
        {
          label: '仓储地点',
          key: 'value3',
          flex: 3,
        },
        {
          label: '产量',
          key: 'value4',
          flex: 1,
        },
        {
          label: '销售',
          key: 'value5',
          flex: 1,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>