<template>
  <div class="mainContainer">
    <div class="filterContainer">
      <TimeFilter ref="timeFilter" />
    </div>
    <div class="mainContainerInner">
      <EconomicaLeft></EconomicaLeft>
      <CenterWrapBox>
        <EconomicalCenter />
      </CenterWrapBox>
      <EconomicaRight></EconomicaRight>
    </div>
  </div>
</template>

<script>
import CenterWrapBox from '@/components/mapComponent/centerWrapBox.vue'
import EconomicaLeft from '@/pages/EconomicalEcology/components/EconomicaLeft.vue'
import EconomicaRight from '@/pages/EconomicalEcology/components/EconomicaRight.vue'
import EconomicalCenter from '@/pages/EconomicalEcology/components/EconomicalCenter.vue'
import TimeFilter from "@/components/TimeFilter.vue"


export default {
  components: {
    CenterWrapBox,
    EconomicaLeft,
    EconomicaRight,
    EconomicalCenter,
    TimeFilter
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.mainContainer {
  position: relative;
  width: 100%;
  // height: 100%;
  height: calc(100% - 230px);
  margin-top: 230px;
  z-index: 999;
  // z-index: 2;
  background: url('~@/assets/application/dtBg.png') no-repeat center center;
  background-size: 100% 100%;

  .filterContainer {
    position: absolute;
    top: -3.3%;
    right: 1.5%;
  }

  .mainContainerInner {
    width: 100%;
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 0;
  }
}
</style>