<template>
  <div class="put_box">
    <div class="charts_radar" id="charts_radar"></div>
    <div class="table_box">
      <table-component :thConfig="thConfig" :tableData="tableData" />
    </div>
  </div>
</template>

<script>
import TableComponent from '@/components/TableComponent'
import {getTaskList} from "@/api/EventTask";
export default {
  name: 'charts2',
  data() {
    return {
      //table
      tableData: [
        {
          id: '1',
          type: '社区治理',
          name: '社区绿化带维护项目',
          level: '<span style="color: #FDC260;">紧急</span>',
          status: '<span style="color: #5BB4FF;">待分拨</span>',
        },
        {
          id: '2',
          type: '经济发展',
          name: '小微企业扶持政策落实',
          level: '<span style="color: #00BEFC;">其他</span>',
          status: '<span style="color: #5BB4FF;">已完成</span>',
        },
        {
          id: '3',
          type: '环境保护',
          name: '工业区垃圾处理方案审查',
          level: '<span style="color: #FF4A48;">特急</span>',
          status: '<span style="color: #5BB4FF;">待认领</span>',
        },
        {
          id: '4',
          type: '文化建设',
          name: '城市历史遗址保护规划',
          level: '<span style="color: #22E4E5;">平急</span>',
          status: '<span style="color: #5BB4FF;">待处理</span>',
        },
        {
          id: '5',
          type: '城市治理',
          name: '市中心交通枢纽改造',
          level: '<span style="color: #FDC260;">紧急</span>',
          status: '<span style="color: #5BB4FF;">待认领</span>',
        },
        {
          id: '6',
          type: '应急响应',
          name: '夏季防滑措施部署',
          level: '<span style="color: #FF4A48;">特急</span>',
          status: '<span style="color: #5BB4FF;">待分拨</span>',
        },
        {
          id: '7',
          type: '社区治理',
          name: '社区安全宣导活动',
          level: '<span style="color: #22E4E5;">平急</span>',
          status: '<span style="color: #5BB4FF;">待处理</span>',
        },
        {
          id: '8',
          type: '经济发展',
          name: '跨国贸易合作协议谈判',
          level: '<span style="color: #00BEFC;">其他</span>',
          status: '<span style="color: #5BB4FF;">已完成</span>',
        },
        {
          id: '9',
          type: '文化建设',
          name: '公民道德教育系列讲座',
          level: '<span style="color: #FDC260;">紧急</span>',
          status: '<span style="color: #5BB4FF;">待认领</span>',
        },
        {
          id: '10',
          type: '城市治理',
          name: '市区破损路牌更换',
          level: '<span style="color: #22E4E5;">平急</span>',
          status: '<span style="color: #5BB4FF;">待处理</span>',
        },
        {
          id: '11',
          type: '环境保护',
          name: '空气质量监测站建设',
          level: '<span style="color: #FF4A48;">特急</span>',
          status: '<span style="color: #5BB4FF;">待分拨</span>',
        },
        {
          id: '12',
          type: '应急响应',
          name: '防洪演习演练',
          level: '<span style="color: #FDC260;">紧急</span>',
          status: '<span style="color: #5BB4FF;">待认领</span>',
        },
      ],
      thConfig: [
        {
          th: '任务类型',
          field: 'type',
          width: '23%',
          hover: false,
        },
        {
          th: '任务名称',
          field: 'name',
          width: '24%',
          hover: true,
        },
        {
          th: '紧急程度',
          field: 'level',
          width: '28%',
          hover: false,
        },
        {
          th: '任务状态',
          field: 'status',
          width: '25%',
          hover: false,
        },
      ],
    }
  },
  components: { TableComponent },
  computed: {},
  mounted() {
    this.getList()
    this.initCharts2()
  },
  methods: {
    getList() {
      getTaskList().then(res => {
        this.tableData = res.data.data.list.map(item => ({
          id: item.id,
          type: item.type,
          name: item.name,
          level: `<span style="color: ${this.getUrgencyColor(item.urgency)};">${item.urgency}</span>`,
          status: `<span style="color: #5BB4FF;">${item.taskPhase}</span>`,
        }))
      })
    },
    initCharts2() {
      getTaskList().then(res => {
        let indicatorData = res.data.data.staticTask.map(item => ({
          name:item.title,
          max: 100
        }))
        let chartsValue = res.data.data.staticTask.map(item => item.cnt)
        let text = res.data.data.val;
        let myChart = this.$echarts.init(document.getElementById('charts_radar'))
        let option = {
          title: {
            text: text,
            x: 'center',
            y: 'center',
            textStyle: {
              color: '#FFC460',
              fontWeight: 'bolder',
              fontSize: 64,
            },
          },
          tooltip: {
            //雷达图的tooltip不会超出div，也可以设置position属性，position定位的tooltip 不会随着鼠标移动而位置变化，不友好
            confine: true,
            enterable: true, //鼠标是否可以移动到tooltip区域内
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            valueFormatter: function (value) {
              return value + '%'
            },
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          radar: {
            name: {
              textStyle: {
                color: '#00F0FF',
                fontSize: 28,
              },
            },
            shape: 'polygon',
            center: ['50%', '50%'],
            radius: '60%',
            // startAngle: 120,
            scale: true,
            axisLine: {
              lineStyle: {
                color: '#6BDAFB',
              },
            },
            splitLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: '#6BDAFB', // 设置网格的颜色
              },
            },
            indicator: indicatorData,
            splitArea: {
              show: true,
              areaStyle: {
                color: ['#0E1A40', '#064069'],
                opacity: 0.2,
              },
            },
          },
          grid: {
            position: 'center',
          },
          polar: {
            center: ['50%', '50%'], // 默认全局居中
            radius: '0%',
          },
          angleAxis: {
            min: 0,
            interval: 5,
            clockwise: false,
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
            },
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
          radiusAxis: {
            min: 0,
            interval: 20,
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              name: '任务完成情况',
              type: 'radar',
              symbol: 'circle', // 拐点的样式，还可以取值'rect','angle'等
              symbolSize: 10, // 拐点的大小
              itemStyle: {
                normal: {
                  borderWidth: 3,
                  borderColor: '#6BDAFB',
                  color: '#fff',
                },
              },
              areaStyle: {
                normal: {
                  color: '#6BDAFB',
                  opacity: 1,
                },
              },
              lineStyle: {
                width: 2,
                color: '#6BDAFB',
              },
              label: {
                normal: {
                  show: false,
                  formatter: (params) => {
                    return params.value + '%'
                  },
                  color: '#000',
                },
              },
              data: [
                {
                  value: chartsValue,
                },
              ],
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
    getUrgencyColor(val) {
      switch (val) {
        case '紧急':
          return '#FDC260'
        case '其他':
          return '#00BEFC'
        case '特急':
          return '#FF4A48'
        case '平急':
          return '#22E4E5'
        default:
          return '#FFFFFF'
      }
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.put_box {
  display: flex;
  .charts_radar {
    width: 1130px;
    height: 500px;
    margin-top: 26px;
  }
  .table_box {
    flex: 1;
    margin-left: 64px;
  }
}
</style>
