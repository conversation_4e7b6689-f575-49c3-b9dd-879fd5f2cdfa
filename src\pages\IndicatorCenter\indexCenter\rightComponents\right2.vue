<template>
  <div class="yqaq">
    <div class="item" v-for="(item, i) in data0" :key="i"
    :style="{'backgroundImage':'url('+item.icon+')'}"
    >
      <!-- <img :src="item.icon" alt=""> -->
      <div>{{ item.name }}</div>
      <div>
        <div>{{ item.value }}</div>
        <div>{{ item.dw }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "yqaq",
  data() {
    return {
      data0: [
        { name: "应急专家", value: 30, dw: "人", icon: require("@/assets/home/<USER>") },
        { name: "应急预案", value: 33, dw: "项", icon: require("@/assets/home/<USER>") },
        { name: "避难场所", value: 163, dw: "个", icon: require("@/assets/home/<USER>") },
        { name: "应急队伍", value: 25, dw: "支", icon: require("@/assets/home/<USER>") },
      ]
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
  },
  watch: {}
}
</script>

<style scoped lang="less">
.yqaq {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;

  .item {
    width: 354px;
    height: 161px;
    padding-right: 27px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    // background-image: url("@/assets/home/<USER>");
    margin-right: 153px;
    margin-top: 13px;



    >div:first-child {
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #FFFFFF;
      margin-top: 15px;
    }

    >div:nth-child(2) {
      display: flex;
      align-items: baseline;

      >div:first-child {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        color: #9AA9BF;

        background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      >div:last-child {
        font-size: 32px;
        font-family: BN;
        font-weight: 400;
        color: #9AA9BF;

        background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

  }
}
</style>