<template>
  <div class="parkLeftContainer">
    <div class="parkLeftContainer-left">
      <!--园区管理-->
      <yqgl></yqgl>
      <!--园区货运TOP3-->
      <yqhyfx style="margin-top: 50px;"></yqhyfx>
      <!--货运类型分析-->
      <hylxfx style="margin-top: 37px;"></hylxfx>
    </div>
    <div class="parkLeftContainer-right">
      <!--车辆管理-->
      <clgl></clgl>
      <!--货运趋势TOP5-->
      <hyqsfx style="margin-top: 71px;"></hyqsfx>
      <!--货车类型分布-->
      <hclxfb style="margin-top: 71px;"></hclxfb>
    </div>
  </div>
</template>

<script>
import yqgl from "@/pages/LogisticsPark/components/LeftComponents/yqgl";
import yqhyfx from "@/pages/LogisticsPark/components/LeftComponents/yqhyfx";
import hylxfx from "@/pages/LogisticsPark/components/LeftComponents/hylxfx";
import clgl from "@/pages/LogisticsPark/components/LeftComponents/clgl";
import hyqsfx from "@/pages/LogisticsPark/components/LeftComponents/hyqsfx";
import hclxfb from "@/pages/LogisticsPark/components/LeftComponents/hclxfb";
export default {
  name: "parkLeft",
  data() {
    return {}
  },
  components: {
    yqgl,
    yqhyfx,
    hylxfx,
    clgl,
    hyqsfx,
    hclxfb,
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.parkLeftContainer {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  .parkLeftContainer-left,.parkLeftContainer-right {
    width: 942px;
    height: 1774px;
    margin-top: 67px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
  }
}
</style>