<template>
  <div class="recommendPage">
    <swiper :options="swiperOption" ref="mySwiper" class="inSwiper">
      <swiper-slide v-for="(item,i) in swiperOption.equipmentList" :key="i">
        <div class="imgWrap" @click="pointLoad(item)">
          <div class="imgBg">
            <img class="sicononeClass" :src="item.icon">
            <div class="imgTxt">{{item.name}}</div>
          </div>
        </div>
      </swiper-slide>
      <div class="swiper-button-prev" slot="button-prev"></div>
      <div class="swiper-button-next" slot="button-next"></div>
    </swiper>
  </div>
</template>

<script>
// 引入插件
import { swiper, swiperSlide } from "vue-awesome-swiper"
import "swiper/dist/css/swiper.css"
import {getDeviceList, getIotInterface, getPkList} from "@/api/iot";

export default {
  components: {
    swiper,
    swiperSlide
  },
  data () {
    return {
      swiperOption: {
        loop: true,
        slidesPerView: 3, // 显示个数
        // spaceBetween: 20,
        // autoplay: {
        //   delay: 3000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false
        // },
        // 显示分页
        // pagination: {
        //   el: ".swiper-pagination",
        //   clickable: true //允许分页点击跳转
        // },
        // 设置点击箭头
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        },
        //设备列表
        equipmentList: [
          {
            name:"金华地磁",
            icon:require("@/assets/common/jhdc.png"),
            pointIcon:"/map/img/jhdcPoint.png",
            layerId:"jhdcPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"智慧灯杆",
            icon:require("@/assets/common/zhdg.png"),
            pointIcon:"/map/img/zhdgPoint.png",
            layerId:"zhdgPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"智慧垃圾桶",
            icon:require("@/assets/common/zhljt.png"),
            pointIcon:"/map/img/zhljtPoint.png",
            layerId:"zhljtPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"环境监测设备",
            icon:require("@/assets/common/hjjc.png"),
            pointIcon:"/map/img/hjjcPoint.png",
            layerId:"hjjcPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"智慧井盖",
            icon:require("@/assets/common/zhjg.png"),
            pointIcon:"/map/img/zhjgPoint.png",
            layerId:"zhjgPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"水质设备",
            icon:require("@/assets/common/szsb.png"),
            pointIcon:"/map/img/szsbPoint.png",
            layerId:"szsbPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          },
          {
            name:"空气站设备",
            icon:require("@/assets/common/kqzsb.png"),
            pointIcon:"/map/img/kqzsbPoint.png",
            layerId:"kqzsbPointLayer",
            pointList: [
              {
                type:"iotPoint",
                typeName:"金华地磁",
                lng:"119.644422",
                lat:"29.064353",
                status:"在线"
              }
            ],
            isLoad: false
          }
        ]
      }
    }
  },
  computed: {
    swiper () {
      return this.$refs.mySwiper.swiper
    }
  },
  mounted () {
    // current swiper instance
    // 然后你就可以使用当前上下文内的swiper对象去做你想做的事了
    console.log("this is current swiper instance object", this.swiper)
    // this.swiper.slideTo(3, 1000, false);

    this.initApi()
  },
  methods: {
    async initApi() {
      try {
        const response = await getPkList();
        const content = response.data.rows;
        const promises = this.swiperOption.equipmentList.map(async item => {
          const matchingItem = content.find(obj => obj.pkName === item.name);
          if (matchingItem) {
            item.pk = matchingItem.pk;
            const deviceListResponse = await this.getDeviceList(item);
            return deviceListResponse;
          } else {
            // 如果没有找到匹配的项，可以考虑抛出错误或返回一个空数组
            return [];
          }
        });

        const deviceLists = await Promise.all(promises);

        // 更新每个item的pointList
        this.swiperOption.equipmentList.forEach((item, index) => {
          item.pointList = deviceLists[index] || [];
        });

        let allEquipmentNumber = 0;
        this.swiperOption.equipmentList.forEach(item => {
          allEquipmentNumber += item.pointList.length;
        });

        this.$emit('number', { typeNumber: this.swiperOption.equipmentList.length, allEquipmentNumber: String(allEquipmentNumber) });
      } catch (error) {
        console.error('Failed to fetch and process data:', error);
        // 可以在这里添加错误处理逻辑，例如用户提示
      }
    },
    pointLoad(item) {
      item.isLoad = !item.isLoad
      if (item.isLoad) {
        this.$EventBus.$emit('CommonDrawPoint', item.pointList,item.layerId,item.pointIcon,true,false,true)
      } else {
        top.mapUtil.removeLayer(String(item.layerId))
      }
    },
    async getDeviceList(item) {
      console.log(item,"device");
      const requestConfig = getDeviceList({pk:item.pk})
      try {
        const res = await requestConfig;
        let pointList = res.data.rows;
        pointList = pointList.map(obj => {return {
          type:"iotPoint",
          typeName:item.name,
          lng:obj.lon,
          lat:obj.lat,
          status:obj.online?'在线':'离线'
        }})
        return pointList;
      } catch (error) {
        console.error('Solr search error:', error);
        throw error; // 重新抛出错误，以便调用者可以处理
      }
    }
  },
  destroyed() {
    this.swiperOption.equipmentList.forEach((item,i) => {
      item.isLoad = false;
      top.mapUtil.removeLayer(String(item.layerId))
    })
  }
};
</script>
<style lang="less" scoped>
.recommendPage {
  position: relative;
  width: 810px;
  height: 100%;
}
.recommendPage .inSwiper {
  position: initial;
}
.imgWrap {
  cursor: pointer;
  display: flex;
  justify-content: center;
}
.imgBg {
  width: 150px;
  height: 140px;
  background: url('~@/assets/common/sbg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  box-sizing: border-box;
  padding-top: 8px;
  overflow: hidden;
  .sicononeClass {
    width: 73px;
    height: 73px;
  }
  .imgTxt {
    font-size: 24px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    margin-top: 14px;
    white-space: nowrap;
  }
}
.recommendPage .swiper-container .swiper-button-prev {
  width: 97px;
  height: 87px;
  background: url('~@/assets/common/you.png') no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  // left: -45px;
  left: -60px;
  top: 33%;
}

.recommendPage .swiper-container .swiper-button-next {
  width: 97px;
  height: 87px;
  background: url('~@/assets/common/zuo.png') no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  // right: -45px;
  right: -60px;
  top: 33%;
}
</style>
