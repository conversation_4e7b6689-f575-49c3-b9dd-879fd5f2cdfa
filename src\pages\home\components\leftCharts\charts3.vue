<template>
  <div>
    <div class="pieCharts">
      <div class="item" v-for="(item, i) in eventData" :key="i">
        <div class="bgdivPar">
          <div :id="'bgDiv' + i" class="bgDiv">
          </div>
          <div class="ybMsg">{{ item.value + "%" }}</div>
        </div>
        <div class="msg">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {getEventCzqk} from "@/api/EventTask";
export default {
  name: "charts3",
  data() {
    return {
      eventData:[
        {name:"催办率",value:0},
        {name:"完结率",value:0},
        {name:"满意率",value:0},
      ]
    }
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.eventData.forEach((item,i) => {
        this.getData(item.name,i)
      })
    })
  },
  methods: {
    getData(name,i) {
      getEventCzqk({type: name}).then(res => {
        this.eventData[i].value = res.data.data[0].cnt;
        this.getPieChart("bgDiv" + i, this.eventData[i])
      })
    },
    getPieChart(id, itemData) {
      let myChart = this.$echarts.init(document.getElementById(id));
      let series = [];
      const centerY = 100; // 原点y轴的单位距离
      const centerYOffset = -5;// 原点偏移
      const centerX = 100 / 1;// 原点x轴的单位距离
      const chartList = [
        {
          value: itemData.value,
          total: 100,
          name: itemData.name
        }
      ];
      chartList.forEach((item, index) => {
        const radius = 80;
        const borderWidth = 5;
        const titleSize = 20;
        const valueSize = 20;
        const ratio = item.value / item.total * 360;
        const center = [
          centerX * (index % 3) + centerX / 2 + '%',
          centerY * Math.floor(index / 3) + centerY / 2 - centerYOffset + '%'
        ];
        series.push(
          { // 内圆背景
            type: 'pie',
            radius: radius + '%',
            center,
            z: 1,
            itemStyle: {
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 0.8, [{
                offset: 0,
                color: '#092a5d'
              },
                {
                  offset: 0.3,
                  color: '#092a5d'
                },
                {
                  offset: 1,
                  color: '#092a5d'
                }
              ], false),
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            },
            label: {
              show: false
            },
            tooltip: {
              show: false
            },
            data: [100]
          },
          { //  内圆边框
            type: 'pie',
            radius: [radius + '%', radius - 1 + '%'],
            center,
            // clockWise: false,
            z: 2,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: '#539FF7',
              color: '#539FF7'
            },
            label: {
              show: false
            },
            data: [100]
          },
          { // 进度
            type: 'gauge',
            radius: radius + '%',
            startAngle: 90,
            endAngle: ~ratio + 91,
            center,
            z: 3,
            axisLine: {
              lineStyle: {
                width: borderWidth,
                color: [[1, new echarts.graphic.LinearGradient(
                  0, 0, 0, 1,
                  [
                    { offset: 0, color: '#539FF7' },
                    { offset: 1, color: '#539FF7' }
                  ]
                )]]
              }
            },
            pointer: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            detail: {
              show: false,
              valueAnimation: true,
              formatter: () => `${item.value}%`,
              offsetCenter: [0, 0],
              fontSize: valueSize,
              fontWeight: 'bolder',
              color: '#52C3F7'
            },
            title: {
              offsetCenter: [0, '165%'],
              fontSize: titleSize,
              color: '#fff'
            },
            data: [item]
          },
          { // 内圆刻度
            type: 'gauge',
            startAngle: 90,
            endAngle: -270,
            radius: radius + '%',
            z: 4,
            center,
            axisLine: {
              show: false
            },
            pointer: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              distance: -10,
              length: borderWidth,
              lineStyle: {
                color: 'rgba(17,131,167,0.6)',
                width: 3
              }
            },
            axisLabel: {
              show: false
            }
          },
          { // 外圆
            type: 'pie',
            z: 5,
            radius: [radius + 5 + '%', radius + 3 + '%'],
            center,
            // clockWise: false,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: '#0e829b',
              color: '#0e829b'
            },
            label: {
              show: false
            },
            data: [100]
          },
          { // 为了添加点击事件添加遮罩
            type: 'pie',
            z: 6,
            radius: [radius + 5 + '%', 0],
            center,
            // clockWise: false,
            itemStyle: {
              shadowBlur: 20,
              shadowColor: 'transparent',
              color: 'transparent'
            },
            label: {
              show: false
            },
            data: [item]
          }
        );
      });

      let option = {

        animation: true,
        series: series
      };

      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
.pieCharts {
  margin-top: 105px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  .item {
    // margin-top: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 33%;

    // margin-left: 44px;
    // margin-right: 22px;
    .bgdivPar {
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      .bgDiv {
        width: 262px;
        height: 262px;
        display: flex;
        align-items: center;
        justify-content: center;

      }
      .ybMsg {
        top: 75px;
        position: absolute;
        font-size: 48px;
        font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
        background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }


    .msg {
      width: 133px;
      font-size: 34px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #D6E7F9;
      background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
      position: relative;
      bottom: 90px;
    }
  }
}
</style>