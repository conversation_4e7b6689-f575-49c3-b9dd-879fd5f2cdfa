<template>
  <div class="innerLeft">
    <Wrapbox bg="left">
      <div class="leftWrapper animate__animated animate__fadeInLeft">
        <div class="leftTop">
          <div class="commonContainer">
            <Ktitle titleText="生态环境" />
            <div class="cardWrap">
              <div class="firstLine">
                <WeatherCard :imgSrc="require('@/assets/economical/rjqw.png')" :isRed="true" num="28" unit="℃"
                  text="日均气温" />
                <WeatherCard :imgSrc="require('@/assets/economical/xdsd.png')" :isRed="false" num="49" unit="%"
                  text="相对湿度" />
                <WeatherCard :imgSrc="require('@/assets/economical/rjjs.png')" :isRed="false" num="4.9" unit="mm"
                  text="日均降水" />
                <WeatherCard :imgSrc="require('@/assets/economical/rzsc.png')" :isRed="true" num="7.2" unit="h"
                  text="日照时长" />
                <WeatherCard :imgSrc="require('@/assets/economical/kqzl.png')" :isRed="false" num="59" text="空气质量" />
              </div>
              <div class="secondLine">
                <WeatherCard :hasBottom="true" :imgSrc="require('@/assets/economical/fsfz.png')" :isRed="true" num="9.8"
                  unit="m/s" text="风速峰值" />
                <WeatherCard :hasBottom="true" :imgSrc="require('@/assets/economical/qwfz.png')" :isRed="false" num="28"
                  unit="℃" text="气温峰值" />
                <WeatherCard :hasBottom="true" :imgSrc="require('@/assets/economical/qyfz.png')" :isRed="false" num="25"
                  unit="KPa" text="气压峰值" />
                <WeatherCard :hasBottom="true" :imgSrc="require('@/assets/economical/ssfh.png')" :isRed="true" num="120"
                  unit="kw" text="实时负荷" />
                <WeatherCard :hasBottom="true" :imgSrc="require('@/assets/economical/zjrl.png')" :isRed="false" num="482"
                  unit="w" text="装机容量" />
              </div>
            </div>
          </div>
        </div>
        <div class="leftBot">
          <div class="botContainer">
            <Ktitle titleText="公共收支" />
            <TitleTwo class="csaqtitle2" :title-text="'财政收支'" />
            <div class="botop">
              <div class="botopInner">
                <div class="inner_l" @click="tabClick('left')">
                  <div class="inner_l_box">
                    <div class="tntxt">
                      <ToText :lineOneType="1" :numType="1" :unitType="1" :showTail="true" :text="'一般公共预算收入'"
                        :num="'18.31'" :unit="'亿元'" :tailNum="'30%'" />
                    </div>
                    <div class="imgBox">
                      <img :src="require('@/assets/economical/zztOne.png')" class="dtClass">
                    </div>
                  </div>
                  <div class="inner_l_box">
                    <div class="tntxt">
                      <ToText :lineOneType="1" :numType="3" :unitType="3" :showTail="true" :text="'一般公共预算支出'"
                        :num="'26.73'" :unit="'亿元'" :tailNum="'30%'" :isUp="false" />
                    </div>
                    <div class="imgBox">
                      <img :src="require('@/assets/economical/zzTwo.png')" class="dtClass">
                    </div>
                  </div>
                </div>
                <div class="inner_m">
                  <AjqsChart ref="ajqsChart" />
                </div>
                <div class="inner_r" @click="tabClick('right')">
                  <div class="inner_l_box">
                    <div class="tntxt">
                      <ToText :lineOneType="1" :numType="1" :unitType="1" :showTail="true" :text="'财政总收入'" :num="'73.06'"
                        :unit="'亿元'" :tailNum="'30%'" />
                    </div>
                    <div class="imgBox">
                      <img :src="require('@/assets/economical/zztOne.png')" class="dtClass">
                    </div>
                  </div>
                  <div class="inner_l_box">
                    <div class="tntxt">
                      <ToText :lineOneType="1" :numType="3" :unitType="3" :showTail="true" :text="'财政总支出'" :num="'79.01'"
                        :unit="'亿元'" :tailNum="'30%'" />
                    </div>
                    <div class="imgBox">
                      <img :src="require('@/assets/economical/zzTwo.png')" class="dtClass">
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="botbot">
              <div class="botbot_l">
                <TitleTwo :title-text="'城乡居民收支'" />
                <div class="waterBox">
                  <div class="waterOne">
                    <div class="imgContent huangClass">
                      <div class="numClass_w">48876元</div>
                    </div>
                    <div class="txtContent">
                      <div>全体居民</div>
                      <div>人均可支配收入</div>
                    </div>
                  </div>
                  <div class="waterOne">
                    <div class="imgContent lvClass">
                      <div class="numClass_w">69110元</div>
                    </div>
                    <div class="txtContent">
                      <div>城镇居民</div>
                      <div>人均可支配收入</div>
                    </div>
                  </div>
                  <div class="waterOne">
                    <div class="imgContent lanClass">
                      <div class="numClass_w">40103元</div>
                    </div>
                    <div class="txtContent">
                      <div>农村居民</div>
                      <div>人均可支配收入</div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="botbot_r">
                <TitleTwo :title-text="'国企收入'" />
                <div class="boxTwo">
                  <div class="boxTop">
                    <div class="insideBox">
                      <img :src="require('@/assets/economical/gqsrO.png')" class="boxImg">
                      <div class="gqsrContainer">
                        <ToText :text="'区属国有企业资产总额'" :num="'715.83'" :unit="'亿元'" />
                      </div>
                    </div>
                    <div class="insideBox">
                      <img :src="require('@/assets/economical/gqsrT.png')" class="boxImg">
                      <div class="gqsrContainer">
                        <ToText :text="'本月区属国有企业利润总额'" :num="'-7.25'" :unit="'亿元'" />
                      </div>
                    </div>
                  </div>

                  <div class="boxTop">
                    <div class="insideBox">
                      <img :src="require('@/assets/economical/gqsrS.png')" class="boxImg">
                      <div class="gqsrContainer">
                        <ToText :text="'区属国有企业已交税费总额'" :num="'0.33'" :unit="'亿元'" />
                      </div>
                    </div>
                    <div class="insideBox">
                      <img :src="require('@/assets/economical/gqsrO.png')" class="boxImg">
                      <div class="gqsrContainer">
                        <ToText :text="'国有资本经营预算总收入'" :num="'52'" :unit="'万元'" />
                      </div>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Wrapbox>
  </div>
</template>

<script>
import Wrapbox from "@/components/wrapbox.vue"
import Ktitle from "@/components/title.vue"
import WeatherCard from "@/pages/EconomicalEcology/components/leftComponents/WeatherCard.vue"
import TitleTwo from "@/components/titleTwo.vue"
import ToText from "@/components/ToText.vue"
import AjqsChart from "@/pages/EconomicalEcology/components/chartsComponents/AjqsChart.vue"

export default {
  components: {
    Wrapbox,
    Ktitle,
    WeatherCard,
    TitleTwo,
    ToText,
    AjqsChart
  },
  data () {
    return {
    }
  },
  methods: {
    tabClick (type) {
      if (type == 'left') {
        let option = this.$refs.ajqsChart.myChart.getOption()
        option.series[0].name = '一般公共预算收入'
        option.series[0].data = [23, 17, 16, 24, 18, 17, 17]
        option.series[1].name = '一般公共预算支出'
        option.series[1].data = [18, 16, 14, 19, 16, 14, 14]
        this.$refs.ajqsChart.myChart.setOption(option, true)
      } else {
        let option = this.$refs.ajqsChart.myChart.getOption()
        option.series[0].name = '财政总收入'
        option.series[0].data = [18, 35, 11, 12, 9, 27, 7]
        option.series[1].name = '财政总支出'
        option.series[1].data = [13, 22, 10, 13, 20, 4, 24]
        this.$refs.ajqsChart.myChart.setOption(option, true)
      }
    }
  }
}
</script>

<style scoped lang="less">
.innerLeft {
  .leftWrapper {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 2fr;

    .leftTop {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 50px 0 0 0;

      .commonContainer {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .cardWrap {
          flex: 1;
          width: 100%;
          box-sizing: border-box;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          padding: 21px 58px 68px 138px;

          .firstLine {
            display: flex;
            justify-content: space-between;
          }

          .secondLine {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }

    .leftBot {
      width: 100%;
      height: 100%;
      box-sizing: border-box;

      .botContainer {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-sizing: border-box;
        padding: 0 0 100px 0;

        .csaqtitle2 {
          width: 92% !important;
        }

        .botop {
          flex: 1;
          width: 100%;
          box-sizing: border-box;
          padding: 0 90px 0 145px;
          margin-top: 60px;
          margin-bottom: 80px;
          // background-color: #FFC460;

          .botopInner {
            width: 100%;
            height: 100%;
            display: flex;

            .inner_l_box {
              width: 382px;
              height: 224px;
              background: url('~@/assets/economical/jxThree.png') no-repeat center center;
              background-size: 100% 100%;
              display: flex;
              flex-direction: column;
              box-sizing: border-box;
              padding: 20px 30px 5px 30px;

              .tntxt {
                margin-bottom: 5px;
              }

              .imgBox {
                flex: 1;
                width: 100%;

                .dtClass {
                  width: 100%;
                  height: 100%;
                }
              }
            }

            .inner_l {
              display: flex;
              justify-content: space-between;
              flex-direction: column;
              cursor: pointer;
            }

            .inner_m {
              flex: 1;
              width: 100%;
            }

            .inner_r {
              display: flex;
              justify-content: space-between;
              flex-direction: column;
              cursor: pointer;

            }
          }
        }

        .botbot {
          display: flex;
          width: 100%;

          .botbot_l {
            width: 50%;
            display: flex;
            flex-direction: column;

            .waterBox {
              display: flex;
              justify-content: space-around;
              margin-top: 60px;

              .waterOne {
                display: flex;
                flex-direction: column;
                align-items: center;

                .huangClass {
                  background: url('~@/assets/economical/huang.png') no-repeat center center;
                  background-size: 100% 100%;
                }

                .lvClass {
                  background: url('~@/assets/economical/lv.png') no-repeat center center;
                  background-size: 100% 100%;
                }

                .lanClass {
                  background: url('~@/assets/economical/lan.png') no-repeat center center;
                  background-size: 100% 100%;
                }

                .imgContent {
                  width: 197px;
                  height: 200px;

                  position: relative;

                  .numClass_w {
                    width: 100%;
                    text-align: center;
                    font-size: 36px;
                    font-family: Bebas Neue;
                    font-weight: bolder;
                    background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    position: absolute;
                    top: 55px;
                    left: 50%;
                    transform: translate(-50%, 0);
                  }
                }

                .txtContent {
                  margin-top: 25px;
                  font-size: 32px;
                  font-family: Source Han Sans CN;
                  font-weight: 400;
                  color: #FFFFFF;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                }
              }
            }
          }

          .botbot_r {
            width: 50%;
            display: flex;
            flex-direction: column;

            .boxTwo {
              flex: 1;
              width: 100%;
              // background-color: aquamarine;
              margin-top: 40px;
              box-sizing: border-box;
              padding: 0 48px;
              display: grid;
              grid-template-columns: 1fr;
              grid-template-rows: 1fr 1fr;
              grid-row-gap: 35px;

              .boxTop {
                display: flex;
                justify-content: space-between;
              }

              .insideBox {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                // background-color: bisque;

                .boxImg {
                  width: 152px;
                  height: 141px;
                  margin-right: 8px;
                }

                .gqsrContainer {
                  flex: 1;
                  // background-color: chartreuse;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>