<template>
  <div class="rightContainer">
    <ktitle :title-text="'预警中心'" @titleClick="$router.push('/EarlyWarningCenter')"></ktitle>
    <div class="container1">
      <div class="item">
        <titleTwo :title-text="'预警类型'"></titleTwo>
        <charts1></charts1>
      </div>
      <div class="item">
        <titleTwo :title-text="'预警总数分析'"></titleTwo>
        <charts2></charts2>
      </div>
      <div class="item">
        <titleTwo :title-text="'各类预警趋势'"></titleTwo>
        <charts3></charts3>
      </div>
      <div class="item">
        <titleTwo :title-text="'预警数量排名'"></titleTwo>
        <charts4></charts4>
      </div>
    </div>
    <ktitle :title-text="'应用集成中心'" @titleClick="$router.push('/ApplicationIntegrationCenter')" style="margin-top: 41px"></ktitle>
    <div class="container2">
      <div class="item">
        <titleTwo :title-text="'应用集成概览'"></titleTwo>
        <div class="Application">
          <div class="Application-item" v-for="(item,i) in apps" :key="i" :style="{backgroundImage:'url('+item.icon+')'}">
            <div class="num">{{item.value}} <span class="unit">{{item.unit}}</span></div>
            <div class="text">{{item.name}} </div>
          </div>
        </div>
      </div>
      <div class="item">
        <titleTwo :title-text="'协同应用数量'"></titleTwo>
        <div class="Collaborative">
          <div style="width: 100%;display: flex;justify-content: space-between;align-items: center">
            <div class="Collaborative-item" v-for="(item,i) in colls.slice(0,2)" :key="i">
              <div class="item-left" :style="{backgroundImage:'url('+item.icon+')'}"></div>
              <div class="item-right">
                <div class="text">{{item.name}}</div>
                <div class="num">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
              </div>
            </div>
          </div>
          <div style="width: 100%;display: flex;justify-content: center;align-items: center">
            <div class="Collaborative-item" v-for="(item,i) in colls.slice(2,3)" :key="i">
              <div class="item-left" :style="{backgroundImage:'url('+item.icon+')'}"></div>
              <div class="item-right">
                <div class="text">{{item.name}}</div>
                <div class="num">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
              </div>
            </div>
          </div>
          <div style="width: 100%;display: flex;justify-content: space-between;align-items: center">
            <div class="Collaborative-item" v-for="(item,i) in colls.slice(3,colls.length)" :key="i">
              <div class="item-left" :style="{backgroundImage:'url('+item.icon+')'}"></div>
              <div class="item-right">
                <div class="text">{{item.name}}</div>
                <div class="num">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item">
        <titleTwo :title-text="'应用数'"></titleTwo>
        <charts5></charts5>
      </div>
      <div class="item">
        <titleTwo :title-text="'高频应用排行'"></titleTwo>
        <charts6></charts6>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import titleTwo from "@/components/titleTwo";
import charts1 from "@/pages/home/<USER>/rightCharts/charts1";
import charts2 from "@/pages/home/<USER>/rightCharts/charts2";
import charts3 from "@/pages/home/<USER>/rightCharts/charts3";
import charts4 from "@/pages/home/<USER>/rightCharts/charts4";
import charts5 from "@/pages/home/<USER>/rightCharts/charts5";
import charts6 from "@/pages/home/<USER>/rightCharts/charts6";
export default {
  name: "right",
  components:{
    titleTwo,
    ktitle,
    charts1,
    charts2,
    charts3,
    charts4,
    charts5,
    charts6
  },
  data() {
    return {
      apps:[
        {
          name:"应用总数",
          value:"31",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"接入应用数",
          value:"10",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"活跃应用数",
          value:"4",
          icon:require("@/assets/home/<USER>"),
          unit:"%"
        },
      ], //应用集成概览数据
      colls:[
        {
          name:"城市管理",
          value:"1",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"社会治理",
          value:"4",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"应急管理",
          value:"1",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"公共服务",
          value:"2",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        },
        {
          name:"文化旅游",
          value:"1",
          icon:require("@/assets/home/<USER>"),
          unit:"个"
        }
      ], //协同应用数量数据
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.unit {
  font-size: 30px;
  margin: 0 0 0 -10px;
}
.rightContainer {
  margin: 80px 0 0 68px;
  .container1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    .item {
      width: 931px;
      height: 400px;
      position: relative;
    }
  }
  .container2 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    .item {
      width: 931px;
      height: 400px;
      position: relative;
      .Application {
        width: 931px;
        height: 300px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 15px;
        .Application-item {
          width: 350px;
          height: 130px;
          background-size: cover;
          .num {
            font-size: 50px;
            font-family: BN;
            font-weight: 600;
            color: #9AA9BF;
            background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-left: 164px;
            white-space: nowrap;
          }
          .text {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #FFFFFF;
            margin: 24px 0 0 164px;
            white-space: nowrap;
          }
        }
      }
      .Collaborative {
        width: 931px;
        height: 300px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        .Collaborative-item {
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          height: 100px;
          .item-left {
            width: 134px;
            height: 123px;
            background-size: cover;
          }
          .item-right {
            width: fit-content;
            .num {
              font-size: 50px;
              font-family: BN;
              font-weight: 600;
              color: #9AA9BF;
              background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-left: 13px;
              white-space: nowrap;
            }
            .text {
              font-size: 32px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #FFFFFF;
              margin-left: 13px;
              white-space: nowrap;
            }
          }
        }
        .mleft {
          margin-left: 234px;
        }
      }
    }
  }
}

</style>