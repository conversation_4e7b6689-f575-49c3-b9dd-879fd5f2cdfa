<template>
  <div class="container">
    <div class="charts" id="chartsTjfx"></div>
  </div>
</template>

<script>
import {getTjfxChart} from "@/api/EventTask";
export default {
  name: "gzdbChart",
  data() {
    return {
      chartsData: [
        {
          name: '特急',
          value1: 71,
          value2: 40,
          value3: 87,
          value4: 41,
        },
        {
          name: '紧急',
          value1: 16,
          value2: 25,
          value3: 65,
          value4: 11,
        },
        {
          name: '平急',
          value1: 11,
          value2: 14,
          value3: 17,
          value4: 21,
        },
        {
          name: '其他',
          value1: 14,
          value2: 15,
          value3: 16,
          value4: 11,
        }
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      getTjfxChart().then(res => {
        this.chartsData = res.data.data.map(item => ({
          name: item.urgency,
          value1: item.dfj,
          value2: item.drl,
          value3: item.dcl,
          value4: item.ywc,
        }))
        let myChart = this.$echarts.init(document.getElementById('chartsTjfx'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: '2%',
            right: '2%',
            top: '22%',
            bottom: '1%',
            containLabel: true,
          },
          legend: {
            show: true,
            icon: 'rect',
            itemWidth: 20,
            itemHeight: 20,
            itemGap: 110,
            left: '25%',
            top: '5%',
            textStyle: {
              color: '#fff',
              fontSize: '28',
            },
          },
          xAxis: [
            {
              type: 'category',
              data: this.chartsData.map((item) => item.name),
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: '单位：个',
              type: 'value',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [10, -100, 10, 10],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '待分拨',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#FF4A48',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 74, 72, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.chartsData.map((item) => item.value1),
            },
            {
              name: '待认领',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(255, 196, 96, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 196, 96, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.chartsData.map((item) => item.value2),
            },
            {
              name: '待处理',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(34, 232, 232, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(34, 232, 232, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.chartsData.map((item) => item.value3),
            },
            {
              name: '已完成',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(0, 192, 255, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 192, 255, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.chartsData.map((item) => item.value4),
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  .charts {
    width: 1887px;
    height: 500px;
  }
}

</style>