<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <logisticsGardenLeft class="animate__animated animate__fadeInLeft"></logisticsGardenLeft>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <logisticsGardenRight class="animate__animated animate__fadeInRight"></logisticsGardenRight>
      </wrapbox>
    </div>
    <div class="inneCenter">
      <logisticsGardenCenter class="animate__animated animate__fadeInDown"></logisticsGardenCenter>
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import logisticsGardenLeft from "@/pages/LogisticsGarden/components/logisticsGardenLeft";
import logisticsGardenRight from "@/pages/LogisticsGarden/components/logisticsGardenRight";
import logisticsGardenCenter from "@/pages/LogisticsGarden/components/logisticsGardenCenter";
export default {
  name: "index",
  data() {
    return {

    }
  },
  components:{
    logisticsGardenLeft,
    logisticsGardenRight,
    logisticsGardenCenter,
    wrapbox
  },
  computed: {},
  mounted() {

  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .spsbBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .inneCenter{
    position: absolute;
    top: 229px;
    left: 2745px;
    z-index: 2;
  }
  .midBottom {
    position: absolute;
    left: 2210px;
    bottom: 66px;
    z-index: 2;
  }
}
</style>