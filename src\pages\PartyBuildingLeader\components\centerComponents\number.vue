<!--干部人数-->
<template>
  <div class="number">
      <div class="number-item" v-for="(item,i) in list" :key="i">
        <div class="titleContainer">
          <div class="title">{{item.name}}</div>
        </div>
        <div class="numberList">
          <div class="number" v-for="(item,i) in addZero(item.value).split('')">
            <div class="num">{{item}}</div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
export default {
  name: "number",
  data() {
    return {
      list:[
        {
          name:"公务员总人数",
          value:294
        },
        {
          name:"区管干部总人数",
          value:150
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    //数值长度不足7位数往前补0来满足七个字符长度要求最后返回字符串的方法
    addZero(value) {
      if (value.toString().length < 7) {
        for (let i = value.toString().length; i < 7; i++) {
          value = "0" + value.toString();
        }
      }
      return value;
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .number {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .number-item {
      width: 1065px;
      height: 371px;
      background-size: cover;
      background: url("@/assets/partyBuilding/numberBg.png");
      margin-top: 100px;
      .titleContainer {
        width: 412px;
        height: 165px;
        background-size: cover;
        background: url("@/assets/partyBuilding/title.png");
        margin: 59px 0 0 326px;
        text-align: center;
        .title {
          font-size: 48px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #B5CDF1;
          position: relative;
          top: 30px;
        }
      }
      .numberList {
        width: 658px;
        height:110px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-left: 200px;
        margin-top: -30px;
        .number {
          width: 94px;
          height: 110px;
          background-size: cover;
          background: url("@/assets/partyBuilding/numBg.png");
          .num {
            font-size: 88px;
            font-family: DINCond-Bold;
            font-weight: 400;
            color: #FFFFFF;
          }
        }
      }
    }
  }
</style>