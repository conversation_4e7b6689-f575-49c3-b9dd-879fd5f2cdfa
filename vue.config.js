const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const resolve = (dir) => {
  return path.join(__dirname, dir)
}
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const isProd = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: process.env.NODE_ENV === 'production' ? 'dist' : 'stage',
  chainWebpack: (config) => {
    config.resolve.alias.set('@$', resolve('src'))
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    plugins: isProd
      ? [
          new CompressionWebpackPlugin({
            test: /\.js$|\.html$|\.css$/u,
            threshold: 4096, // 超过 4kb 压缩
          }),
        ]
      : [],
  },
  pages: {
    index: {
      // page 的入口
      entry: 'src/main.js',
      // 模板来源
      template: 'public/index.html',
      // 在 dist/index.html 的输出
      filename: 'index.html',
      // 当使用 title 选项时，
      // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
      title: '开发区一网统管数字驾驶舱项目',
      // 在这个页面中包含的块，默认情况下会包含
      // 提取出来的通用 chunk 和 vendor chunk。
      chunks: ['chunk-vendors', 'index'],
    },
  },
  publicPath: './',

  devServer: {
    host: '0.0.0.0',
    port: '8080',
    allowedHosts: ['all'],
    proxy: {
      '/csdnMap': {
        changeOrigin: true,
        secure: false,
        target: 'https://csdn.dsjj.jinhua.gov.cn:9601',
        pathRewrite: {
          '^/csdnMap': "https://csdn.dsjj.jinhua.gov.cn:9601"
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || ''; // 真实请求网址
          console.log(realUrl); // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        }
      },
      '/csdnGet': {
        changeOrigin: true,
        secure: false,
        target: 'https://csdn.dsjj.jinhua.gov.cn:8101',
        pathRewrite: {
          '^/csdnGet': "https://csdn.dsjj.jinhua.gov.cn:8101"
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || ''; // 真实请求网址
          console.log(realUrl); // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        }
      },
      '/kfq-api': {
        // target: "http://192.168.110.174:9301", //测试
        target: "https://jkywtg.kfq.jinhua.gov.cn:8081", //线上
        // target: "http://172.16.10.97:8088", //范俊伟
        changeOrigin: true,
        pathRewrite: {
          // '^/kfq-api': "http://192.168.110.174:9301",
          '^/kfq-api': "https://jkywtg.kfq.jinhua.gov.cn:8081",
          // '^/kfq-api': "http://172.16.10.97:8088",
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || ''; // 真实请求网址
          console.log(realUrl); // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl; // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        }
      }
    },
  },
})
