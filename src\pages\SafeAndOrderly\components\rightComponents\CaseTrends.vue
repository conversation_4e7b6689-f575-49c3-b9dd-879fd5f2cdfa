<!--案件趋势-->
<template>
  <div class="trend">
    <titleTwo :title-text="'案件趋势'" style="margin-top: 78px;" :width="'1957'"></titleTwo>
    <div class="trendChart" id="trendChart"></div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "CaseTrends",
  data() {
    return {
      chartsData:[
        {
          name:"0410",
          value1:5,
          value2:3,
        },
        {
          name:"0411",
          value1:14,
          value2:10,
        },
        {
          name:"0412",
          value1:8,
          value2:4,
        },
        {
          name:"0413",
          value1:18,
          value2:15,
        },
        {
          name:"0414",
          value1:4,
          value2:9,
        },
        {
          name:"0415",
          value1:7,
          value2:8,
        },
        {
          name:"0416",
          value1:17,
          value2:16,
        },
        {
          name:"0417",
          value1:14,
          value2:9,
        },
        {
          name:"0418",
          value1:7,
          value2:17,
        },
        {
          name:"0419",
          value1:9,
          value2:5,
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("trendChart"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          itemGap: 45,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
          right:120,
          top:30
        },
        grid: {
          left: "8%",
          right: "6%",
          top: "22%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => (item.name)),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：万人",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 30,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "治安管理处罚数量",
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#CBF2FF",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value1),
          }, {
            name: "刑事立案数量",
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FFFECB",
                  },
                  {
                    offset: 1,
                    color: "#FFFC00",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value2),
          }
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .trend {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    .trendChart {
      width: 100%;
      height: 513px;
    }
  }
</style>