<template>
  <div class="container">
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="370"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "yjsjLeft",
  data() {
    return {
      tableData: [
        {
          index: "1",
          value1:"营运车辆超速",
          value2:"浙G13B13",
          value3:"2024-10-26 23:12",
          value4:"速度超速",
          value5:"金华",
        },
        {
          index: "2",
          value1:"违停上下货",
          value2:"浙G14C14",
          value3:"2024-10-26 23:12",
          value4:"未在规定位置停车上下货",
          value5:"南昌",
        },
        {
          index: "3",
          value1:"道路拥堵",
          value2:"浙G1DD15",
          value3:"2024-10-26 23:12",
          value4:"运输路线道路拥挤",
          value5:"杭州",
        },
        {
          index: "4",
          value1:"道路拥堵",
          value2:"浙G1DD15",
          value3:"2024-10-26 23:12",
          value4:"运输路线道路拥挤",
          value5:"杭州",
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '预警类型',
          key: 'value1',
          flex: 2,
        },
        {
          label: '预警车辆',
          key: 'value2',
          flex: 2,
        },
        {
          label: '预警时间',
          key: 'value3',
          flex: 3,
        },
        {
          label: '预警详情',
          key: 'value4',
          flex: 2,
        },
        {
          label: '预警地点',
          key: 'value5',
          flex: 2,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>