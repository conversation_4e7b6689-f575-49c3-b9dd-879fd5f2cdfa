<!--指标中心左侧页面-->
<template>
  <div class="leftContainer">
    <div class="left1">
      <titleTwo :titleText="'异常指标类型分布'" width="998" :showDate='false'></titleTwo>
      <yczblxfbLeft />
      <titleTwo :titleText="'异常指标'" width="998" :showDate='false'></titleTwo>
      <yczbLeft />
    </div>
    <div class="left2">
      <titleTwo :titleText="'地图展示'" width="1900" :showDate='false'></titleTwo>
      <leftMap />
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleCommon.vue"
import leftMap from '@/pages/IndicatorCenter/indexCenter/leftComponents/leftMap'
import yczbLeft from '@/pages/IndicatorCenter/indexCenter/leftComponents/yczbLeft'
import yczblxfbLeft from '@/pages/IndicatorCenter/indexCenter/leftComponents/yczblxfbLeft'
export default {
  name: 'indexCenterLeft',
  data() {
    return {
      indexs: [
        {
          name: '部门数量',
          value: 21,
        },
        {
          name: '大类数量',
          value: 29,
        },
        {
          name: '小类数量',
          value: 100,
        },
      ], //仪表盘数据
      politicals: [
        {
          name: '党员总数',
          value: '19074',
          icon: require('@/assets/home/<USER>/political1.png'),
          unit: '人',
        },
        {
          name: '基层党组织',
          value: '981',
          icon: require('@/assets/home/<USER>/political2.png'),
          unit: '个',
        },
        {
          name: '本年发展党员',
          value: '134',
          icon: require('@/assets/home/<USER>/political3.png'),
          unit: '人',
        },
        {
          name: '团员总数',
          value: '7715',
          icon: require('@/assets/home/<USER>/political4.png'),
          unit: '人',
        },
        {
          name: '团支部',
          value: '728',
          icon: require('@/assets/home/<USER>/political5.png'),
          unit: '个',
        },
        {
          name: '主题日活动',
          value: '1256',
          icon: require('@/assets/home/<USER>/political6.png'),
          unit: '个',
        },
        {
          name: '志愿服务队数量',
          value: '365',
          icon: require('@/assets/home/<USER>/political7.png'),
          unit: '件',
        },
        {
          name: '志愿服务类型数量',
          value: '12',
          icon: require('@/assets/home/<USER>/political8.png'),
          unit: '小时',
        },
      ], //党建统领数据
      num: '63710', //志愿服务参与人数
      numicon: require('@/assets/home/<USER>/political9.png'), //志愿服务参与人数icon
      num1: 67771, //志愿者注册总人数
      num2: '4785983.4', //服务总时长
      choose: 0, //经济生态默认选中
    }
  },
  components: {
    titleTwo,
    leftMap,
    yczbLeft,
    yczblxfbLeft,
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.gold {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff) !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
  text-align: left !important;
}
.item-left {
  width: 129px;
  height: 105px;
}
.unit {
  font-size: 30px;
  //margin: 0 0 0 -10px;
}
.item-right {
  margin-left: 33px;
  .name {
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #ffffff;
  }
  .value {
    font-size: 50px;
    font-family: BN;
    font-weight: 500;
    color: #9aa9bf;
    background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.leftContainer {
  margin: 80px 0 0 68px;
  display: flex;
  .left1 {
    width: 1088px;
  }
  .left2{
    flex: 1;
    margin-left: 60px;
  }
}
</style>