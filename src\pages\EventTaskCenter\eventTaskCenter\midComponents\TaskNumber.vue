<template>
  <div class="mid-main">
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="getAjList">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="total-sta">
      <div class="num">{{ajSum}}件</div>
      <div class="view-det-entry" @click="sjClick('rw')">详情</div>
    </div>
    <div class="legend">
      <div class="legend-item">
        <div class="block" style="background: #ff4a48"></div>
        <div class="text">待分拨</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #fdc260"></div>
        <div class="text">待认领</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #00befc"></div>
        <div class="text">待处理</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #22e4e5"></div>
        <div class="text">已完成</div>
      </div>
    </div>
    <div class="navs">
      <div
        class="nav-item"
        :class="item.on ? 'nav-item-select' : ''"
        v-for="item of navs"
        :key="item.id"
        @click="navItemClick(item)"
      >
        <img :src="item.icon" alt="" />
        <div class="text">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {getTaskNumber} from "@/api/EventTask";
export default {
  data() {
    return {
      navs: [
        {
          id: '1',
          name: '环境保护',
          icon: require('@/assets/eventTaskCenter/midBottom/task/hjbh.png'),
          on: false,
          markerType: '环境保护MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/hjbh.png',
          markerList: [
            {
              lx: '环境保护',
              jd: '待分拨',
              lng: 119.678093,
              lat: 29.068385,
              type: 'hjbh',
              rwmc: '河流水质监测与污染源排查',
              rwlx: '环境保护',
              jjcd: '紧急',
              rwzt: '待分拨',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
            {
              lx: '环境保护',
              jd: '待处理',
              lng: 119.688585,
              lat: 29.083159,
              type: 'hjbh',
              rwmc: '城市绿地恢复与扩展项目',
              rwlx: '环境保护',
              jjcd: '平急',
              rwzt: '待处理',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
            {
              lx: '环境保护',
              jd: '待处理',
              lng: 119.649958,
              lat: 29.080949,
              type: 'hjbh',
              rwmc: '湖泊生态修复与蓝藻治理项目',
              rwlx: '环境保护',
              jjcd: '紧急',
              rwzt: '待处理',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
            {
              lx: '环境保护',
              jd: '待处理',
              lng: 119.652269,
              lat: 29.08359,
              type: 'hjbh',
              rwmc: '城市垃圾分类与资源回收体系建设',
              rwlx: '环境保护',
              jjcd: '紧急',
              rwzt: '待处理',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
            {
              lx: '环境保护',
              jd: '待认领',
              lng: 119.647167,
              lat: 29.062483,
              type: 'hjbh',
              rwmc: '饮用水源地保护区环境整治行动',
              rwlx: '环境保护',
              jjcd: '紧急',
              rwzt: '待认领',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
            {
              lx: '环境保护',
              jd: '已完成',
              lng: 119.646413,
              lat: 29.097237,
              type: 'hjbh',
              rwmc: '长江流域湿地生态修复项目',
              rwlx: '环境保护',
              jjcd: '紧急',
              rwzt: '已完成',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
          ],
        },
        {
          id: '2',
          name: '经济发展',
          icon: require('@/assets/eventTaskCenter/midBottom/task/jjfz.png'),
          on: false,
          markerType: '经济发展MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/jjfz.png',
          markerList: [
            {
              lx: '经济发展',
              jd: '待分拨',
              lng: 119.649958,
              lat: 29.080949,
              type: 'jjfz',
              rwmc: '促进地方特色产业数字化升级项目',
              rwlx: '经济发展',
              jjcd: '紧急',
              rwzt: '待分拨',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
          ],
        },
        {
          id: '3',
          name: '文化建设',
          icon: require('@/assets/eventTaskCenter/midBottom/task/whjs.png'),
          on: false,
        },
        {
          id: '4',
          name: '社区治理',
          icon: require('@/assets/eventTaskCenter/midBottom/task/sqzl.png'),
          on: false,
          markerType: '社区治理MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/sqzl.png',
          markerList: [
            {
              lx: '社区治理',
              jd: '待分拨',
              lng: 119.660989,
              lat: 29.095532,
              type: 'sqzl',
              rwmc: '老旧小区公共设施改造计划',
              rwlx: '社区治理',
              jjcd: '紧急',
              rwzt: '待分拨',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
          ],
        },
        {
          id: '5',
          name: '城市治理',
          icon: require('@/assets/eventTaskCenter/midBottom/task/cszl.png'),
          on: false,
        },
        {
          id: '6',
          name: '应急响应',
          icon: require('@/assets/eventTaskCenter/midBottom/task/yjxy.png'),
          on: false,
        },
        {
          id: '7',
          name: '基础设施',
          icon: require('@/assets/eventTaskCenter/midBottom/task/jcss.png'),
          on: false,
          markerType: '基础设施MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/jcss.png',
          markerList: [
            {
              lx: '基础设施',
              jd: '待分拨',
              lng: 119.656139,
              lat: 29.082654,
              type: 'jcss',
              rwmc: '老城区道路改造与雨水排水系统升级工程',
              rwlx: '基础设施',
              jjcd: '紧急',
              rwzt: '待分拨',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
          ],
        },
        {
          id: '8',
          name: '卫生健康',
          icon: require('@/assets/eventTaskCenter/midBottom/task/wsjk.png'),
          on: false,
          markerType: '卫生健康MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/wsjk.png',
          markerList: [
            {
              lx: '卫生健康',
              jd: '待认领',
              lng: 119.643807,
              lat: 29.069057,
              type: 'wsjk',
              rwmc: '校园教职工全面健康体检项目',
              rwlx: '卫生健康',
              jjcd: '紧急',
              rwzt: '待认领',
              keyList: [
                { label: '任务名称', key: 'rwmc' },
                { label: '任务类型', key: 'rwlx' },
                { label: '紧急程度', key: 'jjcd' },
                { label: '任务状态', key: 'rwzt' },
              ],
            },
          ],
        },
      ],
      value:"日",
      options:[
        {label:"日",value:"日"},
        {label:"月",value:"月"},
        {label:"年",value:"年"},
      ],
      active: '1',
      ajList: [
        {
          name: '环境保护',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon1.png'),
          count: 0,
        },
        {
          name: '经济发展',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon2.png'),
          count: 0,
        },
        {
          name: '文化建设',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon3.png'),
          count: 0,
        },
        {
          name: '社区治理',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon4.png'),
          count: 0,
        },
        {
          name: '城市治理',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon5.png'),
          count: 0,
        },
        {
          name: '应急响应',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon6.png'),
          count: 0,
        },
        {
          name: '基础设施',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon7.png'),
          count: 0,
        },
        {
          name: '卫生健康',
          icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon8.png'),
          count: 0,
        },
      ]
    }
  },
  computed: {
    ajSum() {
      let sum = 0
      this.ajList.forEach((item,i) => {
        sum += item.count;
      })
      return sum
    }
  },
  mounted() {
    this.getAjList()
  },
  methods: {
    getAjList() {
      getTaskNumber({type: this.value}).then(res => {
        const typeCountMap = res.data.data.reduce((map, obj) => {
          map[obj.type] = (map[obj.type] || 0) + obj.cnt;
          return map;
        }, {});
        this.ajList.forEach(item => {
          item.count = typeCountMap[item.name] || 0;
        });
      })
    },
    navItemClick(item) {
      item.on = !item.on
      if (item.on) {
        this.$EventBus.$emit('CommonDrawPoint', item.markerList, item.markerType, item.markerIcon, false, true, true)
      } else {
        top.mapUtil.removeLayer(String(item.markerType))
      }
    },
    //事件详情
    sjClick(v) {
      this.$emit('tabClick', {name:v, value:this.ajList})
    },
    //清除点位
    cleanAllPoint() {
      this.navs.forEach((item,i) => {
        if (top.mapUtil.layers[String(item.markerType)]) {
          top.mapUtil.removeLayer(String(item.markerType))
        }
      })
    }
  },
  destroyed() {
    this.cleanAllPoint()
  }
}
</script>

<style lang="less" scoped>
.mid-main {
  position: relative;
  /deep/ .select {
    position: absolute;
    z-index: 999;
  }
  .total-sta {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    .num {
      margin-right: 40px;
      background: linear-gradient(to bottom, #ffeccb, #ffc460); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
      font-size: 54px;
      font-weight: bold;
    }
    .view-det-entry {
      color: #00f0ff;
      font-size: 32px;
      cursor: pointer;
    }
  }
  .navs {
    display: flex;
    justify-content: space-between;
    padding: 15px 260px;
    .nav-item {
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 239px;
      height: 218px;
      .text {
        margin-top: 20px;
        color: #00f0ff;
        font-size: 32px;
      }
    }
    .nav-item-select {
      background: url('@/assets/eventTaskCenter/midBottom/navSelBg.png') no-repeat;
    }
  }
  .legend {
    position: absolute;
    z-index: 30;
    top: 10px;
    right: 200px;
    display: flex;
    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 80px;
      .block {
        width: 20px;
        height: 20px;
        border-radius: 3px;
      }
      .text {
        margin-left: 12px;
        color: #d6e7f9;
        font-size: 28px;
      }
    }
  }
}
</style>
