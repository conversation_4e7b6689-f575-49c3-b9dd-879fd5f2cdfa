<template>
  <div class="container">
    <Ktitle :titleText="'跟踪督办'" :show-date="false">
      <div class="setImg"></div>
    </Ktitle>
    <div class="tabs">
      <div class="tab" v-for="(item,i) in tabList" :key="i" :class="{tabActive:tabChoose == i}" @click="tabChange(i)">{{item.name}}</div>
    </div>
    <!--任务督办-->
    <gzdbTable v-show="tabChoose == 0"/>
    <!--统计分析-->
    <gzdbChart v-show="tabChoose == 1"/>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import gzdbTable from "@/pages/CommandAndDispatch/components/RightComponents/gzdbChild/gzdbTable";
import gzdbChart from "@/pages/CommandAndDispatch/components/RightComponents/gzdbChild/gzdbChart";
export default {
  name: "gzdb",
  data() {
    return {
      tabList: [
        {
          name:"任务督办",
          value:""
        },
        {
          name:"统计分析",
          value:""
        }
      ],
      tabChoose:0
    }
  },
  components: {
    Ktitle,
    gzdbTable,
    gzdbChart
  },
  computed: {},
  mounted() {

  },
  methods: {
    tabChange(i) {
      this.tabChoose = i;
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .tabs {
      width: 962px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .tab {
        width: 471px;
        height: 73px;
        background-size: cover;
        background: url("~@/assets/Command/tabBg2.png") no-repeat;
        text-align: center;
        line-height: 73px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
        cursor: pointer;
      }
      .tabActive {
        background: url("~@/assets/Command/tabBg2A.png") no-repeat !important;
      }
    }
  }
</style>