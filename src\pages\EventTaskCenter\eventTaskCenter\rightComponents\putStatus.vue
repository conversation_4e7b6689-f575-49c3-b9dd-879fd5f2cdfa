<template>
  <div class="put_box">
    <div class="charts12" id="charts12"></div>
    <div class="static_box">
      <div class="static_item" v-for="(item, index) in listData" :key="index">
        <img :src="item.img" class="task_p" alt="" />
        <div class="title_box">
          <div class="title">{{ item.name }}</div>
          <div class="count_box">
            <div class="count gold">{{ item.count }}</div>
            <div class="unit gold">件</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getTaskConnectChart} from "@/api/EventTask";

export default {
  name: 'charts2',
  data() {
    return {
      charts2Data: [
        {
          name: '开发区政法办',
          value1: 71,
          value2: 40,
          value3: 87,
          value4: 41,
        },
        {
          name: '苏孟乡',
          value1: 16,
          value2: 25,
          value3: 65,
          value4: 11,
        },
        {
          name: '秋滨街道',
          value1: 11,
          value2: 14,
          value3: 17,
          value4: 21,
        },
        {
          name: '三江街道',
          value1: 14,
          value2: 15,
          value3: 16,
          value4: 11,
        },
        {
          name: '西关街道',
          value1: 7,
          value2: 15,
          value3: 22,
          value4: 43,
        },
        {
          name: '江南街道',
          value1: 12,
          value2: 54,
          value3: 32,
          value4: 15,
        },
        {
          name: '汤溪镇',
          value1: 15,
          value2: 22,
          value3: 18,
          value4: 28,
        },
      ],
      //任务接入情况统计
      listData: [
        { name: '特急', count: 147, img: require('@/assets/eventTaskCenter/task_p1.png') },
        { name: '紧急', count: 181, img: require('@/assets/eventTaskCenter/task_p2.png') },
        { name: '平急', count: 257, img: require('@/assets/eventTaskCenter/task_p3.png') },
        { name: '其他', count: 170, img: require('@/assets/eventTaskCenter/task_p4.png') },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts2()
  },
  methods: {
    initCharts2() {
      getTaskConnectChart().then(res => {
        const urgencyCountMap = res.data.data.rwjrqkRight.reduce((map, obj) => {
          map[obj.urgency] = (map[obj.urgency] || 0) + obj.cnt;
          return map;
        }, {});
        this.listData.forEach(item => {
          item.count = urgencyCountMap[item.name] || 0;
        });
        this.charts2Data = res.data.data.rwjrqkLeft.map(item => ({
          name: item.street,
          value1: item.tj,
          value2: item.jj,
          value3: item.pj,
          value4: item.qt,
        }))
        let myChart = this.$echarts.init(document.getElementById('charts12'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: '8%',
            right: '6%',
            top: '22%',
            bottom: '1%',
            containLabel: true,
          },
          legend: {
            show: true,
            icon: 'rect',
            itemWidth: 20,
            itemHeight: 20,
            itemGap: 110,
            left: '25%',
            top: '5%',
            textStyle: {
              color: '#fff',
              fontSize: '28',
            },
          },
          xAxis: [
            {
              type: 'category',
              data: this.charts2Data.map((item) => item.name),
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: '单位：个',
              type: 'value',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [10, -100, 10, 10],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '特急',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#FF4A48',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 74, 72, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts2Data.map((item) => item.value1),
            },
            {
              name: '紧急',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(255, 196, 96, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 196, 96, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts2Data.map((item) => item.value2),
            },
            {
              name: '平急',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(34, 232, 232, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(34, 232, 232, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts2Data.map((item) => item.value3),
            },
            {
              name: '其他',
              type: 'bar',
              stack: 'total',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(0, 192, 255, 1)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0, 192, 255, 0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts2Data.map((item) => item.value4),
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.put_box {
  display: flex;
  .charts12 {
    width: 1130px;
    height: 500px;
    margin-top: 26px;
  }
  .static_box {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    margin-left: 64px;
    .static_item {
      width: 50%;
      display: flex;
      align-content: center;
      align-items: center;
      .task_p {
        width: 133px;
        height: 133px;
      }
      .title_box {
        margin-left: 14px;
        margin-top: 22px;
        .title {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 32px;
          color: #ffffff;
        }
        .count_box {
          display: flex;
          align-items: baseline;
          margin-top: 14px;
          .count {
            font-family: Bebas Neue;
            font-weight: 400;
            font-size: 60px;
          }
          .unit {
            font-family: Bebas Neue;
            font-weight: 400;
            font-size: 32px;
          }
        }
      }
    }
  }
}
</style>
