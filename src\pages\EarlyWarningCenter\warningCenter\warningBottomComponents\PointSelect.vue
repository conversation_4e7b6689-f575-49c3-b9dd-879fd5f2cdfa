<template>
  <div class="selectBox">
    <div class="selectBox-title">预警级别</div>
    <div class="selectBox-container">
      <div class="selectBox-container-item" v-for="(item,i) in list" :key="i">
        <img :src="item.icon" alt="" class="icon">
        <div class="name">{{item.name}}</div>
        <img v-show="!item.isChecked" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="checked(item)" style="margin-left: 30px;cursor: pointer">
        <img v-show="item.isChecked" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="checked(item)" style="margin-left: 30px;cursor: pointer">
      </div>
    </div>
  </div>
</template>

<script>
import {mapMutations} from "vuex";

export default {
  name: "PointSelect",
  data() {
    return {
      list: [
        {
          id:"1",
          icon:require("@/assets/warnningCenter/严重.png"),
          name:"紧急",
          isChecked:true,
          value: 1
        },
        {
          id:"2",
          icon:require("@/assets/warnningCenter/警告.png"),
          name:"重要",
          isChecked:true,
          value: 2
        },
        {
          id:"3",
          icon:require("@/assets/warnningCenter/安全.png"),
          name:"普通",
          isChecked:true,
          value: 3
        },
        {
          id:"4",
          icon:require("@/assets/warnningCenter/提示.png"),
          name:"提示",
          isChecked:true,
          value: 4
        },
        {
          id:"5",
          icon:require("@/assets/warnningCenter/未确认.png"),
          name:"未确认",
          isChecked:true,
          value: 5
        }
      ]
    }
  },
  computed: {

  },
  mounted() {
    this.Level_HISTORY(this.list.filter(item => item.isChecked))
  },
  methods: {
    ...mapMutations('mapStatus', ['Level_HISTORY']),
    checked(item) {
      item.isChecked = !item.isChecked
      this.Level_HISTORY(this.list.filter(item => item.isChecked))
      this.$emit('levelCheckChange')
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .selectBox {
    width: 452px;
    height: 756px;
    background: url("~@/assets/warnningCenter/box-bg.png") no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .selectBox-title {
      width: 432px;
      height: 65px;
      background: url("~@/assets/warnningCenter/box-title.png") no-repeat;
      background-size: cover;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 40px;
      letter-spacing: 1px;
      text-shadow: 0px 10px 20px rgba(0,0,0,0.4);
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 20px;
    }
    .selectBox-container {
      width: 100%;
      height: 691px;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: center;
      .selectBox-container-item {
        width: 400px;
        height: 100px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .icon {
          width: 56px;
          height: 56px;
        }
        .name {
          width: 100px;
          margin-left: 44px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
</style>