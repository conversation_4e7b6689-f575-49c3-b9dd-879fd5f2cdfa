<!--教育服务-->
<template>
  <div class="rightOne">
    <Ktitle titleText="教育服务" :width="'950'" />
    <div class="jyfwWrap">
      <div class="tyWrap" v-for="(item,i) in list" :key="i">
        <div class="gdzkBoxInner">
          <img :src="item.icon" class="imgClass">
          <ToText :lineOneType="4" :numType="3" :unitType="3" :text="item.name" :num="item.value" :unit="item.unit" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
import ToText from "@/components/ToText.vue"
export default {
  name: "jyfw",
  components:{
    Ktitle,
    ToText
  },
  data() {
    return {
      list: [
        {
          icon:require("@/assets/publicService/you.png"),
          name:"幼儿园数量",
          value:"81",
          unit:"家"
        },
        {
          icon:require("@/assets/publicService/xiao.png"),
          name:"小学数量",
          value:"15",
          unit:"所"
        },
        {
          icon:require("@/assets/publicService/zhong.png"),
          name:"中学数量",
          value:"33",
          unit:"所"
        },
        {
          icon:require("@/assets/publicService/da.png"),
          name:"大学数量",
          value:"1",
          unit:"所"
        },
        {
          icon:require("@/assets/publicService/ben.png"),
          name:"本地户籍义务教育阶段学生数",
          value:"24340",
          unit:"人"
        },
        {
          icon:require("@/assets/publicService/ge.png"),
          name:"各学段转任教师数",
          value:"4046",
          unit:"人"
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.rightOne {
  width: 940px;
  height: fit-content;
  box-sizing: border-box;
  margin: 50px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  // background-color: bisque;
  .jyfwWrap {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;

    .tyWrap {
      width: 100%;
      height: 100%;
      // background-color: antiquewhite;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .gdzkBoxInner {
      display: flex;
      align-items: center;

      .imgClass {
        width: 177px;
        height: 137px;
      }
    }
  }
}

</style>