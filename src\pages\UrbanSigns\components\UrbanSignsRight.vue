<template>
  <div class="container">
    <img :src="img" alt="" class="rIghtPage">
    <div class="number1" @click="showDialog('整洁有序')"></div>
    <div class="number2" @click="showDialog('多元包容')"></div>
    <div class="number3" @click="showDialog('创新活力')"></div>
  </div>
</template>

<script>
export default {
  name: "UrbanSignsRight",
  data() {
    return {
      img: require("@/assets/cstz/右侧.png")
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    showDialog(str) {
      this.$emit('showDialog',str)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  .rIghtPage {
    width: 2097px;
    height: 1927px;
  }
  .number1 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 114px;
    left: 1841px;
    cursor: pointer;
  }
  .number2 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 514px;
    left: 1841px;
    cursor: pointer;
  }
  .number3 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 1234px;
    left: 1841px;
    cursor: pointer;
  }
}
</style>