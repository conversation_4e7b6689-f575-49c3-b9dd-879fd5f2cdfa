<template>
  <div class="SafeAndOrderlyRight">
    <ktitle :title-text="'社会法制'"></ktitle>
    <div class="legality">
      <div class="legality-item" v-for="(item,i) in indexs" :key="i" :style="{background:'url('+ item.img +')'}">
        <div class="info">
          <div class="name">{{item.name}}</div>
          <div class="value">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
        </div>
      </div>
    </div>
    <CaseTrends></CaseTrends>
    <div class="percent">
      <success></success>
      <falseRate></falseRate>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import CaseTrends from "@/pages/SafeAndOrderly/components/rightComponents/CaseTrends";
import success from "@/pages/SafeAndOrderly/components/rightComponents/success";
import falseRate from "@/pages/SafeAndOrderly/components/rightComponents/falseRate";
export default {
  name: "soLeft",
  data() {
    return {
      indexs:[
        {
          name:"派出所民警数量",
          value:184,
          unit:"人",
          img:require("@/assets/safe/社会法治1.png")
        },
        {
          name:"社区民警数量",
          value:56,
          unit:"人",
          img:require("@/assets/safe/社会法治2.png")
        },
        {
          name:"在线警力数量",
          value:260,
          unit:"人",
          img:require("@/assets/safe/社会法治1.png")
        }
      ]
    }
  },
  components:{
    ktitle,
    CaseTrends,
    success,
    falseRate
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .SafeAndOrderlyRight {
    margin: 80px 68px 0 68px;
    .legality {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .legality-item {
        width: 288px;
        height: 292px;
        background-size: cover;
        .info {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
          .name {
            font-size: 38px;
            font-family: SourceHanSansSC;
            font-weight: 500;
            color: #D6E7F9;
            margin-top: 180px;
          }
          .value {
            font-size: 50px;
            font-family: DIN;
            font-weight: bold;
            color: #FFFFFF;
            background: linear-gradient(0deg, #ACCBEE 0%, #E7F0FD 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .unit {
            //margin-left: 5px;
            font-weight: 500;
          }
        }
      }
    }
    .percent {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 68px;
    }
  }
</style>