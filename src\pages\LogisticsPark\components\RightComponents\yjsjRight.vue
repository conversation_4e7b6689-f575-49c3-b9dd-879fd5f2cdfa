<template>
  <div class="container">
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="370"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "yjsjRight",
  data() {
    return {
      tableData: [
        {
          index: "1",
          value1:"违停上下货",
          value2:"2024-10-26 23:12",
          value3:"婺城",
          value4:"未在规定位置停车上下货",
          value5:"浙G13B13",
        },
        {
          index: "2",
          value1:"侧翻",
          value2:"2024-10-26 23:12",
          value3:"兰溪",
          value4:"运输时车辆侧翻",
          value5:"浙G14C14",
        },
        {
          index: "3",
          value1:"碰撞",
          value2:"2024-10-26 23:12",
          value3:"浦江",
          value4:"与其他车辆发生碰撞",
          value5:"浙G1DD15",
        },
        {
          index: "4",
          value1:"违停上下货",
          value2:"2024-10-26 23:12",
          value3:"婺城",
          value4:"未在规定位置停车上下货",
          value5:"浙G13B13",
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '事故类型',
          key: 'value1',
          flex: 2,
        },
        {
          label: '事故时间',
          key: 'value2',
          flex: 3,
        },
        {
          label: '事故区域',
          key: 'value3',
          flex: 1,
        },
        {
          label: '违法情况',
          key: 'value4',
          flex: 3,
        },
        {
          label: '车牌号',
          key: 'value5',
          flex: 3,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>