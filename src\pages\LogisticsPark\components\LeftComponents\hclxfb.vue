<template>
  <div>
    <Ktitle :titleText="'货车类型分布'" width="939.6" :show-date="false" />
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="getChartData">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <pie3D id="hclxEcharts"  :scaleCustom="1.7" :cHidth="410" :options="charts1Data" v-if="showPie3D" :legend-custom="legendOption" :zHeight="5" style="margin-top: 60px;"></pie3D>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import pie3D from "@/components/pie3D";
export default {
  name: "hclxfb",
  data() {
    return {
      value: 1, //预警总数分析默认
      options: [
        { label: '全部园区', value: 1 }
      ],
      charts1Data: [],
      legendOption: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 14,
        left: '65%',
        top: '32%',
      },
      showPie3D: false
    }
  },
  components:{
    pie3D,
    Ktitle
  },
  computed: {},
  mounted() {
    this.getChartData()
  },
  methods: {
    getChartData() {
      this.charts1Data = [
        {
          name: "普通货车",
          value: 105
        },
        {
          name: "冷链车",
          value: 90
        },
        {
          name: "平板车",
          value: 90
        },
        {
          name: "小货车",
          value: 105
        }
      ]
      this.showPie3D = true
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  left: 1728px;
  top: 1440px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #fefefe;
  }
}
</style>