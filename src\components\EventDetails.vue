<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-05-15 11:22:25
 * @LastEditors: wjb
 * @LastEditTime: 2024-05-15 14:32:15
-->
<template>
  <div class="sj_main">
    <div class="sj_left">
      <div class="title">事件信息</div>
      <div class="item_box">
        <div class="item_left">事件来源：</div>
        <div class="item_right">{{obj.sjly}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">事件内容：</div>
        <div class="item_right">{{obj.sjnr}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">事件编号：</div>
        <div class="item_right">{{obj.sjbh}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">事件时间：</div>
        <div class="item_right">{{obj.sjsj}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">事件地点：</div>
        <div class="item_right">{{obj.sjdd}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">事件类型：</div>
        <div class="item_right">{{obj.sjlx}}</div>
      </div>
      <div class="title mgt32">上报人信息</div>
      <div class="item_box">
        <div class="item_left">姓名：</div>
        <div class="item_right">{{obj.xm}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">联系电话：</div>
        <div class="item_right">{{obj.lxdh}}</div>
      </div>
    </div>
    <div class="line"></div>
    <div class="sj_right">
      <div class="title">进度追踪</div>
      <div class="item_box">
        <div class="item_left">事件来源：</div>
        <div class="item_right">{{obj.sjly}}</div>
      </div>
      <div class="process_box">
        <div class="process_top">
          <div class="process_item" v-for="(item, index) in processData" :key="index">
            <img src="@/assets/eventTaskCenter/sj_process_icon.png" v-if="obj.jdindex < index" alt="" />
            <img src="@/assets/eventTaskCenter/sj_process_icon_on.png" v-else alt="" />
            <img src="@/assets/eventTaskCenter/sj_process_line.png" alt="" v-if="index != 3" />
          </div>
        </div>
        <div class="process_bottom">
          <div class="process_name gold">上报</div>
          <div class="process_name" :class="obj.jdindex > 0 ? 'gold' : 'blue_linear1'">受理</div>
          <div class="process_name" :class="obj.jdindex > 1 ? 'gold' : 'blue_linear1'">办理</div>
          <div class="process_name" :class="obj.jdindex > 2 ? 'gold' : 'blue_linear1'">结案</div>
        </div>
      </div>
      <div class="title mgt32">{{obj.cldw}}</div>
      <div class="item_box">
        <div class="item_left">结案时间：</div>
        <div class="item_right">{{obj.jasj}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">结案信息：</div>
        <div class="item_right">{{obj.jaxx}}</div>
      </div>
      <div class="item_box">
        <div class="item_left">图片信息：</div>
        <div class="item_right">{{obj.tpxx}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {getEventDetail} from "@/api/EventTask";

export default {
  props: {
    //显示flag
    showFlag: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      obj: {},
      processData: [1, 2, 3, 4],
    }
  },
  mounted() {
    this.getDetail()
  },
  watch: {
    showFlag: function (newVal, oldVal) {
      if (newVal) {
        let that = this
      }
    }
  },
  methods: {
    getDetail() {
      getEventDetail(this.id).then(res => {
        const that = this;
        let responce = res.data.data;
        this.obj = {
          jdindex: that.getJdindex(responce.processStatus),
          sjly: responce.eventSource,
          sjnr: responce.content,
          sjbh: responce.serialNumber,
          sjsj: responce.occurDate,
          sjdd: responce.occurLocation,
          sjlx: responce.firstType,
          xm: responce.reportUserName,
          lxdh: responce.reportContact,
          cldw: responce.industryOrgName,
          jasj: responce.finishTime,
          jaxx: responce.finishContent,
          tpxx: responce.fileName,
        }
      })
    },
    getJdindex(str) {
      switch (str) {
        case "上报":
          return 0;
        case "受理":
          return 1;
        case "办理":
          return 2;
        case "结案":
          return 3;
        default:
          return 0;
      }
    }
  },
}
</script>

<style lang="less" scoped>
.sj_main {
  display: flex;
  padding: 60px 0;
  .sj_left {
    width: 668px;
    padding: 0 32px;
    box-sizing: border-box;
    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 32px;
      color: #bfd0ff;
      line-height: 40px;
    }
  }
  .line {
    width: 1px;
    background: rgba(255, 255, 255, 0.5);
  }
  .sj_right {
    width: 554px;
    padding-left: 32px;
    box-sizing: border-box;
    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 32px;
      color: #bfd0ff;
      line-height: 40px;
    }
  }
  .item_box {
    display: flex;
    margin-top: 16px;
    .item_left {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 32px;
      color: #cef2ff;
      line-height: 40px;
      width: 160px;
      text-align: left;
    }
    .item_right {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      line-height: 40px;
      flex: 1;
    }
  }
  .mgt32 {
    margin-top: 32px;
  }
  .process_box {
    padding-top: 24px;
    .process_top {
      display: flex;
      margin-left: 21px;
      .process_item {
        display: flex;
        align-content: center;
        align-items: center;
      }
    }
    .process_bottom {
      display: flex;
      align-content: center;
      align-items: center;
      margin-top: 16px;
      .process_name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 32px;
        line-height: 40px;
        margin-right: 46px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
