<template>
  <div class="zdwxyEcharts" id="zdwxyEcharts"></div>
</template>

<script>
export default {
  name: 'charts3',
  data() {
    return {
      chartsData: [
        {
          name: '市场部',
          value: 40,
          value2: 35,
        },
        {
          name: '销售部',
          value: 50,
          value2: 40,
        },
        {
          name: '行政部',

          value: 60,
          value2: 50,
        },
        {
          name: '人事部',

          value: 70,
          value2: 60,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      this.getChart()
    })
  },
  methods: {
    getChart() {
      let myChart = this.$echarts.init(document.getElementById('zdwxyEcharts'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          icon: 'roundRect', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 14, // 设置宽度
          itemHeight: 14, // 设置高度
          itemGap: 36, // 设置间距
          left: '40%',
          top: 5,
          // icon: "circle",
          textStyle: {
            color: "#D6E7F9",
            fontSize: 16,
          },
        },
        grid: {
          left: '4%',
          right: '10%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
              show: true,
            },
          },
        ],
        yAxis: [
          {
            name: '',
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            nameTextStyle: {
              fontSize: 28,
              color: '#D6E7F9',
              padding: [0, 0, 20, 50],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '接待批次',
            type: 'bar',
            barWidth: 18,
            itemStyle: {
              barBorderRadius: [0, 0, 0, 0],
              normal: {
                color: '#00C0FF',
              },
            },
            data: this.chartsData.map((item) => item.value),
            label: {
              show: false,
              position: 'top',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
          },
          {
            name: '接待人数',
            type: 'bar',
            barWidth: 18,
            itemStyle: {
              barBorderRadius: [0, 0, 0, 0],
              normal: {
                color: '#22E8E8',
              },
            },
            data: this.chartsData.map((item) => item.value2),
            label: {
              show: false,
              position: 'top',
              textStyle: {
                color: '#fff',
                fontSize: 28,
              },
            },
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.zdwxyEcharts {
  width: 1936px;
  height: 488px;
  margin-top: 10px;
}
</style>
