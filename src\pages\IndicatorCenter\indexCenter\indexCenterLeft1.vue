<!--指标中心左侧页面-->
<template>
  <div class="leftContainer">
    <ktitle :titleText="'指标汇聚情况'"></ktitle>
    <div class="container1">
      <div v-for="(item,i) in indexs" :key="i">
        <div class="dashName">{{item.name}}</div>
        <dashboard :id="i + ''" :value="item.value"></dashboard>
      </div>
    </div>
    <ktitle :titleText="'党建统领'" style="margin-top: 52px" @titleClick="$router.push('/PartyBuildingLeader')"></ktitle>
    <div class="container2">
      <div class="box1">
        <div class="politicalItem" v-for="(item,i) in politicals" :key="i">
          <div class="item-left" :style="{backgroundImage:'url('+item.icon+')'}"></div>
          <div class="item-right">
            <div class="name">{{item.name}}</div>
            <div class="value">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
          </div>
        </div>
      </div>
      <div class="box2">
        <div class="box-item">
          <div style="display: flex">
            <div class="item-left" :style="{backgroundImage:'url('+numicon+')'}"></div>
            <div class="item-right">
              <div class="name">志愿服务参与人数</div>
              <div class="value">{{num}} <span class="unit">人</span></div>
            </div>
          </div>
        </div>
        <div class="box-item2">
          <pieProgress :id="1" :num="num1" :width="'161'" :height="'161'" :fontSize="'25'" :lineBackground="'#005571'" :is-percent="false"></pieProgress>
          <div class="item-right">
            <div class="name">志愿者注册总人数</div>
            <div class="value">{{num1}} <span class="unit">人</span></div>
          </div>
        </div>
        <div class="box-item3">
          <pieProgress :id="2" :num="num2" :width="'161'" :height="'161'" :fontSize="'25'" :line-color="['#FFC460','#FFC460']" :text-color="'#FFC460'"  :lineBackground="'#654103'" :is-percent="false"></pieProgress>
          <div class="item-right">
            <div class="name">服务总时长</div>
            <div class="value gold">{{num2}} <span class="unit">人</span></div>
          </div>
        </div>
      </div>
    </div>
    <div class="organism">
      <div class="or-container">
        <ktitle :titleText="'经济生态'" :width="'939.6'" @titleClick="$router.push('/EconomicalEcology')"></ktitle>
        <div class="btns">
          <div class="btn" v-for="(item,i) in ['宏观经济','公共收支','市场主体','运行监测']" :key="i" :class="{active:choose == i}" @click="choose = i">{{item}}</div>
        </div>
        <MacroEconomy v-show="choose === 0"></MacroEconomy>
        <PublicRevenueAndExpenditure v-show="choose === 1"></PublicRevenueAndExpenditure>
        <MarketSubject v-show="choose === 2"></MarketSubject>
        <OperationMonitoring v-show="choose === 3"></OperationMonitoring>
      </div>
      <div class="or-container">
        <ktitle :titleText="'生态环境'" :width="'939.6'"></ktitle>
        <Environment></Environment>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import dashboard from "@/components/Dashboard";
import pieProgress from "@/components/progress";
import MacroEconomy from "@/pages/IndicatorCenter/indexCenter/leftComponents/MacroEconomy";
import PublicRevenueAndExpenditure from "@/pages/IndicatorCenter/indexCenter/leftComponents/PublicRevenueAndExpenditure";
import MarketSubject from "@/pages/IndicatorCenter/indexCenter/leftComponents/MarketSubject";
import OperationMonitoring from "@/pages/IndicatorCenter/indexCenter/leftComponents/OperationMonitoring";
import Environment from "@/pages/IndicatorCenter/indexCenter/leftComponents/Environment";
export default {
  name: "indexCenterLeft",
  data() {
    return {
      indexs:[
        {
          name:"部门数量",
          value:21
        },
        {
          name:"大类数量",
          value:29
        },
        {
          name:"小类数量",
          value:100
        }
      ], //仪表盘数据
      politicals:[
        {
          name:"党员总数",
          value:"19074",
          icon:require("@/assets/home/<USER>/political1.png"),
          unit:"人"
        },
        {
          name:"基层党组织",
          value:"981",
          icon:require("@/assets/home/<USER>/political2.png"),
          unit:"个"
        },
        {
          name:"本年发展党员",
          value:"134",
          icon:require("@/assets/home/<USER>/political3.png"),
          unit:"人"
        },
        {
          name:"团员总数",
          value:"7715",
          icon:require("@/assets/home/<USER>/political4.png"),
          unit:"人"
        },
        {
          name:"团支部",
          value:"728",
          icon:require("@/assets/home/<USER>/political5.png"),
          unit:"个"
        },
        {
          name:"主题日活动",
          value:"1256",
          icon:require("@/assets/home/<USER>/political6.png"),
          unit:"个"
        },
        {
          name:"志愿服务队数量",
          value:"365",
          icon:require("@/assets/home/<USER>/political7.png"),
          unit:"件"
        },
        {
          name:"志愿服务类型数量",
          value:"12",
          icon:require("@/assets/home/<USER>/political8.png"),
          unit:"小时"
        },
      ], //党建统领数据
      num:"63710", //志愿服务参与人数
      numicon:require('@/assets/home/<USER>/political9.png'), //志愿服务参与人数icon
      num1:67771, //志愿者注册总人数
      num2:"4785983.4", //服务总时长
      choose:0, //经济生态默认选中
    }
  },
  components:{
    ktitle,
    dashboard,
    pieProgress,
    MacroEconomy,
    PublicRevenueAndExpenditure,
    MarketSubject,
    OperationMonitoring,
    Environment
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.gold {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff) !important;
  -webkit-background-clip: text !important;
  color: transparent !important;
  text-align: left !important;
}
.item-left {
  width: 129px;
  height: 105px;
}
.unit {
  font-size: 30px;
  //margin: 0 0 0 -10px;
}
.item-right {
  margin-left: 33px;
  .name {
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #FFFFFF;
  }
  .value {
    font-size: 50px;
    font-family: BN;
    font-weight: 500;
    color: #9AA9BF;
    background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.leftContainer {
    margin: 80px 0 0 68px;
    .container1 {
      width: 1936px;
      height: 220.5px;
      margin-top: 52px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .dashName {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        text-align: center;
        margin-bottom: 15px;
      }
    }
    .container2 {
      width: 1936px;
      height: 515px;
      margin-top: 49px;
      .box1 {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-wrap: wrap;
        height: 248px;
        .politicalItem {
          width: 424px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
        }
      }
      .box2 {
        width: 1851px;
        height: 220px;
        background: url("@/assets/home/<USER>/boxbg.png");
        background-size: cover;
        margin: 47px 0 0 50px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .box-item {
          width: 570px;
          height: 220px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .box-item2 {
          width: 600px;
          height: 220px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .box-item3 {
          width: 680px;
          height: 220px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .organism {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .or-container {
        width: 940px;
        height: 680px;
        margin-top: 49px;
        .btns {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-wrap: wrap;
          .btn {
            cursor: pointer;
            width: 256px;
            height: 71px;
            background-size: cover;
            background: url("@/assets/home/<USER>");
            text-align: center;
            line-height: 71px;
            font-size: 32px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #D6E7F9;
            margin: 22px 0 0 23px;
          }
          .active {
            background: url("@/assets/home/<USER>");
          }
        }
      }
    }
  }
</style>