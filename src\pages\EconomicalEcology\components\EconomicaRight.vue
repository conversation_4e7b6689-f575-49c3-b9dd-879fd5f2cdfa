<template>
  <div class="innerRight">
    <Wrapbox bg="right">
      <div class="rightWrapper animate__animated animate__fadeInRight">
        <div class="rightTop">
          <Ktitle titleText="市场主体" />
          <div class="topContainer">
            <div class="topInner">
              <div class="topLeft">
                <TitleTwo :title-text="'市场主体总量'" />
                <div class="chartsWrap_l">
                  <PieChart ref="pieChart" />
                  <div class="pieTitle">
                    <div class="numTxt">
                      <div class="nu">59444</div>
                      <div class="un">户</div>
                    </div>
                    <div class="textClass">市场主体总量</div>
                  </div>
                </div>
              </div>
              <div class="topRight">
                <TitleTwo :title-text="'注册资本总额'" />
                <div class="chartsWrap_r">
                  <ColumnarChart ref="columnarChart" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="rightBot">
          <Ktitle titleText="市场监测" />
          <div class="botWrap">
            <div class="botInner">
              <div class="topInner">
                <div class="topLeft">
                  <TitleTwo :title-text="'进出口贸易'" />
                  <div class="jckmyWrap_l">
                    <div class="jckmyOne">
                      <ToText :lineOneType="1" :numType="1" :unitType="1" :text="'对外出口总额'" :num="'29352'"
                        :unit="'万元'" />
                      <div class="dtClass"></div>
                    </div>
                    <div class="jckmyTwo">
                      <div class="topTxt">
                        <div class="topTxt_O">服务贸易进出口总额</div>
                        <div class="topTxt_T gold">
                          <div class="topTxt_S">29938</div>
                          <div class="topTxt_F">万元</div>
                        </div>
                      </div>
                      <div class="progressWrap">
                        <el-progress :define-back-color="'#654103'" :stroke-width="16" :width="160" type="circle"
                          :percentage="75" :color="'#FFC460'"></el-progress>
                      </div>
                      <div class="jckzsClass">进出口增速</div>
                    </div>
                    <div class="jckmyThree">
                      <ToText :lineOneType="1" :numType="3" :unitType="3" :text="'对外进口总额'" :num="'35548'"
                        :unit="'万元'" />
                      <div class="dtClassTwo"></div>
                    </div>
                  </div>
                </div>
                <div class="topRight">
                  <TitleTwo :title-text="'粮食'" />
                  <div class="lsWrap_r">
                    <div class="lsbox">
                      <ToText :lineOneType="1" :numType="3" :unitType="3" :text="'粮食生产规模'" :num="'88200'" :unit="'亩'" />
                      <div class="lsImgClass"></div>
                    </div>
                    <div class="lsbox">
                      <ToText :lineOneType="1" :numType="3" :unitType="3" :text="'粮食产量'" :num="'8500'" :unit="'吨'" />
                      <div class="lsImgClass"></div>
                    </div>
                    <div class="lsbox">
                      <ToText :lineOneType="1" :numType="1" :unitType="1" :text="'生猪养殖规模'" :num="'104400'" :unit="'头'" />
                      <div class="lsImgClassTwo"></div>
                    </div>
                    <div class="lsbox">
                      <ToText :lineOneType="1" :numType="1" :unitType="1" :text="'猪肉产量'" :num="'2800'" :unit="'吨'" />
                      <div class="lsImgClassTwo"></div>
                    </div>
                    <div class="lsbox">
                      <ToText :lineOneType="1" :numType="5" :unitType="2" :text="'食品企业数量'" :num="'7'" :unit="'家'" />
                      <div class="lsImgClassThree"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="botInner">
              <div class="topInner">
                <div class="topLeft">
                  <TitleTwo :title-text="'网络零售额'" />
                  <div class="wlls_l">
                    <WllsChart ref="wllsChart" />
                  </div>
                </div>
                <div class="topRight">
                  <TitleTwo :title-text="'工地智控'" />
                  <div class="gdzk_r">
                    <div class="gdzkBox">
                      <div class="gdzkBoxInner">
                        <img :src="require('@/assets/economical/gdzkO.png')" class="imgClass">
                        <ToText :lineOneType="3" :numType="4" :unitType="1" :text="'工业用地宗数'" :num="'871'" :unit="'宗'" />
                      </div>
                    </div>
                    <div class="gdzkBox">
                      <div class="gdzkBoxInner">
                        <img :src="require('@/assets/economical/gdzkT.png')" class="imgClass">
                        <ToText :lineOneType="3" :numType="4" :unitType="1" :text="'工业用地面积'" :num="'2.83'" :unit="'万亩'" />
                      </div>
                    </div>
                    <div class="gdzkBox">
                      <div class="gdzkBoxInner">
                        <img :src="require('@/assets/economical/gdzkS.png')" class="imgClass">
                        <ToText :lineOneType="3" :text="'低效用地宗数'" :num="'589'" :unit="'宗'" />
                      </div>
                    </div>
                    <div class="gdzkBox">
                      <div class="gdzkBoxInner">
                        <img :src="require('@/assets/economical/gdzkF.png')" class="imgClass">
                        <ToText :lineOneType="3" :text="'低效用地面积'" :num="'1.45'" :unit="'万亩'" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Wrapbox>
  </div>
</template>

<script>
import Wrapbox from "@/components/wrapbox.vue"
import Ktitle from "@/components/title.vue"
import TitleTwo from "@/components/titleTwo.vue"
import PieChart from "@/pages/EconomicalEcology/components/chartsComponents/PieChart.vue"
import ColumnarChart from "@/pages/EconomicalEcology/components/chartsComponents/ColumnarChart.vue"
import WllsChart from "@/pages/EconomicalEcology/components/chartsComponents/WllsChart.vue"
import ToText from "@/components/ToText.vue"


export default {
  components: {
    Wrapbox,
    Ktitle,
    TitleTwo,
    PieChart,
    ColumnarChart,
    WllsChart,
    ToText
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.innerRight {
  .rightWrapper {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 2fr;

    .rightTop {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 50px 0 0 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      // background-color: aquamarine;
      .topContainer {
        flex: 1;
        width: 100%;
        box-sizing: border-box;
        padding: 12px 75px 0px 75px;
      }

    }

    .rightBot {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 50px 0 0 0;
      display: flex;
      flex-direction: column;
      align-items: center;

      .botWrap {
        flex: 1;
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;

        .botInner {
          width: 100%;
          height: 100%;
          // background-color: #FFC460;
          box-sizing: border-box;
          padding: 12px 75px 0px 75px;

        }
      }
    }
  }
}



.topInner {
  width: 100%;
  height: 100%;
  display: flex;

  .topLeft {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;

    // background-color: antiquewhite;

    .chartsWrap_l {
      flex: 1;
      width: 100%;
      position: relative;

      // background-color: antiquewhite;

      .pieTitle {
        position: absolute;
        // background-color: aqua;
        top: 46%;
        left: 23%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;

        .numTxt {
          font-family: Bebas Neue;
          font-weight: bolder;
          color: #9AA9BF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: flex;
          align-items: baseline;

          .nu {
            font-size: 60px;
            margin-right: 5px;
          }

          .un {
            font-size: 32px;


          }
        }

        .textClass {
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #E3EDFF;

        }
      }
    }

    .jckmyWrap_l {
      flex: 1;
      width: 100%;
      margin-top: 14px;
      padding: 0 16px;
      box-sizing: border-box;
      // background-color: antiquewhite;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-column-gap: 20px;
      grid-row-gap: 18px;

      .jckmyOne {
        grid-column-start: 1;
        grid-column-end: 2;
        background: url('~@/assets/economical/jxThree.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 21px 28px 0 28px;
      }

      .jckmyTwo {
        grid-column-start: 2;
        grid-column-end: 3;
        grid-row-start: 1;
        grid-row-end: 3;
        background: url('~@/assets/economical/jxThree.png') no-repeat center center;
        background-size: 100% 100%;
        box-sizing: border-box;
        padding-top: 67px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .topTxt {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 30px;

          .topTxt_O {
            font-size: 30px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #FFFFFF;
          }

          .topTxt_T {
            display: flex;
            align-items: baseline;
            font-weight: bolder;
            // margin-top: 20px;

            .topTxt_S {
              font-size: 60px;
            }

            .topTxt_F {
              font-size: 32px;
            }
          }
        }

        .progressWrap {
          // ::v-deep .el-progress__text {
          //   height: 161px !important;
          //   width: 161px !important;
          // }

          ::v-deep .el-progress__text {
            font-size: 50px !important;

            background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFFFFF 53.0029296875%, #FFC460 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
          }
        }

        .jckzsClass {
          font-size: 30px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #FFFFFF;
          margin-top: 16px;
        }
      }

      .jckmyThree {
        grid-column-start: 1;
        grid-column-end: 2;
        grid-row-start: 2;
        grid-row-end: 3;
        background: url('~@/assets/economical/jxThree.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 21px 28px 0 28px;
      }

      .dtClass {
        flex: 1;
        width: 100%;
        margin-top: 27px;
        background: url('~@/assets/economical/zztOne.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .dtClassTwo {
        flex: 1;
        width: 100%;
        margin-top: 27px;
        background: url('~@/assets/economical/zzTwo.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }

    .wlls_l {
      flex: 1;
      width: 100%;
    }

  }

  .topRight {
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    box-sizing: border-box;
    // background-color: aquamarine;

    .chartsWrap_r {
      flex: 1;
      width: 100%;
      // background-color: coral;
    }

    .lsWrap_r {
      flex: 1;
      width: 100%;
      margin-top: 14px;
      padding: 0 16px;
      box-sizing: border-box;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr 1fr;
      grid-gap: 5px;
      // background-color: #FFC460;

      .lsbox {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        padding: 12px 35px 0px 35px;
        background: url('~@/assets/economical/jxThree.png') no-repeat center center;
        background-size: 100% 100%;

        .lsImgClass {
          flex: 1;
          width: 100%;
          background: url('~@/assets/economical/sy.png') no-repeat center center;
          background-size: 100% 100%;

        }

        .lsImgClassTwo {
          flex: 1;
          width: 100%;
          background: url('~@/assets/economical/tel.png') no-repeat center center;
          background-size: 100% 100%;

        }

        .lsImgClassThree {
          flex: 1;
          width: 100%;
          background: url('~@/assets/economical/ss.png') no-repeat center center;
          background-size: 100% 100%;

        }
      }
    }

    .gdzk_r {
      flex: 1;
      width: 100%;
      margin-top: 45px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      // background-color: coral;


      .gdzkBox {
        width: 100%;
        height: 100%;
        // background-color: #FFC460;

        .gdzkBoxInner {
          display: flex;
          align-items: center;

          .imgClass {
            width: 129px;
            height: 116px;
            margin-right: 33px;
          }
        }


      }
    }
  }
}
</style>