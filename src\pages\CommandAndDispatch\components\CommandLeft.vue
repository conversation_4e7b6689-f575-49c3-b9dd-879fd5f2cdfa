<template>
  <div class="CommandLeftContainer">
    <div class="CommandLeftContainer-left">
      <!--指挥体系-->
      <zhtx></zhtx>
      <!--舆情信息-->
      <yqxx style="margin-top: 37px;"></yqxx>
    </div>
    <div class="CommandLeftContainer-right">
      <!--重大任务-->
      <zdrw></zdrw>
      <!--知识库-->
      <fzfx style="margin-top: 71px;"></fzfx>
    </div>
  </div>
</template>

<script>
import zhtx from "@/pages/CommandAndDispatch/components/LeftComponents/zhtx";
import yqxx from "@/pages/CommandAndDispatch/components/LeftComponents/yqxx";
import zdrw from "@/pages/CommandAndDispatch/components/LeftComponents/zdrw";
import fzfx from "@/pages/CommandAndDispatch/components/LeftComponents/fzfx";
export default {
  name: "CommandLeft",
  data() {
    return {

    }
  },
  components: {
    zhtx,
    zdrw,
    yqxx,
    fzfx
  },
  computed: {},
  mounted() {

  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang="less">
  .CommandLeftContainer {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .CommandLeftContainer-left,.CommandLeftContainer-right {
      width: 942px;
      height: 1774px;
      margin-top: 67px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
    }
  }
</style>