<template>
  <div class="eventSource">
     <div id="sjlyEcharts"></div>
  </div>
</template>

<script>
export default {
  name: "eventSource",
  data() {
    return {
      charts2Data:[
        {name:"市政建设",value:1200},
        {name:"平安综治",value:1200},
        {name:"教育文旅",value:1200},
        {name:"安全应急",value:1200},
        {name:"卫生健康",value:1200},
      ],
     }
  },
  computed: {},
  mounted() {
    this.initCharts2()
  },
  methods: {
    initCharts2() {
      let that = this
      let myChart = this.$echarts.init(document.getElementById("sjlyEcharts"));
      let imgUrl = require('@/assets/common/piebg.png')
      let option = {
        color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '48%',
          top: '14%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 30,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15]
              },
              value: {
                fontSize: 25,
                color: '#FFC460',
                // padding: [10, 0, 0, 15]
              },
            }
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total;
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '}{value|' + p + '%}'
          },
        },
        graphic: [
          {
            type: "image",
            id: "logo",
            left: "10.7%",
            top: "11.4%",
            z: -10,
            bounding: "raw",
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.8, 0.8], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '45%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: false,
            },
            data: this.charts2Data,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .eventSource {
    margin-top: 22px;
    #sjlyEcharts{
      width: 940px;
      height: 400px;
    }
  }
</style>