<template>
  <div id="qyfzChart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: "汽车类",
          value: 28.6
        },
        {
          name: "石油及制品类",
          value: 10.1
        },
        {
          name: "粮油制品类",
          value: 5.9
        },
        {
          name: "家用电器和影像器材",
          value: 2.9
        },
        {
          name: "服装、鞋帽、针纺织品类",
          value: 2.7
        }
      ],
      myChart:undefined
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      this.myChart = this.$echarts.init(document.getElementById("qyfzChart"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: '{b}: <br/> {c}亿元',
        },
        grid: {
          top: "8%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            inside: true,
            textStyle: {
              color: "#fff",
              verticalAlign: "bottom",
              align: "left",
              padding: [0, 0, 5, 0],
              fontSize: 46
            },
            formatter: function (value, index) {
              if (index == 0) {
                return '{titleOne|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 1) {
                return '{titleTwo|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 2) {
                return '{titleThree|TOP' + (index + 1) + '}' + '  ' + value
              } else {
                return '{title|TOP' + (index + 1) + '}' + '  ' + value
              }
            },
            rich: {
              titleOne: {
                width: 80,
                align: 'left',
                color: '#FF4949',
                fontSize: 32,
                fontWeight: '500'
              },
              titleTwo: {
                width: 80,
                align: 'left',
                color: '#DF8F30',
                fontSize: 32,
                fontWeight: '500'
              },
              titleThree: {
                width: 80,
                align: 'left',
                color: '#B76FD8',
                fontSize: 32,
                fontWeight: '500'
              },
              title: {
                width: 80,
                align: 'left',
                color: '#30B8D5',
                fontSize: 32,
                fontWeight: '500'
              }
            }
          },
          data: this.chartsData.map(item => item.name),
        },
        series: [
          {
            // cursor:"auto",
            type: "bar",
            name: "",
            showBackground: true,
            backgroundStyle: {
              color: '#2E3F54'
            },
            itemStyle: {
              barBorderRadius: [0, 10, 10, 0],
              color: function (params) {
                var colors = [
                  "#4587E7",
                  "#35AB33",
                  "#F5AD1D",
                  "#ff7f50",
                  "#da70d6",
                  "#32cd32",
                  "#6495ed",
                ]
                // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                if (params.dataIndex == 0) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FF9B78", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#FF4949", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else if (params.dataIndex == 1) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FAFF78", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#DF8F30", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else if (params.dataIndex == 2) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#E2B8F6", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#B76FD8", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#2BDAFF", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#078FF7", //指100%处的颜色
                    },
                    ],
                    false
                  )
                }
                // return colors[params.dataIndex];
              },
            },
            label: {
              show: true,
              position: [1150, -40],
              color: "#fff",
              formatter: function (params) {
                return params.value + '亿元'
              },
              fontSize: 43,
              textStyle: {
                color: '#B5DAFF',
                fontFamily: 'Source Han Sans CN'
              }
            },
            barWidth: 8,
            color: "#539FF7",
            data: this.chartsData.map(item => item.value),
          }, {
            name: "",
            type: "bar",
            barWidth: 8,
            barGap: "-100%",
            data: this.chartsData.map(item => 30),
            itemStyle: {
              normal: {
                color: "#2E5171",
              },
            },
            z: 0,
          }]
      }

      this.myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
#qyfzChart {
  width: 100%;
  height: 100%;
}
</style>