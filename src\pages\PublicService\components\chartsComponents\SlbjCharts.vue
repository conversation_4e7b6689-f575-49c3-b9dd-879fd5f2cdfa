<!--案件趋势-->
<template>
  <div id="slbjChart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: "1月",
          value1: 23,
          value2: 18,
        },
        {
          name: "2月",
          value1: 17,
          value2: 16,
        },
        {
          name: "3月",
          value1: 16,
          value2: 14,
        },
        {
          name: "4月",
          value1: 24,
          value2: 19,
        },
        {
          name: "5月",
          value1: 18,
          value2: 16,
        },
        {
          name: "6月",
          value1: 17,
          value2: 14,
        },
        {
          name: "7月",
          value1: 17,
          value2: 14,
        },
        {
          name: "8月",
          value1: 18,
          value2: 16,
        },
        {
          name: "9月",
          value1: 16,
          value2: 14,
        },
        {
          name: "10月",
          value1: 23,
          value2: 18,
        },
        {
          name: "11月",
          value1: 17,
          value2: 14,
        },
        {
          name: "12月",
          value1: 18,
          value2: 16,
        },
      ]
    }
  },
  mounted () {
    this.initChart()
  },
  methods: {
    initChart () {
      let myChart = this.$echarts.init(document.getElementById("slbjChart"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          icon: "circle",
          itemGap: 45,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
          // right: 120,
          top: 90
        },
        grid: {
          left: "2%",
          right: "6%",
          top: "30%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => (item.name)),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
                padding: [8, 0, 0, 0],
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：件",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 30,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",

              },
            },
          },
        ],
        series: [
          {
            name: "8890受理数",
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00C0FF",
                  },
                  {
                    offset: 0.5,
                    color: "#055880",
                  },
                  {
                    offset: 1,
                    color: "#0A3157",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value1),
          }, {
            name: "8890办结数",
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FFC460",
                  },
                  {
                    offset: 0.5,
                    color: "#5C503E",
                  },
                  {
                    offset: 1,
                    color: "#0A3157",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value2),
          }
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  }
}
</script>

<style scoped lang="less">
#slbjChart {
  width: 100%;
  height: 100%;
}
</style>