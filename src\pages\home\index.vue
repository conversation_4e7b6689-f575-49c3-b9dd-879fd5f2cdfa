<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <homeleft @titleClick="titleClick" class="animate__animated animate__fadeInLeft"></homeleft>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <homeright class="animate__animated animate__fadeInRight"></homeright>
      </wrapbox>
    </div>
    <div class="animate__animated animate__fadeInUp midBottom">
      <MidBottom />
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import homeleft from "@/pages/home/<USER>/left"
import homeright from "@/pages/home/<USER>/right"
import MidBottom from "@/pages/home/<USER>/midBottom/MidBottom.vue"
import layout from "@/layouts/mainLayout/index"
import {MapInitTimer} from "@/utils";
export default {
  name: 'index',
  components: {
    wrapbox,
    homeleft,
    layout,
    homeright,
    MidBottom
  },
  data () {
    return {
      initTimer: null
    }
  },
  mounted () {
    //保证地图初始化完成后调用
    this.initTimer = MapInitTimer(this.FlyToCenter.bind(this));
  },
  computed: {},
  methods: {
    titleClick (val) {
      this.childPage = val
    },
    FlyToCenter() {
      top.mapUtil.flyTo({
        destination: [119.638389, 29.087509],//飞行中心点
        zoom: 17,//飞行层级
        pitch: 35, //倾斜角
      })
    }
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.initTimer) {
      clearInterval(this.initTimer);
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;

  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;

    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }

    .spsbBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;

    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }

    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .midBottom {
    position: absolute;
    left: 2210px;
    bottom: 66px;
    z-index: 2;
  }
}
</style>
