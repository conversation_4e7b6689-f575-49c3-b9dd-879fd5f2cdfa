<template>
  <div class="csaq">
    <div class="item" v-for="(item, i) in data0" :key="i">
      <img :src="item.icon" alt="">
      <div>
        <div>{{ item.name }}</div>
        <div>
          <div>{{ item.value }}</div>
          <div>{{ item.dw }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "csaq",
  data() {
    return {
      data0: [
        { name: "应急场所", value: 163, dw: "个", icon: require("@/assets/home/<USER>") },
        { name: "应急设备", value: 152511, dw: "个", icon: require("@/assets/home/<USER>") },
        { name: "应急队伍", value: 25, dw: "支", icon: require("@/assets/home/<USER>") },
        { name: "危化企业", value: 15, dw: "家", icon: require("@/assets/home/<USER>") },
        { name: "消防队伍", value: 14, dw: "支", icon: require("@/assets/home/<USER>") },
        { name: "消防车辆", value: 11, dw: "辆", icon: require("@/assets/home/<USER>") },
        { name: "消防人员", value: 261, dw: "人", icon: require("@/assets/home/<USER>") },
        { name: "工业企业安全隐患数", value: 177248, dw: "件", icon: require("@/assets/home/<USER>") },
      ]
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
  },
  watch: {}
}
</script>

<style scoped lang="less">
.csaq {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  .item {
    display: flex;
    align-items: center;
    >img {
      width: 152px;
      height: 141px;
    }

    >div {
      width: 132px;
      margin-left: 25px;
      margin-right: 206px;

      >div:first-child {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
      }
      >div:nth-child(2){
        display: flex;
        align-items: baseline;
        >div:first-child{
          font-size: 60px;
font-family: BN;
font-weight: 400;
color: #9AA9BF;

background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
        }
        >div:last-child{
          font-size: 32px;
font-family: BN;
font-weight: 400;
color: #9AA9BF;

background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
        }
      }
    }
  }
}</style>