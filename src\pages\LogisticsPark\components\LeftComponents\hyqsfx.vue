<template>
  <div>
    <Ktitle :titleText="'货运趋势TOP5'" width="939.6" :show-date="false" />
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts3">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="charts13" id="charts13"></div>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
export default {
  name: "hyqsfx",
  components: {
    Ktitle
  },
  data() {
    return {
      value:1, //各类预警默认
      options:[
        {label:"园区",value:1},
      ], //各类预警下拉列表
      charts2Data: []
    }
  },
  computed: {},
  mounted() {
    this.initCharts3()
  },
  methods: {
    initCharts3() {
      this.charts2Data = [
        {
          name: "2024 10/2",
          value1: 140,
          value2: 20,
          value3: 33,
          value4: 42,
          value5: 78,
        },
        {
          name: "2024 10/3",
          value1: 40,
          value2: 120,
          value3: 54,
          value4: 34,
          value5: 68,
        },
        {
          name: "2024 10/4",
          value1: 38,
          value2: 100,
          value3: 41,
          value4: 68,
          value5: 94,
        },
        {
          name: "2024 10/5",
          value1: 77,
          value2: 77,
          value3: 54,
          value4: 45,
          value5: 23,
        },
        {
          name: "2024 10/6",
          value1: 130,
          value2: 50,
          value3: 64,
          value4: 34,
          value5: 56,
        },
        {
          name: "2024 10/7",
          value1: 38,
          value2: 90,
          value3: 76,
          value4: 34,
          value5: 65,
        },
        {
          name: "2024 10/8",
          value1: 110,
          value2: 60,
          value3: 23,
          value4: 54,
          value5: 87,
        },
      ]
      let myChart = this.$echarts.init(document.getElementById("charts13"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 14, // 设置宽度
          itemHeight: 14, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: "#D6E7F9",
            fontSize: 25,
          },
          left:"12%",
          top:"5%"
        },
        grid: {
          left: "0%",
          right: "0%",
          top: "34%",
          bottom: "2%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.charts2Data.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 24,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "运货量",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: [10,20,20,10],
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
          {
            name: "",
            type: "value",
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "模具城",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#00C0FF",
              },
            },
            data: this.charts2Data.map(item => item.value1),
          },
          {
            name: "美宝龙科技园",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#FFC460",
              },
            },
            data: this.charts2Data.map(item => item.value2),
          },
          {
            name: "晴天科技园",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#f82b06",
              },
            },
            data: this.charts2Data.map(item => item.value3),
          },
          {
            name: "金华亚泰小微园",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#cb27f8",
              },
            },
            data: this.charts2Data.map(item => item.value4),
          },
          {
            name: "金华置信产业园",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#3bec52",
              },
            },
            data: this.charts2Data.map(item => item.value5),
          }
        ]
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 870px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}
.charts13 {
  width: 931px;
  height: 380px;
  margin-top: 26px;
}
</style>