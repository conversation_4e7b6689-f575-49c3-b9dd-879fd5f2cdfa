<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <eventCenterLeft class="animate__animated animate__fadeInLeft"></eventCenterLeft>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <eventCenterRight class="animate__animated animate__fadeInRight"></eventCenterRight>
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import eventCenterLeft from "@/pages/EventCenter/eventCenter/eventCenterLeft"
import eventCenterRight from "@/pages/EventCenter/eventCenter/eventCenterRight"
export default {
  name: "index",
  data () {
    return {
      markerList: [
        {
          label: '事件A',
          value: 's01',
          statu: 0,
          lng: 119.65842342884746,
          lat: 29.07590877935061,
          imgType: 'gsjg'
        }, {
          label: '事件B',
          value: 's02',
          statu: 0,
          lng: 119.65042342884746,
          lat: 29.07890877935061,
          imgType: 'gsjg'
        }
      ]
    }
  },
  components: {
    eventCenterLeft,
    eventCenterRight,
    wrapbox
  },
  computed: {},
  mounted () {
    if (typeof (gis) == "undefined") {                                     // 解决事件中心刷新页面点位不显示的问题
      setTimeout(() => {
        // console.log('刷新后setTimeout中的gis', gis)
        this.$EventBus.$emit('eventDrawPoint', this.markerList)
      }, 2000)
    } else {
      // console.log('跳转后的gis', gis)
      this.$EventBus.$emit('eventDrawPoint', this.markerList)
    }
  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .midBottom {
    position: absolute;
    left: 2210px;
    bottom: 66px;
    z-index: 2;
  }
}
</style>