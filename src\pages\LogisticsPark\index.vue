<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <parkLeft class="animate__animated animate__fadeInLeft"></parkLeft>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <parkRight class="animate__animated animate__fadeInRight"></parkRight>
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import parkLeft from "@/pages/LogisticsPark/components/parkLeft";
import parkRight from "@/pages/LogisticsPark/components/parkRight";
import {MapInitTimer} from "@/utils";
export default {
  name: 'index',
  data() {
    return {
      timer: null,
      LogisticsParkPoints: [
        {"name":"中南高科金西创业园","lx":"生产制造业","address":"金西区块纬三路以北，经三路以西","lng":119.911577,"lat":29.192423,"zdcy":"金属制品","jzmj":49644.01,"yqlxr":"","phone":"","videoCode":33079953001320081717,"type":"logisticsPark"},
        {"name":"中盈小微园","lx":"生产制造业","address":"秋滨街道南二环西路3188号 ","lng":119.586651,"lat":29.045737,"zdcy":"五金交电、文化包装、电子商务","jzmj":141797.21,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"金华中盈科技发展有限公司","lx":"生产制造业","address":"南二环西路3188号a4号","lng":119.645586,"lat":29.079183,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":33079951001311083112,"type":"logisticsPark"},
        {"name":"金华市新嘉智能科技有限公司","lx":"生产制造业","address":"花溪路1859号","lng":119.645436,"lat":29.079483,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"浙江菁英电商管理服务有限公司","lx":"生产制造业","address":"仙华北街花溪路678号","lng":119.645811,"lat":29.079361,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"金华市智缘物业服务有限公司","lx":"生产制造业","address":"秋滨街道工业园区九峰街139号","lng":119.645623,"lat":29.079253,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"浙江北大科技园有限公司","lx":"生产制造业","address":"八达路66号","lng":119.642024,"lat":29.079272,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":33079956001311084219,"type":"logisticsPark"},
        {"name":"金华科技园创业服务中心有限公司","lx":"生产制造业","address":"西关街道李渔路1313号","lng":119.645345,"lat":29.079253,"zdcy":"","jzmj":1000,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"浙江菁英电子商务产业园","lx":"生产性服务类","address":"花溪路678号","lng":119.6168353,"lat":29.05886471,"zdcy":"电商类","jzmj":58000.00,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"物联科技大厦","lx":"生产制造业","address":"金衢路199号","lng":119.617255,"lat":29.076402,"zdcy":"","jzmj":100,"yqlxr":"龚文露","phone":13515794668,"videoCode":"","type":"logisticsPark"},
        {"name":"浙中仙华科创园","lx":"生产制造业","address":"龙潭路589号","lng":119.620026,"lat":29.051435,"zdcy":"","jzmj":100,"yqlxr":"魏渊","phone":13857998619,"videoCode":33079953001311085246,"type":"logisticsPark"},
        {"name":"金开金西智造园","lx":"生产制造业","address":"汤溪镇登云街918号","lng":119.415221,"lat":29.053599,"zdcy":"","jzmj":100,"yqlxr":"周国平","phone":13505867720,"videoCode":"","type":"logisticsPark"},
        {"name":"金华之心","lx":"生产制造业","address":"金华市婺城区李渔路1313号","lng":119.6349818,"lat":29.08154279,"zdcy":"","jzmj":100,"yqlxr":"魏渊","phone":13857998619,"videoCode":"","type":"logisticsPark"},
        {"name":"中谷文化产业园","lx":"生产性服务类","address":"宾虹西路161号","lng":119.561886,"lat":29.088315,"zdcy":"文化创意总部经济","jzmj":12458.00,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"金西机电科技产业园","lx":"生产制造业","address":"金西经济技术开发区","lng":119.377682,"lat":29.067656,"zdcy":"机电产品制造","jzmj":70405.45,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"金开现代智造园-金华中科孵化器有限公司","lx":"生产制造业","address":"科畅街168号","lng":119.596219,"lat":29.03495,"zdcy":"汽车零部件","jzmj":117558.06,"yqlxr":"周国平","phone":13505867720,"videoCode":"","type":"logisticsPark"},
        {"name":"浙中网络经济中心","lx":"生产性服务类","address":"四联路398号","lng":119.6400028,"lat":29.07781988,"zdcy":"软件和信息技术服务","jzmj":20923.84,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"模具城","lx":"生产制造业","address":"大盘街355号","lng":119.6124,"lat":29.079935,"zdcy":"专用设备制造业，新一代信息技术产业","jzmj":86925.00,"yqlxr":"陈烈","phone":13967478106,"videoCode":"","type":"logisticsPark"},
        {"name":"浙中仙华科创园","lx":"生产制造业","address":"龙潭路589号","lng":119.620026,"lat":29.051435,"zdcy":"软件和信息技术服务","jzmj":20923.84,"yqlxr":"","phone":"","videoCode":33079956001320080303,"type":"logisticsPark"},
        {"name":"金华市新嘉智创园","lx":"生产制造业","address":"八达路以东、石城街以北","lng":120.114351,"lat":28.964498,"zdcy":"智能制造","jzmj":191901.07,"yqlxr":"","phone":"","videoCode":33079956001321086410,"type":"logisticsPark"},
        {"name":"金华北大科技园（二期）（未名科创大厦）","lx":"生产性服务类","address":"金华市婺城区八达路66号","lng":119.6160655,"lat":29.0676865,"zdcy":"软件和信息技术服务","jzmj":40000.00,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"美保龙科技园","lx":"生产性服务类","address":"永康街639号","lng":119.6385263,"lat":29.08054635,"zdcy":"数字科技","jzmj":13495.59,"yqlxr":"","phone":"","videoCode":"","type":"logisticsPark"},
        {"name":"晴天科技园","lx":"生产性服务类","address":"九峰街139号","lng":119.605043,"lat":29.06625,"zdcy":"新能源产业，新一代信息技术产业","jzmj":22203.50,"yqlxr":"","phone":"","videoCode":33079951001320080255,"type":"logisticsPark"},
        {"name":"金华亚泰小微园","lx":"生产性服务类","address":"永康街697号","lng":119.6392639,"lat":29.07863662,"zdcy":"软件和信息技术服务","jzmj":20064.62,"yqlxr":"","phone":"","videoCode":33079951001320081402,"type":"logisticsPark"},
        {"name":"金华置信产业园","lx":"生产性服务类","address":"仙华南街1099号","lng":119.62426,"lat":29.032268,"zdcy":"","jzmj":237885.04,"yqlxr":"刘朋朋","phone":17369700737,"videoCode":33079951001311082593,"type":"logisticsPark"}
      ],
      carPoints: [
        {"lx":"厢式货车","carNumber":"浙G88A56","hyl":"12吨","name":"刘明伟","phone":15205798856,"lng":119.38613,"lat":29.08665,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G66B78","hyl":"8吨","name":"李程欢","phone":13805791234,"lng":119.407,"lat":29.10132,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G33C90","hyl":"5吨","name":"王坚强","phone":18605797788,"lng":119.37108,"lat":29.04829,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G44D12","hyl":"20吨","name":"赵敏伟","phone":13905795566,"lng":119.38738,"lat":29.04015,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G22E34","hyl":"15吨","name":"刘洋","phone":15005799900,"lng":119.40065,"lat":29.01385,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G77F56","hyl":"9吨","name":"陈晨","phone":13605793344,"lng":119.63421,"lat":29.0611,"type":"car"},
        {"lx":"危险品运输车","carNumber":"浙G11G78","hyl":"3吨","name":"周海涛","phone":15305796677,"lng":119.66207,"lat":29.07757,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G55H90","hyl":"10吨","name":"吴磊","phone":18005792233,"lng":119.62548,"lat":29.07192,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G99J12","hyl":"7吨","name":"郑渊洁","phone":13505798877,"lng":119.60108,"lat":29.05589,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G33K34","hyl":"18吨","name":"孙浩飞","phone":15105794455,"lng":119.59639,"lat":29.06452,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G66L56","hyl":"11吨","name":"黄燕","phone":13705796688,"lng":119.60017,"lat":29.07446,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G22M78","hyl":"6吨","name":"郭凯","phone":15905799977,"lng":119.6056,"lat":29.04444,"type":"car"},
        {"lx":"危险品运输车","carNumber":"浙G88N90","hyl":"4吨","name":"徐静","phone":18105793322,"lng":119.65027,"lat":29.05783,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G55Q34","hyl":"16吨","name":"胡波","phone":15505797766,"lng":119.62254,"lat":29.06208,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G99R56","hyl":"13吨","name":"马丽","phone":13405795588,"lng":119.60931,"lat":29.05499,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G77S78","hyl":"22吨","name":"罗翔","phone":18205791122,"lng":119.64897,"lat":29.05621,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G33T12","hyl":"10吨","name":"谢明","phone":15605798899,"lng":119.60857,"lat":29.0208,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙A87654","hyl":"12吨","name":"张伟","phone":15934567890,"lng":119.58751,"lat":29.06763,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G98765","hyl":"8吨","name":"李天华","phone":13812345678,"lng":119.60107,"lat":29.04629,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙GC5432","hyl":"15吨","name":"王鹏","phone":18654321098,"lng":119.64003,"lat":29.09121,"type":"car"},
        {"lx":"牵引车","carNumber":"浙GD7543","hyl":"20吨","name":"赵敏","phone":13798765432,"lng":119.62487,"lat":29.01323,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙GE8654","hyl":"10吨","name":"刘鹏","phone":15267890123,"lng":119.62326,"lat":29.00037,"type":"car"},
        {"lx":"自卸车","carNumber":"浙G45078","hyl":"18吨","name":"陈伟","phone":13623456789,"lng":119.5942,"lat":28.99342,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G56239","hyl":"25吨","name":"周涛","phone":15109876543,"lng":119.38816,"lat":28.98431,"type":"car"},
        {"lx":"罐式货车","carNumber":"浙GT6720","hyl":"14吨","name":"吴磊","phone":18012345678,"lng":119.4196,"lat":28.95742,"type":"car"},
        {"lx":"平板货车","carNumber":"浙GU8901","hyl":"9吨","name":"孙敏伟","phone":13598764321,"lng":119.39376,"lat":28.99466,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G89012","hyl":"11吨","name":"郑悦","phone":15345678901,"lng":119.62768,"lat":29.07899,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G8A568","hyl":"12吨","name":"张辉","phone":15800134567,"lng":119.6181,"lat":29.044,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G2B876","hyl":"8吨","name":"李辉","phone":13567890123,"lng":119.63675,"lat":29.0447,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G5C346","hyl":"15吨","name":"王强","phone":18612345678,"lng":119.59778,"lat":29.01058,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G1D754","hyl":"20吨","name":"赵伟","phone":13987654321,"lng":119.42074,"lat":29.0457,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G6E865","hyl":"10吨","name":"刘伟","phone":15200001234,"lng":119.39024,"lat":29.0878,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G3F245","hyl":"18吨","name":"陈静","phone":13654321098,"lng":119.38735,"lat":29.0794,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G9G643","hyl":"9吨","name":"周帅","phone":15912345678,"lng":119.36172,"lat":29.03836,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G4H365","hyl":"13吨","name":"吴峰","phone":18001234567,"lng":119.35525,"lat":29.03414,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G7I481","hyl":"22吨","name":"郑洁","phone":13765432109,"lng":119.62362,"lat":29.08496,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G0J906","hyl":"11吨","name":"孙浩","phone":15098765432,"lng":119.59041,"lat":28.97872,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G1245A","hyl":"15吨","name":"张敏","phone":13800138000,"lng":119.61193,"lat":29.01804,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G2343B","hyl":"22吨","name":"李娜","phone":15900159001,"lng":119.65189,"lat":29.07422,"type":"car"},

      ]
    }
  },
  components: {
    wrapbox,
    parkLeft,
    parkRight,
  },
  computed: {},
  mounted() {
    //保证地图初始化完成后调用
    this.timer = MapInitTimer(this.loadPoints.bind(this));
  },
  methods: {
    //清除原有的所有图层
    cleanAllWarningLayers() {
      // 确保 top.mapUtil 和 top.mapUtil.layers 是可用的
      if (!top || !top.mapUtil || typeof top.mapUtil.layers !== 'object') {
        console.error('mapUtil or mapUtil.layers is not available');
        return;
      }

      // 使用常量定义正则表达式，以提高代码的可维护性
      const mapWarningLayerRegex = /WlyqMarkerArray/;

      // 获取所有层的键，并筛选出匹配 MapWarningList 的层
      const layerKeys = Object.keys(top.mapUtil.layers).filter(item => mapWarningLayerRegex.test(item));

      // 检查是否有匹配的层
      if (layerKeys.length === 0) {
        console.log('No warning layers found to clean');
        return;
      }

      // 安全地移除匹配的层
      try {
        top.mapUtil.removeAllLayers(layerKeys);
      } catch (error) {
        console.error('Failed to remove warning layers:', error);
      }
    },
    loadPoints() {
      this.$EventBus.$emit('CommonDrawPoint', this.LogisticsParkPoints,"园区点位WlyqMarkerArray",'/map/img/园区.png',true,true,false)
      this.$EventBus.$emit('CommonDrawPoint', this.carPoints,"货车点位WlyqMarkerArray",'/map/img/货车.png',true,true,false)
    },
  },
  watch: {},
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  destroyed() {
    this.cleanAllWarningLayers()
  }
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .spsbBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .midBottom {
    position: absolute;
    left: 2210px;
    bottom: 66px;
    z-index: 2;
  }
}
</style>
