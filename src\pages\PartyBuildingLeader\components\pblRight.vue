<template>
  <div class="PartyBuildingLeaderRight">
    <ktitle :title-text="'政务服务'"></ktitle>
    <services></services>
    <handling></handling>
    <handNums></handNums>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import services from "@/pages/PartyBuildingLeader/components/rightComponents/services";
import handling from "@/pages/PartyBuildingLeader/components/rightComponents/handling";
import handNums from "@/pages/PartyBuildingLeader/components/rightComponents/handNums";
export default {
  name: "pblLeft",
  data() {
    return {}
  },
  components: {
    ktitle,
    services,
    handling,
    handNums
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .PartyBuildingLeaderRight {
    margin: 80px 68px 0 68px;
  }
</style>