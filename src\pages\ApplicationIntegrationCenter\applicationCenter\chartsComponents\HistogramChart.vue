<template>
  <div id="histogramChart"></div>
</template>

<script>
export default {
  data () {
    return {
      charts2Data: [
        {
          name: '招商招才',
          value: 1,
        },
        {
          name: '党群工作部',
          value: 1,
        },
        {
          name: '党政综合部',
          value: 3,
        },
        {
          name: '国资监管中心',
          value: 1,
        },
        {
          name: '建设更新部',
          value: 2,
        },
        {
          name: '经济发展部',
          value: 5,
        },
        {
          name: '社会事务部',
          value: 3,
        },
        {
          name: '社会治理部',
          value: 4,
        },
        {
          name: '乡村振兴部',
          value: 2,
        },
        {
          name: '行政服务中心',
          value: 2,
        },
        {
          name: '宣传部',
          value: 1,
        },
        {
          name: '行政执法分局',
          value: 1,
        },
        {
          name: '公安局江南分局',
          value: 3,
        },
        {
          name: '三江街道',
          value: 1,
        },
        {
          name: '苏孟乡',
          value: 1,
        }
      ],
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initCharts2()
    })
  },
  methods: {
    initCharts2 () {
      let myChart = this.$echarts.init(document.getElementById('histogramChart'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow',
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: "{b0}: {c1}"
        },
        grid: {
          left: '5%',
          right: '0%',
          top: '10%',
          bottom: '0%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.charts2Data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate:45,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
              margin:26
            },
          },
          {
            type: 'category',
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            data: ['', '', '', '', '', '', ''],
          }

        ],
        yAxis: [
          {
            name: '单位：个数',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [0, 0, 10, 30],
              // padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          // {
          //   name: '',
          //   type: 'value',
          //   max: 100,
          //   nameTextStyle: {
          //     fontSize: 24,
          //     color: '#D6E7F9',
          //     padding: 5,
          //   },
          //   splitLine: {
          //     lineStyle: {
          //       color: 'rgb(119,179,241,.4)',
          //     },
          //   },
          //   axisLabel: {
          //     formatter: '{value}%',
          //     textStyle: {
          //       fontSize: 28,
          //       color: '#D6E7F9',
          //     },
          //   },
          // },
        ],
        series: [
          {
            name: '预警个数',
            type: 'bar',
            barWidth: 36,
            smooth: false,
            symbolSize: 10,
            xAxisIndex: 1,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#00C0FF',
                  },
                  {
                    offset: 1,
                    color: '#00C0FF',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.charts2Data.map((item) => item.value),
          },
          {
            type: 'bar',
            barWidth: '49',

            itemStyle: {
              normal: {
                show: true,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(119, 179, 241, .2)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(119, 179, 241, .2)',
                  },
                ]),
                barBorderRadius: 0,
                borderWidth: 0,
              },
            },

            data: [25, 25, 25, 25, 25, 25, 25],
          },

        ],
      }
      myChart.setOption(option)
    },
  }
}
</script>

<style scoped lang="less">
#histogramChart {
  width: 100%;
  height: 100%;
  // background-color: blueviolet;
}
</style>