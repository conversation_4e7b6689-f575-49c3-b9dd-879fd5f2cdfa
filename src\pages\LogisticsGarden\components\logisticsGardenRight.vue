<template>
  <div class="rightContainer">
    <div class="box1">
      <ktitle :titleText="'人员组成部分'"></ktitle>
      <PersonComprise></PersonComprise>
    </div>
    <div class="box2">
      <ktitle :titleText="'本年访客概况'"></ktitle>
      <visitorSurvey></visitorSurvey>
    </div>
    <div class="box3">
      <ktitle :titleText="'本月部门接待排行'"></ktitle>
      <departmentRank></departmentRank>
    </div>
  </div>
</template>

<script>
import ktitle from '@/components/title'
import PersonComprise from '@/pages/LogisticsGarden/components/rightComponents/PersonComprise' //人员组成部分
import visitorSurvey from '@/pages/LogisticsGarden/components/rightComponents/visitorSurvey' //本年访客概况
import departmentRank from '@/pages/LogisticsGarden/components/rightComponents/departmentRank' //本月部门接待排行
export default {
  name: 'eventCenterLeft',
  data() {
    return {}
  },
  components: {
    ktitle,
    PersonComprise,
    visitorSurvey,
    departmentRank,
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.rightContainer {
  margin: 80px 68px 0 68px;
  .box1 {
    height: 518px;
  }
  .box2 {
    height: 588px;
    margin-top: 30px;
  }
  .box3 {
    height: 560px;
    margin-top: 30px;
  }
}
</style>
