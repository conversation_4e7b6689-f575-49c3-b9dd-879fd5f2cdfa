const loginTab = [
  {
    path: '/login',
    name: '登录',
    component: () => import('@/pages/login/index'),
    meta: {
      title: '登录',
    },
  },
]
const tabRoutes = [
  {
    path: '/home',
    name: '首页',
    component: () => import('@/pages/home/<USER>'),
    meta: {
      title: '首页',
    },
  },
  {
    path: '/CommandAndDispatch',
    name: '指挥调度',
    component: () => import('@/pages/CommandAndDispatch/index'),
    meta: {
      title: '指挥调度',
    },
  },
  {
    path: '/IndicatorCenter',
    name: '指标中心',
    component: () => import('@/pages/IndicatorCenter/index'),
    meta: {
      title: '指标中心',
    },
  },
  {
    path: '/EventCenter',
    name: '事件中心',
    component: () => import('@/pages/EventCenter/index'),
    meta: {
      title: '事件中心',
    },
  },
  {
    path: '/EventTaskCenter',
    name: '事件中心',
    component: () => import('@/pages/EventTaskCenter/index'),
    meta: {
      title: '事件中心',
    },
  },
  {
    path: '/EarlyWarningCenter',
    name: '预警中心',
    component: () => import('@/pages/EarlyWarningCenter/index'),
    meta: {
      title: '预警中心',
    },
  },
  {
    path: '/ApplicationIntegrationCenter',
    name: '应用集成中心',
    component: () => import('@/pages/ApplicationIntegrationCenter/index'),
    meta: {
      title: '应用集成中心',
    },
  },
  {
    path: '/LogisticsGarden',
    name: '物流园区',
    component: () => import('@/pages/LogisticsGarden/index'),
    meta: {
      title: '物流园区',
    },
  },
  {
    path: '/SafeAndOrderly',
    name: '安全有序',
    component: () => import('@/pages/SafeAndOrderly/index'),
    meta: {
      title: '安全有序',
    },
  },
  {
    path: '/PartyBuildingLeader',
    name: '党建统领',
    component: () => import('@/pages/PartyBuildingLeader/index'),
    meta: {
      title: '党建统领',
    },
  },
  {
    path: '/EconomicalEcology',
    name: '经济生态',
    component: () => import('@/pages/EconomicalEcology/index'),
    meta: {
      title: '经济生态',
    },
  },
  {
    path: '/PublicService',
    name: '公共服务',
    component: () => import('@/pages/PublicService/index'),
    meta: {
      title: '公共服务',
    },
  },
  {
    path: '/LogisticsPark',
    name: '物流园区监测',
    component: () => import('@/pages/LogisticsPark/index'),
    meta: {
      title: '物流园区监测',
    },
  },
  {
    path: '/UrbanSigns',
    name: '城市体征',
    component: () => import('@/pages/UrbanSigns/index'),
    meta: {
      title: '城市体征',
    },
  },
  {
    path: '/qyzf',
    name: '企业智服',
    component: () => import('@/pages/qyzf/index'),
    meta: {
      title: '企业智服',
    },
  },
]
const options = {
  routes: [
    {
      path: '/',
      name: '登录',
      redirect: '/login',
      component: () => import('@/layouts/mainLayoutLogin'),
      children: loginTab,
      meta: {
        title: '登录',
      },
    },
    {
      path: '/home',
      name: '首页',
      redirect: '/home',
      component: () => import('@/layouts/mainLayout'),
      children: tabRoutes,
      meta: {
        title: '首页',
      },
    },
  ],
}
export { tabRoutes, options }
