<!--社会保障-->
<template>
  <div class="commonContainer">
    <Ktitle titleText="社会保障" />
    <div class="cardWrap">
      <div class="gdzkBoxInner">
        <img :src="require('@/assets/publicService/month.png')" class="imgClass">
        <ToText :lineOneType="3" :text="'低保当年发放金额'" :num="'629.37'" :unit="'万元'" />
      </div>
      <div class="gdzkBoxInner">
        <img :src="require('@/assets/publicService/year.png')" class="imgClass">
        <ToText :lineOneType="3" :text="'低保当月发放金额'" :num="'71.35'" :unit="'万元'" />
      </div>
      <div class="gdzkBoxInner">
        <img :src="require('@/assets/publicService/yl.png')" class="imgClass">
        <ToText :lineOneType="3" :text="'基本养老保险参保人数'" :num="'178521'" :unit="'人'" />
      </div>
    </div>
    <div class="chartsWrap">
      <div class="select">
        <el-select v-model="value" placeholder="请选择">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </div>
      <ShbzChart ref="shbzChart" />
    </div>
  </div>
</template>

<script>
import ToText from "@/components/ToText.vue"
import Ktitle from "@/components/title.vue"
import ShbzChart from "@/pages/PublicService/components/chartsComponents/ShbzChart.vue"
export default {
  name: "shbz",
  components: {
    Ktitle,
    ShbzChart,
    ToText
  },
  data() {
    return {
      value: 3,
      options: [
        { label: "年度", value: 1 },
        { label: "季度", value: 2 },
        { label: "月度", value: 3 },
      ],
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .commonContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .cardWrap {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 36px;
    margin-bottom: 70px;

    .gdzkBoxInner {
      display: flex;
      align-items: center;

      .imgClass {
        width: 130px;
        height: 110px;
        margin-right: 40px;
      }
    }
  }

  .chartsWrap {
    flex: 1;
    width: 100%;
    position: relative;

    // background-color: bisque;
    /deep/ .select {
      position: absolute;
      right: 96px;
      top: -20px;
      z-index: 999;

      .el-input__inner {
        width: 186px;
        height: 60px;
        border-radius: 40px;
        background: transparent;
        font-size: 30px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #FEFEFE;
      }
    }

  }
}
</style>