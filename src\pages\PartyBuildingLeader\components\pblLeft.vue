<template>
  <div class="PartyBuildingLeaderLeft">
    <ktitle :title-text="'党建发展'"></ktitle>
    <develop :filterTime="filterTime"></develop>
    <ktitle :title-text="'团建发展'"></ktitle>
    <team></team>
  </div>
</template>

<script>
import ktitle from "@/components/title"
import develop from "@/pages/PartyBuildingLeader/components/leftComponents/develop"
import team from "@/pages/PartyBuildingLeader/components/leftComponents/team"
export default {
  props: {
    filterTime: {
      type: String,
      default: ''
    }
  },
  data () {
    return {

    }
  },
  components: {
    ktitle,
    develop,
    team
  },
  created () {
  },
  mounted () {

  },
  methods: {}

}
</script>

<style scoped lang="less">
.PartyBuildingLeaderLeft {
  margin: 80px 68px 0 68px;
  overflow: hidden;
}
</style>