<template>
  <div class="shzlzxWrap">
    <div class="firstPart pd-1">
      <div class="firstPartInner">
        <div v-for="item in dataList" :key="item.id" class="wthCs">
          {{ item.name }}
        </div>
      </div>

    </div>
    <div class="secondPart mg-2">
      <div class="labelClass">本月数量</div>
      <div class="numWrap">
        <div class="numClass wthCs" v-for="item in dataList" :key="item.id">
          {{ item.value }}
        </div>
      </div>
    </div>
    <div class="secondPart mg-3">
      <div class="labelClass">总数</div>
      <div class="totalWrap">
        <div class="numClass wthCs" v-for="item in dataList" :key="item.id">
          {{ item.total }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  data () {
    return {
      dataList: [{
        id: 101,
        name: '信访接待',
        value: 24,
        total: 261
      }, {
        id: 102,
        name: '司法服务',
        value: 20,
        total: 61
      }, {
        id: 103,
        name: '社会心理',
        value: 31,
        total: 161
      }, {
        id: 104,
        name: '联合调处',
        value: 42,
        total: 330
      }, {
        id: 105,
        name: '劳动仲裁',
        value: 11,
        total: 146
      }, {
        id: 106,
        name: '公共法律',
        value: 44,
        total: 375
      }, {
        id: 107,
        name: '交通事故',
        value: 9,
        total: 96
      }, {
        id: 108,
        name: '医疗纠纷',
        value: 33,
        total: 255
      }, {
        id: 109,
        name: '退役军人',
        value: 28,
        total: 312
      }, {
        id: 110,
        name: '土地征收',
        value: 2,
        total: 134
      }, {
        id: 111,
        name: '民情民访待办',
        value: 17,
        total: 46
      }]
    }
  },
  computed: {
    total () {
      const sum = this.dataList.reduce((pre, current) => {
        return pre + current.value
      }, 0)
      return sum
    }
  }
}
</script>

<style lang="less" scoped>
.wthCs{
  width: 250px;
  display: flex;
  justify-content: center;
  align-items: center;
  // background-color: antiquewhite;
}
.pd-1 {
  padding-top: 52px;
  box-sizing: border-box;
}

.mg-2 {
  margin-top: 48px;
}

.mg-3 {
  margin-top: 10px;
}

.shzlzxWrap {
  width: 100%;
  height: 100%;
  background: url('~@/assets/common/nrk.png') no-repeat center center;
  background-size: 100% 100%;

  .firstPart {
    display: flex;
    justify-content: center;

    .firstPartInner {
      width: calc(100% - 205px);
      // height: 72px;
      // background: #078603;
      margin-left: 136px;
      display: flex;
      justify-content: space-around;
      // justify-content: space-evenly;
      align-items: center;
      font-size: 40px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #77b3f1;
    }
  }

  .secondPart {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;

    .labelClass {
      width: 88px;
      font-size: 40px;
      font-family: Source Han Sans CN;
      color: #77b3f1;
      letter-spacing: 4px;
    }

    .numWrap {
      width: calc(100% - 205px);
      height: 72px;
      background: #035b86;
      margin-left: 48px;
      font-size: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }

    .totalWrap {
      width: calc(100% - 205px);
      height: 72px;
      background: #0f2b4d;
      margin-left: 48px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
}
</style>