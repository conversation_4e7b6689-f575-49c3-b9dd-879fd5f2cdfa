<template>
  <div class="zdwxyEcharts" id="zdwxyEcharts"></div>
</template>

<script>
export default {
  name: "charts3",
  data() {
    return {
      chartsData: [
        {
          name: "1月", value: 4
        },
        {
          name: "2月", value: 7
        },
        {
          name: "3月", value: 4
        },
        {
          name: "4月", value: 7
        },
        {
          name: "5月", value: 9
        },
        {
          name: "6月", value: 6
        },
        {
          name: "7月", value: 3
        }
      ]
    }
  },
  computed: {},
  mounted() {
    // this.$nextTick(() => {
    //   this.getChart()
    // })
    this.$nextTick(()=>{
      var serveTBar = this.$echarts.init(document.getElementById('zdwxyEcharts'));
      serveTBar.setOption(this.getEcharts3DBar());
    })
  },
  methods: {
    // getChart() {
    //   let myChart = this.$echarts.init(document.getElementById("zdwxyEcharts"));
    //   let option = {
    //     tooltip: {
    //       trigger: "axis",
    //       borderWidth: 0,
    //       axisPointer: {
    //         // 坐标轴指示器，坐标轴触发有效
    //         type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
    //       },
    //       formatter: function (params) {
    //         var tar = params[0];
    //         return tar.name + ' : ' + tar.value;
    //       },
    //       backgroundColor: 'rgba(0, 0, 0, 0.6)',
    //       textStyle: {
    //         color: 'white',
    //         fontSize: '28',
    //       },
    //     },
    //     // legend: {
    //     //   icon: 'roundRect', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
    //     //   itemWidth: 14, // 设置宽度
    //     //   itemHeight: 14, // 设置高度
    //     //   itemGap: 36, // 设置间距
    //     //   left: 170,
    //     //   top: 5,
    //     //   // icon: "circle",
    //     //   textStyle: {
    //     //     color: "#D6E7F9",
    //     //     fontSize: 16,
    //     //   },
    //     // },
    //     grid: {
    //       left: "4%",
    //       right: "10%",
    //       top: "20%",
    //       bottom: "1%",
    //       containLabel: true,
    //     },
    //     xAxis: [
    //       {
    //         type: "value",
    //         axisLine: {
    //           lineStyle: {
    //             color: "rgb(119,179,241,.4)", // 颜色
    //             width: 1, // 粗细
    //           },
    //         },
    //         axisTick: {
    //           show: false,
    //         },
    //         axisLabel: {
    //           interval: 0,
    //           textStyle: {
    //             color: "#D6E7F9",
    //             fontSize: 28,
    //           },
    //           show:true
    //         },
    //       }
    //     ],
    //     yAxis: [
    //       {
    //         name: "单位：mg/L",
    //         type: "category",
    //         data: this.chartsData.map(item => item.name),
    //         nameTextStyle: {
    //           fontSize: 28,
    //           color: "#D6E7F9",
    //           padding: [0, 0, 20, 50],
    //         },
    //         splitLine: {
    //           lineStyle: {
    //             color: "rgb(119,179,241,.4)",
    //           },
    //         },
    //         axisLabel: {
    //           textStyle: {
    //             fontSize: 28,
    //             color: "#D6E7F9",
    //           },
    //         },
    //       }
    //     ],
    //     series: [
    //       {
    //         name: "人口数",
    //         type: "bar",
    //         barWidth: 18,
    //         itemStyle: {
    //           barBorderRadius: [0, 0, 0, 0],
    //           normal:{
    //             color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
    //               {
    //                 offset: 0,
    //                 color: "#00D1FF",
    //               },
    //               {
    //                 offset: 1,
    //                 color: "rgba(0,209,255,0)",
    //               },
    //             ]),
    //           }
    //         },
    //         data: this.chartsData.map(item => item.value),
    //         label: {
    //           show: false,
    //           position: 'top',
    //           textStyle: {
    //             color: "#fff",
    //             fontSize: 28
    //           }
    //         }
    //       },
    //     ],
    //   };
    //   myChart.setOption(option)
    //   myChart.getZr().on('mousemove', param => {
    //     myChart.getZr().setCursorStyle('default')
    //   })
    // },
    getEcharts3DBar() {
      var colorArr = ["#0C628C", "#3887D5", "#2570BB"];
      var color = {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[1],
          },
          {
            offset: 1,
            color: colorArr[1],
          },
        ],
      };
      var barWidth = 38;
      var option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          x: '10%',
          x2: '0%',
          y: '18%',
          y2: '15%',
        },
        legend: {
          show: true,
          right:'5%',
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          data: this.chartsData.map(item=>item.name),
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false }
        },
        yAxis: {
          name:"单位：个",
          type: 'value',
          nameTextStyle: {
            fontSize: 28,
            color: "#D6E7F9",
            padding: [10, -100, 10, 10],
          },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            z: 1,
            name: '重大危险源数量',
            type: "bar",
            barWidth: barWidth,
            barGap: "0%",
            data: this.chartsData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
          },
          { // 底部方块
            z: 2,
            name: '重大危险源数量',
            type: "pictorialBar",
            data: this.chartsData,
            symbol: "diamond",
            symbolOffset: ["0%", "60%"],
            symbolSize: [barWidth, 12],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
          { // 顶部方块
            z: 3,
            name: '重大危险源数量',
            type: "pictorialBar",
            symbolPosition: "end",
            data: this.chartsData,
            symbol: "diamond",
            symbolOffset: ["-1%", "-55%"],
            symbolSize: [barWidth+2, (10 * (barWidth+28)) / barWidth],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
        ],
      };
      return option;
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.zdwxyEcharts {
  width: 884px;
  height: 338px;
  margin-top: 10px;
}
</style>