<template>
  <div class="eventTrend">
     <div id="sjqs_bar" style="  width: 1936px;
      height: 486px;"></div>
  </div>
</template>

<script>
export default {
  name: "eventTrend",
  data() {
    return { 
      data0:[
        {name:"1月",value:3000},
        {name:"2月",value:1800},
        {name:"3月",value:2000},
        {name:"4月",value:1700},
        {name:"5月",value:1300},
        {name:"6月",value:2000},
        {name:"7月",value:1000},
        {name:"8月",value:1400},
        {name:"9月",value:1100},
        {name:"10月",value:2300},
        {name:"11月",value:1700},
        {name:"12月",value:1200},
      ]
    }
  },
  computed: {},
  mounted() {
 this.$nextTick(()=>{
  var serveTBar = this.$echarts.init(document.getElementById('sjqs_bar'));
    serveTBar.setOption(this.getEcharts3DBar());
 })
  },
  methods: {
    getEcharts3DBar() {
      var colorArr = ["#0C628C", "#3887D5", "#2570BB"];
      var color = {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[1],
          },
          {
            offset: 1,
            color: colorArr[1],
          },
        ],
      };
      var barWidth = 38;
      var option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            var str = params[0].name + ":";
            params.filter(function (item) {
              if (item.componentSubType == "bar") {
                str += "<br/>" + item.seriesName + "：" + item.value;
              }
            });
            return str;
          },
        },
        grid: {
          x: '7%',
          x2: '0%',
          y: '15%',
          y2: '15%',
        },
        legend: {
          show: false,
          data: ['本期', '同期'],
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          data: this.data0.map(item=>item.name),
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false }
        },
        yAxis: {
          name:"单位：万人",
          type: 'value',
          nameTextStyle: {
              fontSize: 28,
              color: "#D6E7F9",
              padding: [10, -100, 10, 10],
            },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            z: 1,
            name: '本期',
            type: "bar",
            barWidth: barWidth,
            barGap: "0%",
            data: this.data0,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
          },
          { // 底部方块
            z: 2,
            name: '本期',
            type: "pictorialBar",
            data: this.data0,
            symbol: "diamond",
            symbolOffset: ["0%", "60%"],
            symbolSize: [barWidth, 12],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
          { // 顶部方块
            z: 3,
            name: '本期',
            type: "pictorialBar",
            symbolPosition: "end",
            data: this.data0,
            symbol: "diamond",
            symbolOffset: ["-1%", "-55%"],
            symbolSize: [barWidth+2, (10 * (barWidth+28)) / barWidth],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#0A789C",
                  },
                  {
                    offset: 1,
                    color: "#00C0FF ",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
        
        ],
      };
      return option;
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .eventTrend {
    margin-top: 22px;
    .sjqs_bar{
      width: 1936px;
      height: 486px;
    }
  }
</style>