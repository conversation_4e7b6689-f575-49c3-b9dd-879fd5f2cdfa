<template>
  <div class="container">
    <Ktitle :titleText="'园区货运TOP3'" width="939.6" :show-date="false" />
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="372"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "yqhyfx",
  data() {
    return {
      tableData: [
        {
          index: "1",
          name:"金华传化公路港",
          number:"124",
          percent:"5.3%",
          number2:"12",
          percent2:"4.5%"
        },
        {
          index: "2",
          name:"菜鸟物流园",
          number:"156",
          percent:"2.4%",
          number2:"18",
          percent2:"2.2%"
        },
        {
          index: "3",
          name:"金华昌鑫物流有限公司",
          number:"123",
          percent:"-0.45%",
          number2:"21",
          percent2:"-0.51%"
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '园区',
          key: 'name',
          flex: 2,
        },
        {
          label: '货运量',
          key: 'number',
          flex: 1,
        },
        {
          label: '同比',
          key: 'percent',
          flex: 1,
        },
        {
          label: '评价运量',
          key: 'number2',
          flex: 1,
        },
        {
          label: '同比',
          key: 'percent2',
          flex: 1,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>