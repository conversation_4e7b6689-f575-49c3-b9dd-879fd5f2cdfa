<template>
    <div class="sjzx_middle_left">
      <div class="sjzx_middle_left_container">
        <el-checkbox-group v-model="mapUrlsValue">
          <el-checkbox 
            v-for="(item,index) in yjDataMap" 
            :label="item.name" 
            :key="index" 
            class="checkbox-box"
            @change="getPointData($event, item)">
            {{item.name}}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'YjDialog',
    data() {
      return {
        mapUrlsValue: ['救援队伍'],
        yjDataMap: [
          {
            name: '救援队伍',
            idName: 'yjwz',
            img: 'zhdd_map_zydd_jydw',
            url: '/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:yjdw&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.22367661602989,28.52706653689894%20120.77889703265598,28.52706653689894%20120.77889703265598,29.68484864821428%20119.22367661602989,29.68484864821428%20119.22367661602989,28.52706653689894%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E',
          },
          {
            name: '应急专家',
            idName: 'yjzj',
            img: 'zhdd_map_zydd_yjzj',
            url: '/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:yjzj&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.22367661602989,28.52706653689894%20120.77889703265598,28.52706653689894%20120.77889703265598,29.68484864821428%20119.22367661602989,29.68484864821428%20119.22367661602989,28.52706653689894%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E',
          },
          {
            name: '应急物资',
            idName: 'yjdw',
            img: 'zhdd_map_zydd_yjwz',
            url: '/geoserver/poitest/ows?service=WFS&request=GetFeature&version=1.0.0&typeName=poitest:yjwz&maxFeatures=3000&outputFormat=json&filter=%3CFilter%20xmlns=%22http://www.opengis.net/ogc%22%20xmlns:gml=%22http://www.opengis.net/gml%22%3E%3CIntersects%3E%3CPropertyName%3Ethe_geom%3C/PropertyName%3E%3Cgml:Polygon%3E%3Cgml:outerBoundaryIs%3E%3Cgml:LinearRing%3E%3Cgml:coordinates%3E119.22367661602989,28.52706653689894%20120.77889703265598,28.52706653689894%20120.77889703265598,29.68484864821428%20119.22367661602989,29.68484864821428%20119.22367661602989,28.52706653689894%20%3C/gml:coordinates%3E%3C/gml:LinearRing%3E%3C/gml:outerBoundaryIs%3E%3C/gml:Polygon%3E%3C/Intersects%3E%3C/Filter%3E',
          }
        ],
        popObj: {},
        currentName: '救援队伍'
      }
    },
    mounted() {
      this.getPointData(true, this.yjDataMap[0])
    },
    methods: {
      getPointData(flag, item) {
        let name = typeof flag === 'boolean' ? item.name : item.target._value
        let yjData = this.yjDataMap.find(ele => ele.name === name)
        
        if(flag) {
          axios({
            method: 'post',
            url: yjData.url,
            headers: {
              'Content-Type': 'application/json;charset=UTF-8',
              Authorization: 'Bearer ' + sessionStorage.getItem('csdnIndexApiAuthorization').replaceAll('"', ''),
            },
          }).then((res) => {
            const { features } = res.data
            let pointData = features.map(item => ({
              data: { ...item.properties },
              point: item.geometry.coordinates.join(','),
              lng: item.geometry.coordinates[0],
              lat: item.geometry.coordinates[1]
            }))
            this.addPointFun(name, pointData, yjData.img)
          })
        } else {
          this.rmPiont(name)
        }
      },
      addPointFun(name, pointData, img) {
        this.currentName = name
        mapUtil.loadPointLayer({
          data: pointData,
          layerid: name,
          iconcfg: {
            image: img,
          },
          onclick: this.onClickPiont,
        })
      },
      onClickPiont(item) {
        let heights = 350
        
        if (this.currentName == '救援队伍') {
          this.popObj = { '名称': item.data.team_name, '级别': item.data.team_grade, '类型': item.data.rescue_type }
          heights = 350
        } else if (this.currentName == '应急专家') {
          this.popObj = { '名称': item.data.expert_name, '性别': item.data.sex, '职务': item.data.title, '电话': item.data.phone, '工作单位': item.data.work_unit }
          heights = 500
        } else {
          this.popObj = { '名称': item.data.material_name, '类型': item.data.material_type, '数量': item.data.material_count }
          heights = 350
        }
  
        mapUtil.flyTo({
          destination: [item.lng, item.lat],
          offset: [-30, (heights * -1) + 150],
        })
  
        let arr = []
        for (let key in this.popObj) {
          arr.push(`<div class="cglp-con-item">
            <div class="cgrklp-text">${key}</div>
            <div class="cglp-text" title="${this.popObj[key]}">${this.popObj[key] || '--'}</div>
          </div>
          <div class="cglp-con-line"></div>`)
        }
  
        let str = `<div class="cglp" style="height:${heights}px">
          <div class="cglp-bt">${this.currentName}详情</div>
          <div class="icon-closedd cursor cgrklp-close" onclick="this.parentNode.parentNode.style.display = 'none'"></div>
          <div class="cglp-con">
            ${arr.join('')}
          </div>
         </div>`
  
        this.createPop(item, str)
      },
      createPop(item, str) {
        let pos = [item.lng, item.lat]
        let objData = {
          layerid: item.layerid,
          position: pos,
          popup: {
            closeButton: true,
          },
          content: str,
        }
        mapUtil._createPopup(objData)
      },
      rmPiont(name) {
        mapUtil.removeLayer(name)
      },
    }
  }
  </script>
  
  <style scoped lang="less">
   .sjzx_middle_left {
      /* position: absolute;
        top: 0;
        left: 48px; */
      width: 300px;
      min-height: 340px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      /* box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56); */
      /* opacity: 0.85; */
      border-radius: 10px;
    }

    .sjzx_middle_title {
      font-size: 36px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #d6e7f9;
      background: linear-gradient(180deg, #aed6ff 0%, #74b8ff 47.4853515625%, #9ccfff 50%, #ddeeff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: center;
    }

    .sjzx_middle_title p {
      margin-top: 10px;
      height: 82px;
      line-height: 83px;
      white-space: nowrap;
    }

    .sjzx_middle_title p:before {
      content: '';
      height: 1px;
      top: -3%;
      position: relative;
      width: 18%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-right: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title p:after {
      content: '';
      top: -3%;
      position: relative;
      width: 18%;
      height: 1px;
      border-bottom: 3px solid #74b8ff;
      display: inline-block;
      margin-left: 5px;
      margin-bottom: 12px;
    }

    .sjzx_middle_title .before {
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title .after {
      /* display: inline-block; */
      display: inline-block;
      width: 10px;
      height: 10px;
      background-color: #74b8ff;
      /* transform: rotateZ(90deg); */
      border-radius: 5px;
      margin-bottom: 10px;
    }

    .sjzx_middle_title p .tab {
      cursor: pointer;
    }

    :deep .el-checkbox__input {
      float: right;
      margin-right: 30px;
    }

    :deep .el-tree-node__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;

      color: #c0d6ed;
      line-height: 58px;
    }

    :deep .el-tree-node__content {
      height: 50px !important;
      margin-bottom: 10px;
    }

    :deep .is-focusable {
      background-color: unset;
    }

    /* .el-tree-node__expand-icon.expanded {
        display: block;
      } */
    :deep .el-tree-node__content>.el-tree-node__expand-icon {
      /* display: none; */
    }

    /* .el-tree--highlight-current
        .el-tree-node.is-current
        > .el-tree-node__content {
        background-color: unset;
      }
      .el-tree-node__content:hover,
      .el-tree > .el-tree-node.is-current {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      }

      .el-tree-node.is-focusable.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        ) !important;
        border-radius: 0px 30px 30px 0px;
      } */

    :deep .el-tree-node.is-current>.el-tree-node__content,
    :deep .el-tree-node__content:hover {
      background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    :deep .el-checkbox {
      /* position: absolute;
        right: 0; */
      display: block;
      border-radius: 15px;
      margin-bottom: 2px;
      margin-right: 0;
    }

    :deep .el-checkbox-group .el-checkbox:hover {
      background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    :deep .el-checkbox-group .is-checked {
      background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
      border-radius: 0px 30px 30px 0px;
    }

    :deep .el-checkbox__label {
      font-size: 30px;
      font-family: PangMenZhengDao;
      font-weight: bold;
      /* font-style: italic; */
      color: #c0d6ed;
      line-height: 58px;
    }

    :deep .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 15px;
      background-color: #344d67;
    }

    .auth-tree .el-checkbox__inner {
      width: 33px;
      height: 33px;
      margin-top: 21px;
      background-color: #344d67;
    }

    .sjzx_middle_left_container {
      padding: 30px 20px 0 40px;
    }

    .checkbox-box-img {
      width: 30px;
      height: 42px;
      position: relative;
      top: 10px;
    }

    /* .checkbox-box >span{
        position: relative;
        top:-10px;
      } */
    /* .el-checkbox.is-checked {
        background: linear-gradient(
          94deg,
          rgba(3, 97, 156, 0) 0%,
          #03619c 100%
        );
        border-radius: 0px 30px 30px 0px;
      } */
    :deep .el-checkbox__input.is-checked .el-checkbox__inner,
    :deep .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #252316;
      border-color: #ffc561;
    }

    :deep .el-checkbox__inner::after {
      width: 7px;
      height: 18px;
      left: 10px;
      color: #ffc561 !important;
    }

    .sjzx_middle_right {
      position: absolute;
      left: 2800px;
      top: 0;
      width: 620px;
      height: 1350px;
      background: linear-gradient(179deg, #0e1a40 0%, #064069 100%);
      box-shadow: 0px 7px 50px 0px rgba(35, 154, 228, 0.56);
    }

    .sjzx_middle_right_container {
      margin-top: 10px;
      margin-left: 90px;
      border-left: 8px solid #00ffff;
      /* height: 1200px; */
      /* overflow-y: auto; */
    }

    .sjzx_middle_right_content {
      height: 1000px;
      overflow-y: scroll;
    }

    .sjzx_middle_right_content::-webkit-scrollbar {
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
      /* scrollbar-arrow-color: red; */
    }

    .sjzx_middle_right_content::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .btn_right {
      padding: 0 50px;
      min-width: 250px;
      width: auto;
      height: 53px;
      background-image: url('@/assets/home/<USER>/btn.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      font-size: 24px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #ffffff;
      line-height: 25px;
      text-shadow: 0px 2px 5px #000000;
      background-color: transparent;
      border: unset;
      margin-left: 40px;
      margin-bottom: 20px;
    }

    .yjyp-item p {
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin-left: 50px;
      line-height: 40px;
    }

    .yjyp-item {
      margin-bottom: 70px;
    }

    .red {
      background: linear-gradient(180deg, #ffffff 0%, #ffcdcd 50.244140625%, #ff4949 53.0029296875%, #ffcdcd 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .yellow {
      background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffc460 53.0029296875%, #ffeccb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .blue {
      color: #22e8e8 !important;
    }

    .item-s {
      margin-bottom: 20px;
    }

    .item-time {
      position: relative;
    }

    .item-time::before {
      position: absolute;
      left: -65px;
      content: '';
      display: inline-block;
      width: 51px;
      height: 25px;
      background-image: url('@/assets/home/<USER>/circle.png');
      background-size: 100% 100%;
    }

    .center_bottom {
      position: absolute;
      top: 1365px;
      left: 45px;

      display: flex;
      justify-content: center;
    }

    .shijian {
      width: 549px;
      height: 263px;
      background: url('@/assets/home/<USER>/dwjc-right-bc.png') no-repeat;
      background-size: 100% 100%;
      margin: 20px auto;
    }

    .shijian #eventMain {
      display: inline-block;
      width: 93.5%;
      margin: 25px;
    }

    .shijian .contain {
      overflow-y: auto;
      height: 239px;
      overflow-x: hidden;
      width: 525px;
    }

    :deep .el-tree {
      background-color: unset;
    }

    .sjzx_middle_left_container {
      height: 200px;
      overflow-y: scroll;
    }

    .shijian .contain::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .shijian .contain::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    .sjzx_middle_left_container::-webkit-scrollbar-thumb {
      border-radius: 6px;
      /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
      background: #20aeff;
      height: 8px;
    }

    .auth-tree>.el-tree-node>.el-tree-node__content .el-checkbox {
      display: none;
    }

    :deep .el-icon-caret-left:before {
      font-size: 20px;
    }

    :deep .el-tree-node__expand-icon {
      position: absolute;
      right: 0;
    }

    :deep .el-tree-node__label {
      padding-left: 15px;
    }

    /* .el-tree-node__expand-icon.expanded {
        -webkit-transform: rotate(2700deg);
        transform: rotate(270deg);
      } */
    :deep .el-tree-node__expand-icon.expanded {
      -webkit-transform: rotate(-90deg);
      transform: rotate(-90deg);
    }

    :deep .el-checkbox__input.is-checked+.el-checkbox__label {
      color: #c0d6ed;
    }

    :deep .el-tree-node__content>label.el-checkbox {
      position: absolute;
      right: 0;
    }
</style>