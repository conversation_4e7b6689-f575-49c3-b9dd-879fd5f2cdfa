<template>
  <div class="midBottomWrap">
    <div class="midBoTop">
      <div class="leftLine">
        <TitleLine :direction="'left'" />
      </div>
      <div :class="{'tabTxtClass':true,'tabTxtActice':isTabOne}" @click="tabStatu = 1">社会治理中心</div>
      <div class="tagLine"></div>
      <div :class="{'tabTxtClass':true,'tabTxtActice':!isTabOne}" @click="tabStatu = 2">多维感知</div>
      <div class="RightLine">
        <TitleLine :direction="'right'" />
      </div>
    </div>
    <div class="contentWrap">
      <div v-show="tabStatu == 1" class="animate__animated animate__fadeIn">
        <ShzlzxPart />
      </div>
      <div v-if="tabStatu == 2" class="animate__animated animate__fadeIn">
        <DwgzPart />
      </div>
    </div>

  </div>
</template>

<script>
import TitleLine from "@/pages/home/<USER>/midBottom/TitleLineLeft.vue"
import ShzlzxPart from "@/pages/home/<USER>/midBottom/ShzlzxPart.vue"
import DwgzPart from "@/pages/home/<USER>/midBottom/DwgzPart.vue"

export default {
  components: {
    TitleLine,
    ShzlzxPart,
    DwgzPart
  },
  data () {
    return {
      tabStatu: 1
    }
  },
  computed: {
    isTabOne () {
      return this.tabStatu == 1 ? true : false
    }
  }
}
</script>

<style lang="less" scoped>
.midBottomWrap {
  width: 3281px;
  height: 565px;
  background: url('~@/assets/common/botBg.png') no-repeat center center;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding-top: 77px;
  padding-bottom: 22px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .midBoTop {
    display: flex;
    align-items: center;
    width: 100%;

    .leftLine {
      flex: 1;
      margin-right: 19px;
    }
    .tagLine {
      width: 4px;
      height: 56px;
      background: linear-gradient(0deg, #caffff, #00c0ff, #ffffff, #caffff);
      box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.29);
      opacity: 0.5;
      margin: 0 30px;
    }
    .RightLine {
      flex: 1;
      margin-left: 19px;
    }
    .tabTxtClass {
      font-size: 54px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      color: rgba(255, 255, 255, 0.5);
      // opacity: 0.5;
      text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.5);
      background: linear-gradient(0deg, #caffff 0%, #00c0ff 50%, #ffffff 0%, #caffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      cursor: pointer;
      transition: 0.3s;
    }
    .tabTxtActice {
      text-shadow: none !important;
      background: linear-gradient(0deg, #caffff 0%, #00c0ff 50%, #caffff 0%, #ffffff 100%) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }
  }
  .contentWrap {
    width: 3160px;
    height: 351px;
  }
}
</style>