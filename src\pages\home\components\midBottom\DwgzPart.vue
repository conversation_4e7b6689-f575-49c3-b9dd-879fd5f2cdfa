<template>
  <div class="shzlzxWrap">
    <div class="leftPart">
      <div class="countoWrap">
        <span class="txt_o">前端感知设备</span>
        <span class="txt_t">{{numberObj.typeNumber}}类</span>
        <div class="countoContainer">
          <CounTo :val="numberObj.allEquipmentNumber" />
        </div>
        <span class="txt_f">个</span>
      </div>
      <div class="swiperWrap">
        <div class="swiperContainer">
          <ImgSwiper @number="getNumber"/>
        </div>
      </div>
    </div>
    <div class="midPart">
      <titleTwo :title-text="'感知预警'"></titleTwo>
      <div class="table">
        <div class="tableLine title">
          <div class="tableTab" v-for="(item,index) in tableTitle" :key="index" :class="{'indexNum':index == 0}" style="color: #77B3F1">{{item}}</div>
        </div>
        <div class="tableContainer">
          <div class="tableLine" v-for="(item,index) in tableContainer" :key="index">
            <div class="tableTab indexNum numContainer">{{index + 1}}</div>
            <div class="tableTab" :title="item.warnTime" style="width: 220.2px;">{{item.warnTime}}</div>
            <div class="tableTab" :title="item.sbNo">{{item.sbNo}}</div>
            <div class="tableTab" :title="item.describe">{{item.describe}}</div>
          </div>
        </div>
      </div>
    </div>
    <videoList />
  </div>
</template>

<script>
import TitleTwo from "@/components/titleTwo.vue"
import ImgSwiper from "@/pages/home/<USER>/midBottom/ImgSwiper.vue"
import videoList from "@/pages/home/<USER>/midBottom/videoList";
import CounTo from "@/pages/home/<USER>/midBottom/CounTo.vue"
import {getDwgzList} from "@/api/warning";
export default {
  components: {
    TitleTwo,
    ImgSwiper,
    videoList,
    CounTo
  },
  data () {
    return {
      tableTitle: ['序号', '时间', '设备ID', '预警详情'],
      tableContainer: [],
      numberObj: {
        typeNumber:"",
        allEquipmentNumber:""
      },
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getNumber(res) {
      this.numberObj = {
        typeNumber: res.typeNumber,
        allEquipmentNumber: String(res.allEquipmentNumber),
      }
    },
    getList() {
      getDwgzList().then(res => {
        this.tableContainer = res.data.data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.pd-1 {
  padding-top: 52px;
  box-sizing: border-box;
}
.mg-2 {
  margin-top: 48px;
}
.mg-3 {
  margin-top: 10px;
}
.ml_1 {
  margin-left: 72px;
}
.shzlzxWrap {
  width: 100%;
  height: 100%;
  // background-color: aqua;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  top: -38px;
  // background: url('~@/assets/common/nrk.png') no-repeat center center;
  // background-size: 100% 100%;
  .leftPart {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding-top: 30px;
    // justify-content: space-between;
    // background-color: antiquewhite;
    .countoWrap {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      // background-color: #22e8e8;
      .txt_o {
        font-size: 32px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #ffffff;
        margin-right: 8px;
        margin-bottom: 8px;
        letter-spacing: 2px;
      }
      .txt_t {
        font-size: 40px;
        font-family: Source Han Sans SC;
        font-weight: bolder;
        letter-spacing: 2px;
        color: #ffffff;
        // padding: 10px 0 0 0;
        background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .txt_f {
        font-size: 40px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        letter-spacing: 2px;
        color: #ffffff;
        // padding: 10px 0 0 0;
        background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .countoContainer {
        margin: 0 8px 0 12px;
      }
    }
    .swiperWrap {
      width: 100%;
      height: 140px;
      display: flex;
      justify-content: center;
      margin-top: 90px;

      .swiperContainer {
        width: 900px;
        height: 140px;
        display: flex;
        justify-content: center;
        // background-color: #ffc460;
      }
    }
  }
  .midPart {
    // background-color: bisque;
    //margin-top: 4px;
    .table {
      width: 936px;
      height: 315px;
      //margin-top: 12px;
      .tableContainer {
        height: 231px;
        overflow-y: scroll;
      }
      .tableLine {
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        width: 936px;
        height: 63px;
        background: #0F2B4D;
        margin-bottom: 14px;
        .tableTab {
          text-align: left;
          width: 187.2px;
          font-size: 34px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .indexNum {
          width: 120px;
        }
        .numContainer {
          font-size: 34px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #D6E7F9;
          background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .title {
        background: #035B86;
        color: #77B3F1;
      }
    }
  }
  .rightPart {
    flex: 1;
    height: 100%;
    // background-color: antiquewhite;
    box-sizing: border-box;
    padding-top: 30px;
    .changWrap {
      display: flex;
      justify-content: center;
      align-items: center;
      .countoWrap {
        display: flex;
        justify-content: center;
        align-items: flex-end;
        // background-color: #22e8e8;
        .txt_o {
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #ffffff;
          margin-right: 8px;
          margin-bottom: 8px;
          letter-spacing: 2px;
        }
        .txt_t {
          font-size: 40px;
          font-family: Source Han Sans SC;
          font-weight: bolder;
          letter-spacing: 2px;
          color: #ffffff;
          // padding: 10px 0 0 0;
          background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .txt_f {
          font-size: 40px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          letter-spacing: 2px;
          color: #ffffff;
          // padding: 10px 0 0 0;
          background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .countoContainer {
          margin: 0 8px 0 12px;
        }
      }
      .freshImgWrap {
        display: flex;
        align-items: center;
        margin-left: 90px;
        cursor: pointer;
        .freshImg {
          width: 32px;
          height: 32px;
        }
        .hypClass {
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #c0d6ed;
          margin-left: 12px;
        }
      }
    }
    .photoWrap {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 36px;
      box-sizing: border-box;
      // padding: 0 140px;
      .imgOne {
        width: 316px;
        height: 216px;
        background: url('~@/assets/common/spmc.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
}
</style>