<template>
  <div class="container">
    <Ktitle :titleText="'园区管理'" width="939.6" :show-date="false" />
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="470"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "yqgl",
  data() {
    return {
      tableData: [
        {
          index: "1",
          name:"秋滨工业园区",
          address:"浙江省金华市婺城区秋滨工业园始丰路169号",
          mj:"39872㎡"
        },
        {
          index: "2",
          name:"城北工业区",
          address:"浙江省金华市城北工业园区玉泉路468号",
          mj:"19409㎡"
        },
        {
          index: "3",
          name:"金磐开发区",
          address:"浙江省金华市婺城区西关街道环城南路南段1668号",
          mj:"19023㎡"
        },
        {
          index: "4",
          name:"临江工业园区",
          address:"浙江省金华市婺城区广泽路",
          mj:"5595亩"
        },
        {
          index: "5",
          name:"竹马工业区",
          address:"浙江省金华市婺城区金九线北侧",
          mj:"17930㎡"
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '园区',
          key: 'name',
          flex: 3,
        },
        {
          label: '地址',
          key: 'address',
          flex: 4,
        },
        {
          label: '面积',
          key: 'mj',
          flex: 2,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>