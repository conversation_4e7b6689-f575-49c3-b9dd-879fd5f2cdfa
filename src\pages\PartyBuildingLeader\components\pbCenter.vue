<!--公职公务干部信息-->
<template>
  <div class="CadreInformation">
    <ktitle :titleText="'公职公务干部信息'" :width="'3057.5'"></ktitle>
    <number></number>
    <cadre></cadre>
    <div class="chartsList">
      <charts1></charts1>
      <charts2></charts2>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import number from "@/pages/PartyBuildingLeader/components/centerComponents/number";
import cadre from "@/pages/PartyBuildingLeader/components/centerComponents/cadre";
import charts1 from "@/pages/PartyBuildingLeader/components/centerComponents/charts1.vue";
import charts2 from "@/pages/PartyBuildingLeader/components/centerComponents/charts2.vue";
export default {
  name: "soCenter",
  data() {
    return {}
  },
  components:{
    ktitle,
    number,
    cadre,
    charts1,
    charts2,
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .CadreInformation {
    margin: 26px 68px 0 68px;
    overflow: hidden;
    .chartsList {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
  }
</style>