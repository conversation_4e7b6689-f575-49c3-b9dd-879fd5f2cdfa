<template>
  <div style="margin-top: 100px">
    <pie3D :id="'eventRightPieCharts'" :options="chartsData" :widthCustom="939.6" :height-custom="500" :legendCustom="legend" :internalDiameterRatio="1.8" :scaleCustom="1"></pie3D>
  </div>
</template>

<script>
import pie3D from "@/components/pie3D";
export default {
  name: "DepartmentEventAnalysis",
  data() {
    return {
      chartsData:[
        {
          name:"市政建设部",
          value:22
        },
        {
          name:"城市规划部",
          value:40
        },
        {
          name:"城市管理部",
          value:22
        },
        {
          name:"综合管理部",
          value:60
        },
        {
          name:"行政执法部",
          value:22
        }
      ],
      legend:{
        itemWidth: 30,
        itemHeight: 15,
        itemGap: 20,
        left: '50%',
        top: '20%',
      }
    }
  },
  components:{
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>