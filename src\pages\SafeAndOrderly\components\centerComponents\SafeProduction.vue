<!--安全生产-->
<template>
  <div>
    <titleTwo :title-text="'安全生产'"></titleTwo>
    <div class="box">
      <div class="item" v-for="(item,i) in list" :key="i">
        <img :src="item.img" alt="">
        <div class="info">
          <div class="name">{{item.name}}</div>
          <div class="value blue"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "SafeProduction",
  data() {
    return {
      list:[
        {
          name:"危化品经营企业数",
          value:60,
          unit:"家",
          img:require("@/assets/safe/安全生产1.png")
        },
        {
          name:"危化品生产企业数",
          value:17,
          unit:"家",
          img:require("@/assets/safe/安全生产2.png")
        },
        {
          name:"安全产业数量",
          value:1,
          unit:"件",
          img:require("@/assets/safe/安全生产3.png")
        },
        {
          name:"工业企业安全隐患排查",
          value:4209,
          unit:"次",
          img:require("@/assets/safe/安全生产4.png")
        },
        {
          name:"执立案数",
          value:213,
          unit:"件",
          img:require("@/assets/safe/安全生产5.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .box {
    width: 935px;
    height: 440px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    .item {
      width: 427px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: 33px;
      img {
        width: 129px;
        height: 116px;
      }
      .info {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        margin-left: 32px;
        .name {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          white-space: nowrap;
        }
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
        }
        .unit {
          font-size: 25px;
          white-space: nowrap;
          margin-left: 5px;
        }
      }
    }
  }
</style>