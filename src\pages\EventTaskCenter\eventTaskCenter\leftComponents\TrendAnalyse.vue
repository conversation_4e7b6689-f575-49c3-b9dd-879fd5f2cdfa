<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts1">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="charts1" id="charts1"></div>
  </div>
</template>

<script>
import {getEventQs} from "@/api/EventTask";

export default {
  name: 'charts1',
  data() {
    return {
      charts1Data: [
        { date: '0:00', value: 220 },
        { date: '2:00', value: 240 },
        { date: '4:00', value: 250 },
        { date: '6:00', value: 220 },
        { date: '8:00', value: 300 },
        { date: '10:00', value: 240 },
        { date: '12:00', value: 270 },
        { date: '14:00', value: 300 },
        { date: '16:00', value: 250 },
        { date: '18:00', value: 280 },
        { date: '20:00', value: 330 },
        { date: '22:00', value: 180 },
      ],
      value:"日",
      options:[
        {label:"日",value:"日"},
        {label:"月",value:"月"},
        {label:"年",value:"年"},
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts1()
  },
  methods: {
    initCharts1() {
      getEventQs({type: this.value}).then(res => {
        this.charts1Data = res.data.data.map(item => ({
          date: item.label,
          value: item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById('charts1'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: '3%',
            right: '6%',
            top: '22%',
            bottom: '1%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: this.charts1Data.map((item) => item.date),
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: '单位/件',
              type: 'value',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [10, -100, 10, 10],
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
            {
              name: '',
              type: 'value',
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: 5,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#77B3F1',
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '事件数',
              type: 'line', // 直线ss
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#00C0FF',
                },
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0.2,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0)',
                  },
                ]),
                // color: new echarts.graphic.LinearGradient(
                //   0,
                //   0,
                //   0,
                //   1,
                //   [
                //     {
                //       offset: 0,
                //       color: 'red',
                //       //渐变色的开始颜色
                //     },
                //     {
                //       offset: 0.8,
                //       color: 'green',
                //       //渐变色的开始颜色
                //     },
                //   ],
                //   false
                // ),
              },
              data: this.charts1Data.map((item) => item.value),
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 80px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}
.charts1 {
  height: 380px;
}
</style>
