<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <WarningCenterLeft class="animate__animated animate__fadeInLeft" />
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <WarningCenterRight class="animate__animated animate__fadeInRight" />
      </wrapbox>
    </div>
    <div class="innerBottom">
      <WarningCenterBottom />
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import WarningCenterLeft from "@/pages/EarlyWarningCenter/warningCenter/warningCenterLeft.vue"
import WarningCenterRight from "@/pages/EarlyWarningCenter/warningCenter/warningCenterRight.vue"
import WarningCenterBottom from "@/pages/EarlyWarningCenter/warningCenter/warningCenterBottom";
export default {
  components: {
    WarningCenterLeft,
    WarningCenterRight,
    WarningCenterBottom,
    wrapbox
  },
  data () {
    return {

    }
  },
  computed: {},
  mounted () {

  },
  methods: {

  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerBottom {
    position: absolute;
    left: 2220px;
    bottom: 0px;
    z-index: 2;
  }
}
</style>