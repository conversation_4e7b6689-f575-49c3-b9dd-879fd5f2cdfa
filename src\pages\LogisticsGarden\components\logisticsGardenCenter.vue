<template>
  <div class="eventCenterCenter">
    <div class="item_box">
      <div class="item_top">
        <div class="item_top_left">今日人流量</div>
        <div class="item_top_right">
          <CounTo :val="personCount.toString()" />
        </div>
      </div>
      <div class="item_bottom">
        <img class="left_img" src="@/assets/common/logistics_center_left_icon.png" alt="" />
        <div class="count_box">
          <div class="count_top">
            <div class="block"></div>
            <div class="name">内部人员</div>
          </div>
          <div class="count">156</div>
        </div>
        <div class="count_box1">
          <div class="count_top">
            <div class="block"></div>
            <div class="name">未识别人员</div>
          </div>
          <div class="count">156</div>
        </div>
      </div>
    </div>
    <div class="item_box">
      <div class="item_top">
        <div class="item_top_left">今日车流量</div>
        <div class="item_top_right">
          <CounTo :val="carCount.toString()" :colotTxt="'#7AFFD0'"/>
        </div>
      </div>
      <div class="item_bottom">
        <img class="right_img" src="@/assets/common/logistics_center_right_icon.png" alt="" />
        <div class="count_box">
          <div class="count_top">
            <div class="block"></div>
            <div class="name">内部车辆</div>
          </div>
          <div class="count">85</div>
        </div>
        <div class="count_box1">
          <div class="count_top">
            <div class="block"></div>
            <div class="name">未识别车辆</div>
          </div>
          <div class="count">34</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CounTo from '@/pages/LogisticsGarden/components/centerComponents/CounTo.vue'
export default {
  name: 'eventCenterCenter',
  components: { CounTo },
  data() {
    return {
      personCount:2302422,
      carCount:9710200,
    }
  },
  mounted() {
    setInterval(() => {
      this.personCount+=50
      this.carCount+=50
    }, 30000);
  },
  methods: {},
  watch: {},
}
</script>

<style lang="less" scoped>
.eventCenterCenter {
  margin: 80px 68px 0 68px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  .item_box {
    width: 1065px;
    height: 421px;
    background: url('@/assets/common/logistics_center_bkg.png') 0 0 no-repeat;
    background-size: cover;
    box-sizing: border-box;
    padding: 40px 36px;
    &:first-child {
      margin-right: 104px;
    }
    .item_top {
      display: flex;
      align-content: center;
      align-items: center;
      .item_top_left {
        width: 272px;
        height: 165px;
        padding-top: 42px;
        box-sizing: border-box;
        background: url('@/assets/common/logistics_center_name_bkg.png') 0 0 no-repeat;
        background-size: cover;
        font-size: 48px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #b5cdf1;
        line-height: 58px;
        margin-right: 18px;
      }
      .item_top_right {
      }
    }
    .item_bottom {
      display: flex;
      align-content: center;
      align-items: center;
      .left_img {
        width: 124px;
        height: 192px;
        margin-left: 42px;
        margin-right: 96px;
      }
      .right_img {
        width: 222px;
        height: 153px;
        margin-left: 72px;
        margin-right: 64px;
      }
      .count_box {
        .count_top {
          display: flex;
          align-content: center;
          align-items: center;
          .block {
            width: 21px;
            height: 21px;
            background: #00c0ff;
          }
          .name {
            font-size: 28px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #d6e7f9;
            margin-left: 8px;
          }
        }
        .count {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9aa9bf;
          background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-top: 30px;
          margin-left: 32px;
        }
      }
      .count_box1 {
        margin-left: 260px;
        .count_top {
          display: flex;
          align-content: center;
          align-items: center;
          .block {
            width: 21px;
            height: 21px;
            background: #fcdf1d;
          }
          .name {
            font-size: 28px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #d6e7f9;
            margin-left: 8px;
          }
        }
        .count {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9aa9bf;
          background: linear-gradient(180deg, #ffffff 0%, #ffc460 50.244140625%, #ffffff 53.0029296875%, #ffeccb 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-top: 30px;
          margin-left: 32px;
        }
      }
    }
  }
}
</style>
