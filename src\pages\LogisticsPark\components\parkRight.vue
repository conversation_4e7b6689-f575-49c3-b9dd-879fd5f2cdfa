<template>
  <div class="parkRightContainer">
    <yqhjjc></yqhjjc>
    <Ktitle :titleText="'产销情况分析'" width="1935.6" :show-date="false"/>
    <div class="parkRightContainer-twice">
      <cxqkfx-left></cxqkfx-left>
      <cxqkfx-right></cxqkfx-right>
    </div>
    <Ktitle :titleText="'仓储情况分析'" width="1935.6" :show-date="false"/>
    <div class="parkRightContainer-twice">
      <ccqkfx-left></ccqkfx-left>
      <ccqkfx-right></ccqkfx-right>
    </div>
    <Ktitle :titleText="'预警事件'" width="1935.6" :show-date="false"/>
    <div class="parkRightContainer-twice">
      <yjsj-left></yjsj-left>
      <yjsj-right></yjsj-right>
    </div>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon'
import yqhjjc from "@/pages/LogisticsPark/components/RightComponents/yqhjjc";
import cxqkfxLeft from "@/pages/LogisticsPark/components/RightComponents/cxqkfxLeft";
import cxqkfxRight from "@/pages/LogisticsPark/components/RightComponents/cxqkfxRight";
import ccqkfxLeft from "@/pages/LogisticsPark/components/RightComponents/ccqkfxLeft";
import ccqkfxRight from "@/pages/LogisticsPark/components/RightComponents/ccqkfxRight";
import yjsjLeft from "@/pages/LogisticsPark/components/RightComponents/yjsjLeft";
import yjsjRight from "@/pages/LogisticsPark/components/RightComponents/yjsjRight";

export default {
  name: "parkRight",
  data() {
    return {}
  },
  components: {
    Ktitle,
    yqhjjc,
    cxqkfxLeft,
    cxqkfxRight,
    ccqkfxLeft,
    ccqkfxRight,
    yjsjLeft,
    yjsjRight
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.parkRightContainer {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin: 80px 0 0 68px;

  .parkRightContainer-twice {
    width: 1935.6px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 26px;
  }
}
</style>