<template>
  <div class="midBottomWrap">
    <div class="midBoTop">
      <div class="leftLine">
        <TitleLine :direction="'left'" />
      </div>
      <div :class="{ tabTxtClass: true, tabTxtActice: isTabOne }" @click="tabStatu = 1">事件数量</div>
      <div class="tagLine"></div>
      <div :class="{ tabTxtClass: true, tabTxtActice: !isTabOne }" @click="tabStatu = 2">任务数量</div>
      <div class="RightLine">
        <TitleLine :direction="'right'" />
      </div>
    </div>
    <div class="contentWrap">
      <div v-if="tabStatu == 1" class="animate__animated animate__fadeIn">
        <EventNumber @tabClick="tabClick" />
      </div>
      <div v-if="tabStatu == 2" class="animate__animated animate__fadeIn">
        <!-- <DwgzPart /> -->
        <TaskNumber @tabClick="tabClick" />
      </div>
    </div>
    <!-- 视频弹框 -->
    <CommonDialog :dialog-flag="showFlag" :title="title" :dialogWidth="'2200px'" @close="showFlag = false">
      <EventAndTaskInfo :title="title" v-if="showFlag" :list-data="detailList"/>
    </CommonDialog>
  </div>
</template>

<script>
import TitleLine from '@/pages/home/<USER>/midBottom/TitleLineLeft.vue'
import EventNumber from './midComponents/EventNumber.vue'
import TaskNumber from './midComponents/TaskNumber.vue'
import CommonDialog from '@/components/Commondialog/index.vue' //弹框
import EventAndTaskInfo from './midComponents/EventAndTaskInfo.vue'

export default {
  components: {
    TitleLine,
    EventNumber,
    TaskNumber,
    CommonDialog,
    EventAndTaskInfo,
    // ShzlzxPart,
    // DwgzPart,
  },
  data() {
    return {
      tabStatu: 1,
      //任务详情/事件详情弹框
      title: '',
      showFlag: false,
      detailList: []
    }
  },
  computed: {
    isTabOne() {
      return this.tabStatu == 1 ? true : false
    },
  },
  methods: {
    //任务详情/事件详情
    tabClick(obj) {
      //任务详情
      if (obj.name == 'rw') {
        this.title = '任务概况'
        this.detailList = obj.value
        this.showFlag = true
      } else if (obj.name == 'sj') {
        //事件详情
        this.title = '事件概况'
        this.detailList = obj.value
        this.showFlag = true
      }
    },
  },
}
</script>

<style lang="less" scoped>
.midBottomWrap {
  width: 3281px;
  height: 565px;
  background: url('~@/assets/common/botBg.png') no-repeat center center;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding-top: 77px;
  padding-bottom: 22px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .midBoTop {
    display: flex;
    align-items: center;
    width: 100%;

    .leftLine {
      flex: 1;
      margin-right: 19px;
    }
    .tagLine {
      width: 4px;
      height: 56px;
      background: linear-gradient(0deg, #caffff, #00c0ff, #ffffff, #caffff);
      box-shadow: 0px 4px 9px 0px rgba(0, 0, 0, 0.29);
      opacity: 0.5;
      margin: 0 30px;
    }
    .RightLine {
      flex: 1;
      margin-left: 19px;
    }
    .tabTxtClass {
      font-size: 54px;
      font-family: Source Han Sans SC;
      font-weight: bold;
      color: rgba(255, 255, 255, 0.5);
      // opacity: 0.5;
      text-shadow: 0px 4px 9px rgba(0, 0, 0, 0.5);
      background: linear-gradient(0deg, #caffff 0%, #00c0ff 50%, #ffffff 0%, #caffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      cursor: pointer;
      transition: 0.3s;
    }
    .tabTxtActice {
      text-shadow: none !important;
      background: linear-gradient(0deg, #caffff 0%, #00c0ff 50%, #caffff 0%, #ffffff 100%) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }
  }
  .contentWrap {
    width: 3160px;
    height: 351px;
  }
}
</style>
