<!--接入方式分布-->
<template>
  <div>
    <titleTwo :titleText="'接入方式分布'"></titleTwo>
    <pie3D :id="'warningCharts4'" :options="chartsData" :widthCustom="935" :height-custom="307" :legendCustom="legend" :internalDiameterRatio="0" :scaleCustom="1.5" :zHeight="0.5"></pie3D>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import pie3D from "@/components/pie3D";
export default {
  name: "distribute",
  data() {
    return {
      chartsData:[
        {
          name:"平台接入",
          value:938
        },
        {
          name:"设备直连",
          value:1075
        },
        {
          name:"其他",
          value:1138
        }
      ],
      legend:{
        itemWidth: 30,
        itemHeight: 15,
        itemGap: 10,
        left: '55%',
        top: '30%',
      }
    }
  },
  components:{
    titleTwo,
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>