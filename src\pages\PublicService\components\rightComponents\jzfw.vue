<!--居住服务-->
<template>
  <div class="rightThree">
    <Ktitle titleText="居住服务" :width="'950'" />
    <div class="jzfwWrap">
      <div class="partWrap">
        <div class="partop">
          <div class="xsmjTxt">商品房销售面积</div>
          <div class="tntClass">
            <div class="numClass">8.85</div>
            <div class="unitClass">万平方米</div>
          </div>
        </div>
        <div class="parbot"></div>
      </div>
    </div>

    <div class="jzfwWrap">
      <div class="partWrap">
        <div class="partopTwo">
          <div class="xsmjTxt">商品房销售额</div>
          <div class="tntClassTwo">
            <div class="numClass">16.17</div>
            <div class="unitClass">亿元</div>
          </div>
        </div>
        <div class="parbotTwo"></div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
export default {
  name: "jzfw",
  components:{
    Ktitle
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .rightThree {
    width: 940px;
    height: fit-content;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 70px;

    .xsmjTxt {
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #FFFFFF;
      margin-left: 155px;
      margin-right: 30px;
    }

    .tntClass {
      display: flex;
      align-items: baseline;
      background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #00C0FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bolder;

      .numClass {
        font-size: 60px;
        font-family: BN;
      }

      .unitClass {
        font-size: 32px;

      }
    }

    .tntClassTwo {
      display: flex;
      align-items: baseline;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-weight: bolder;

      .numClass {
        font-size: 60px;
        font-family: BN;
      }

      .unitClass {
        font-size: 32px;

      }
    }

  // background-color: bisque;
  .jzfwWrap {
    flex: 1;
    width: 100%;
    // background-color: bisque;
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    box-sizing: border-box;

    .partWrap {
      display: flex;
      flex-direction: column;
      // align-items: center;
      padding: 0 0 0 70px;
      box-sizing: border-box;

      .partop {
        width: 751px;
        height: 176px;
        background: url('~@/assets/publicService/xsmj.png') no-repeat center center;
        background-size: 100% 100%;

        display: flex;
        align-items: center;
      }

      .parbot {
        width: 713px;
        height: 167px;
        background: url('~@/assets/publicService/xseBg.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .partopTwo {
        width: 751px;
        height: 176px;
        background: url('~@/assets/publicService/xse.png') no-repeat center center;
        background-size: 100% 100%;
        display: flex;
        align-items: center;
      }

      .parbotTwo {
        width: 713px;
        height: 167px;
        background: url('~@/assets/publicService/xsmjBg.png') no-repeat center center;
        background-size: 100% 100%;
      }


    }
  }
}
</style>