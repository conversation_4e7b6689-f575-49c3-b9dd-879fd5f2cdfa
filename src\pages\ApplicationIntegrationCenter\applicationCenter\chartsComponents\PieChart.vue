<template>
  <div id="pieChart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: '招商招才',
          value: 1,
        },
        {
          name: '党群工作部',
          value: 1,
        },
        {
          name: '党政综合部',
          value: 3,
        },
        {
          name: '国资监管中心',
          value: 1,
        },
        {
          name: '建设更新部',
          value: 2,
        },
        {
          name: '经济发展部',
          value: 5,
        },
        {
          name: '社会事务部',
          value: 3,
        },
        {
          name: '社会治理部',
          value: 4,
        },
        {
          name: '乡村振兴部',
          value: 2,
        },
        {
          name: '行政服务中心',
          value: 2,
        },
        {
          name: '宣传部',
          value: 1,
        },
        {
          name: '行政执法分局',
          value: 1,
        },
        {
          name: '公安局江南分局',
          value: 3,
        },
        {
          name: '三江街道',
          value: 1,
        },
        {
          name: '苏孟乡',
          value: 1,
        }
      ],
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChartsPie()
    })
  },
  methods: {
    initChartsPie () {
      let that = this
      let myChart = this.$echarts.init(document.getElementById('pieChart'))
      let imgUrl = require('@/assets/common/piebg.png')
      let option = {
        color: [
          '#00C0FF',
          '#FFD461',
          '#B76FD8',
          '#FF4949',
          '#FD852E',
          '#FF4949',
          '#0594C3',
          '#009D9D',
          '#A47905',
        ],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '55%',
          // top: '15%',
          top: '0%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 10,
          textStyle: {
            rich: {
              name: {
                fontSize: 24,
                color: '#ffffff',
                padding: [0, 20, 0, 15],
              },
              value: {
                fontSize: 24,
                color: '#FFC460'
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            // console.log('图白哦',name,p,option.series[0].data)
            that.serverNum = total
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '}  {value|' + p + '%}'
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'logo',
            left: '3%',
            top: '17%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [1.5, 1.5], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '55%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: false,
            },
            data: this.chartsData,
          },
        ],
      }
      myChart.setOption(option)
    },
  }
}
</script>

<style scoped lang="less">
#pieChart {
  width: 100%;
  height: 100%;
  // background-color: blueviolet;
}
</style>