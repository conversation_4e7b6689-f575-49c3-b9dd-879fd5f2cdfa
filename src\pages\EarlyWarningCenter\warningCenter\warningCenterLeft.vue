<template>
  <div class="warnningLeft">
    <ktitle :titleText="'预警接入'"></ktitle>
    <warningNums></warningNums>
    <div class="trendAndTop">
      <trend></trend>
      <top></top>
    </div>
    <div class="LevelAndMethod">
      <level></level>
      <distribute></distribute>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import warningNums from "@/pages/EarlyWarningCenter/warningCenter/warningLeftComponents/warningNums";
import trend from "@/pages/EarlyWarningCenter/warningCenter/warningLeftComponents/trend";
import top from "@/pages/EarlyWarningCenter/warningCenter/warningLeftComponents/top";
import level from "@/pages/EarlyWarningCenter/warningCenter/warningLeftComponents/level";
import distribute from "@/pages/EarlyWarningCenter/warningCenter/warningLeftComponents/distribute";
export default {
  name:"warningCenterLeft",
  components:{
    ktitle,
    warningNums,
    trend,
    top,
    level,
    distribute
  }
}
</script>

<style scoped lang="less">
  .warnningLeft {
    margin: 80px 68px 0 68px;
    overflow: hidden;
    .trendAndTop {
      width: 1935.6px;
      height: 448px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .LevelAndMethod {
      width: 1935.6px;
      height: 368px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
</style>