<template>
  <div class="container">
    <div class="filterContainer">
      <TimeFilter ref="timeFilter" />
    </div>
    <div class="innerLeft">
      <wrapbox bg="left">
        <soLeft class="animate__animated animate__fadeInLeft"></soLeft>
      </wrapbox>
    </div>
    <div class="innerCenter animate__animated animate__fadeIn">
      <centerWrapBox>
        <soCenter></soCenter>
      </centerWrapBox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <soRight class="animate__animated animate__fadeInRight"></soRight>
      </wrapbox>
    </div>
  </div>
</template>

<script>
import wrapbox from "@/components/wrapbox"
import centerWrapBox from "@/components/mapComponent/centerWrapBox"
import soLeft from "@/pages/SafeAndOrderly/components/soLeft"
import soRight from "@/pages/SafeAndOrderly/components/soRight"
import soCenter from "@/pages/SafeAndOrderly/components/soCenter"
import TimeFilter from "@/components/TimeFilter.vue"

export default {
  name: "index",
  data () {
    return {}
  },
  components: {
    soLeft,
    soRight,
    soCenter,
    wrapbox,
    centerWrapBox,
    TimeFilter
  },
  computed: {},
  mounted () {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: calc(100% - 230px);
  z-index: 999;
  margin-top: 230px;
  background: url("@/assets/home/<USER>");
  background-size: cover;
  display: flex;
  justify-content: space-evenly;
  align-items: center;

  .filterContainer {
    position: absolute;
    top: -3.3%;
    right: 1.5%;
  }

  .innerLeft {
    // margin-top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }

  .innerCenter {
    // margin-top: 229px;
    z-index: 2;
  }

  .innerRight {
    // margin-top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
}</style>