<!--团建发展-->
<template>
  <div class="teamBuild">
    <div class="team-item" v-for="(item,i) in teamList" :key="i" :style="{background:'url('+item.img+')'}">
      <div class="info">
        <div class="name">{{item.name}}</div>
        <div class="value"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "team",
  data() {
    return {
      teamList:[
        {
          name:"团日主题活动数量",
          value:2980,
          unit:"个",
          img:require("@/assets/partyBuilding/团建发展1.png")
        },
        {
          name:"团日主题活动参与人数",
          value:6580,
          unit:"人",
          img:require("@/assets/partyBuilding/团建发展2.png")
        },
        {
          name:"当天签到总人数",
          value:600,
          unit:"人",
          img:require("@/assets/partyBuilding/团建发展3.png")
        },
        {
          name:"实时在岗总人数",
          value:300,
          unit:"人",
          img:require("@/assets/partyBuilding/团建发展4.png")
        },
        {
          name:"服务总时长",
          value:46172.9,
          unit:"小时",
          img:require("@/assets/partyBuilding/团建发展5.png")
        },
        {
          name:"志愿服务队数量",
          value:353,
          unit:"个",
          img:require("@/assets/partyBuilding/团建发展6.png")
        },
        {
          name:"志愿服务类型数量",
          value:24,
          unit:"类",
          img:require("@/assets/partyBuilding/团建发展7.png")
        },
        {
          name:"志愿者注册总人数",
          value:65131,
          unit:"人",
          img:require("@/assets/partyBuilding/团建发展8.png")
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .teamBuild {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 108px;
    height: 508px;
    .team-item {
      width:458px;
      height: 233px;
      background-size: cover;
      .info {
        margin: 43px 0 0 133px;
        .name {
          width: 268px;
          height: 71px;
          word-break: break-all;
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
        }
        .value {
          font-size: 57px;
          font-family: DIN;
          font-weight: 500;
          color: #FFFFFF;
          margin-top: 6px;
        }
        .unit {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
        }
      }
    }
  }
</style>