<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="change">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="charts14" id="charts14"></div>
  </div>
</template>

<script>
export default {
  name: 'charts4',
  data() {
    return {
      value: 2, //预警数量分析默认
      options: [
        { label: '年度', value: 1 },
        { label: '季度', value: 4 },
        { label: '月度', value: 2 },
      ], //预警数量下拉列表
      charts2Data: [
        {
          name: '开发区政法办',
          value: 12,
        },
        {
          name: '苏孟乡',
          value: 8,
        },
        {
          name: '秋滨街道',
          value: 16,
        },
        {
          name: '三江街道',
          value: 6,
        },
        {
          name: '西关街道',
          value: 16,
        },
        {
          name: '江南街道',
          value: 15,
        },
        {
          name: '汤溪镇',
          value: 15,
        },
        {
          name: '罗埠镇',
          value: 4,
        },
        {
          name: '洋埠镇',
          value: 3,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts4()
  },
  methods: {
    initCharts4() {
      let myChart = this.$echarts.init(document.getElementById('charts14'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '0%',
          right: '6%',
          top: '22%',
          bottom: '-5%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.charts2Data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate: -20,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '预警数量/个',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '预警个数',
            type: 'bar',
            barWidth: 30,
            barGap: 0.5,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#00C0FF',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,192,255,0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.charts2Data.map((item) => item.value),
          },
        ]
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    change(res) {
      switch (res) {
        case 1:
          this.charts2Data = [
            {
              name: '开发区政法办',
              value: 12,
            },
            {
              name: '苏孟乡',
              value: 8,
            },
            {
              name: '秋滨街道',
              value: 16,
            },
            {
              name: '三江街道',
              value: 6,
            },
            {
              name: '西关街道',
              value: 16,
            },
            {
              name: '江南街道',
              value: 15,
            },
            {
              name: '汤溪镇',
              value: 15,
            },
            {
              name: '罗埠镇',
              value: 4,
            },
            {
              name: '洋埠镇',
              value: 3,
            },
          ];
          this.initCharts4();
          break;
        case 4:
          this.charts2Data = [
            {
              name: '开发区政法办',
              value: 2,
            },
            {
              name: '苏孟乡',
              value: 3,
            },
            {
              name: '秋滨街道',
              value: 1,
            },
            {
              name: '三江街道',
              value: 4,
            },
            {
              name: '西关街道',
              value: 2,
            },
            {
              name: '江南街道',
              value: 5,
            },
            {
              name: '汤溪镇',
              value: 3,
            },
            {
              name: '罗埠镇',
              value: 4,
            },
            {
              name: '洋埠镇',
              value: 1,
            },
          ];
          this.initCharts4();
          break;
        case 2:
          this.charts2Data = [
            {
              name: '开发区政法办',
              value: 1,
            },
            {
              name: '苏孟乡',
              value: 2,
            },
            {
              name: '秋滨街道',
              value: 1,
            },
            {
              name: '三江街道',
              value: 3,
            },
            {
              name: '西关街道',
              value: 2,
            },
            {
              name: '江南街道',
              value: 5,
            },
            {
              name: '汤溪镇',
              value: 3,
            },
            {
              name: '罗埠镇',
              value: 7,
            },
            {
              name: '洋埠镇',
              value: 1,
            },
          ];
          this.initCharts4();
          break;
      }
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 60px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #fefefe;
  }
}
.charts14 {
  width: 931px;
  height: 300px;
  margin-top: 26px;
}
</style>
