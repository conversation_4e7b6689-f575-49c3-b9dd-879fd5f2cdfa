<template>
  <div id="lyfwChart"></div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      let myChart = this.$echarts.init(document.getElementById("lyfwChart"))

      var data2 = [1, 2, 2, 1, 8, 5, 7, 4, 2, 8]
      var data1 = []
      for (var i = 0; i < data2.length; i++) {
        data1.push({
          value: 8,
          label: data2[i],
        })
      }
      var barWidth = 21
      let option = {
        grid: {
          top: "25%",
          left: "10%",
          right: "0%",
          bottom: "15%",
          // containLabel: true,
        },
        legend: {
          show: true,
          // icon: "circle",
          right: '5%',
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false },
          data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月"],
        },
        yAxis: {
          name: "单位：万人",
          type: 'value',
          nameTextStyle: {
            fontSize: 28,
            color: "#D6E7F9",
            padding: [10, 0, 20, 10],
          },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            // 上半截柱子
            // name: "接待游客人数",
            type: "bar",
            barWidth: barWidth,
            barGap: "-100%",
            z: 0,
            itemStyle: {
              color: "#0D4C71",
              opacity: 1,
            },
            data: data1,
          },
          {
            //下半截柱子
            name: "接待游客人数",
            type: "bar",
            barWidth: barWidth,
            barGap: "-100%",
            itemStyle: {
              opacity: 1,
              color: function (params) {
                return new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#00C0FF", // 0% 处的颜色
                      // color: "red", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#84FFF2", // 100% 处的颜色
                    },
                  ],
                  false
                )
              },
            },
            data: data2,
          }
        ],
      }

      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
#lyfwChart {
  width: 100%;
  height: 450px;
}
</style>