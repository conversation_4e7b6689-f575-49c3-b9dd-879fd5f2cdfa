<template>
  <div id="zzy<PERSON><PERSON><PERSON>"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: "1月",
          value: 7
        },
        {
          name: "2月",
          value: 48
        },
        {
          name: "3月",
          value: 17
        },
        {
          name: "4月",
          value: 8
        },
        {
          name: "5月",
          value: 17
        },
        {
          name: "6月",
          value: 27
        },
        {
          name: "7月",
          value: 38
        },
        {
          name: "8月",
          value: 17
        },
        {
          name: "9月",
          value: 27
        },
        {
          name: "10月",
          value: 38
        },
        {
          name: "11月",
          value: 18
        },
        {
          name: "12月",
          value: 7
        }
      ]
    }
  },
  mounted () {
    this.$nextTick(() => {
      var serveTBar = this.$echarts.init(document.getElementById('zzytzChart'))
      serveTBar.setOption(this.getEcharts3DBar())
    })
  },
  methods: {
    getEcharts3DBar () {
      var colorArr = ["#0C628C", "#3887D5", "#2570BB"]
      var color = {
        type: "linear",
        x: 0,
        x2: 1,
        y: 0,
        y2: 0,
        colorStops: [
          {
            offset: 0,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[0],
          },
          {
            offset: 0.5,
            color: colorArr[1],
          },
          {
            offset: 1,
            color: colorArr[1],
          },
        ],
      }
      var barWidth = 38
      var option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          x: '5%',
          x2: '0%',
          y: '20%',
          y2: '15%',
        },
        legend: {
          show: true,
          icon: "circle",
          right: '5%',
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          data: this.chartsData.map(item => item.name),
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false }
        },
        yAxis: {
          name: "单位：%",
          type: 'value',
          nameTextStyle: {
            fontSize: 28,
            color: "#D6E7F9",
            padding: [10, 0, 20, 10],
          },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            z: 1,
            name: '投资增速',
            type: "bar",
            barWidth: barWidth,
            barGap: "0%",
            data: this.chartsData,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(44, 212, 153, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(152, 222, 61, 0.85)",
                  },
                ]),
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: "#00C0FF",
                //   },
                //   {
                //     offset: 1,
                //     color: "#0A789C",
                //   },
                // ]),
                // barBorderRadius: 4,
              },
            },
          },
          { // 底部方块
            z: 2,
            name: '投资增速',
            type: "pictorialBar",
            data: this.chartsData,
            symbol: "diamond",
            symbolOffset: ["0%", "60%"],
            symbolSize: [barWidth, 12],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(152, 222, 61, 0.85)",
                  },
                  {
                    offset: 1,
                    color: "rgba(152, 222, 61, 0.85)",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
          { // 顶部方块
            z: 3,
            name: '投资增速',
            type: "pictorialBar",
            symbolPosition: "end",
            data: this.chartsData,
            symbol: "diamond",
            symbolOffset: ["-1%", "-55%"],
            symbolSize: [barWidth + 2, (10 * (barWidth + 28)) / barWidth],
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "rgba(44, 212, 153, 1)",
                  },
                  {
                    offset: 1,
                    color: "rgba(44, 212, 153, 1)",
                  },
                ]),
                // barBorderRadius: 4,
              },
            },
            tooltip: {
              show: false,
            },
          },
        ],
      }
      return option
    }
  }
}
</script>

<style scoped lang="less">
#zzytzChart {
  width: 100%;
  height: 100%;
}
</style>