<template>
  <div class="bottomContainer">
    <WarningSelect @warningCheckChange="triggerGetFilterMapPointLayer"/>
    <PointSelect class="right" @levelCheckChange="triggerGetFilterMapPointLayer"/>
  </div>
</template>

<script>
import WarningSelect from "@/pages/EarlyWarningCenter/warningCenter/warningBottomComponents/WarningSelect";
import PointSelect from "@/pages/EarlyWarningCenter/warningCenter/warningBottomComponents/PointSelect";
import {mapGetters} from "vuex";
import {getIotMaFilterList} from "@/api/warning";
export default {
  name:"warningCenterBottom",
  components:{
    WarningSelect,
    PointSelect
  },
  data() {
    return {
      levelList: [
        {
          label:"紧急",
          value:1
        },
        {
          label:"重要",
          value:2
        },
        {
          label:"普通",
          value:3
        },
        {
          label:"提示",
          value:4
        },
        {
          label:"未确认",
          value:5
        },
      ],
      timer: null
    }
  },
  computed: {
    ...mapGetters(['warningCheckList','levelCheckList'])
  },
  mounted() {
    this.cleanAllWarningLayers()
  },
  methods: {
    //根据左右筛选条件调用接口获取筛选出的点位列表
    async getFilterMapPointLayer() {
      try {
        this.cleanAllWarningLayers();
        const sourceList = this.unique(this.warningCheckList.map(item => item.source));
        const nameList = this.warningCheckList.map(item => item.value);
        const levelList = this.levelCheckList.map(item => item.value);

        // 调用API获取数据
        const res = await getIotMaFilterList({ level: levelList, source: sourceList, pk: nameList });

        if (res.data && res.data.data.length > 0) {
          const list = res.data.data.map(item => ({
            level: item.level,
            title: item.devId,
            name: item.pkName,
            time: item.warnTime,
            content: item.describe,
            source: item.source,
            lng: item.lon,
            lat: item.lat,
            levelDescribe: this.getLabel(this.levelList, item.level),
            type: item.source === 1 ? "iotWarn" : "ManualWarn",
            icon: this.generateIconPath(item),
            layerid: this.generateLayerId(item),
          }));

          const groupArr = Object.entries(this.groupBySource(list, "layerid"));
          groupArr.forEach(([layerId, pointData]) => {
            const icon = pointData[0].icon;
            this.$EventBus.$emit('CommonDrawPoint', pointData, layerId, icon, true, true, true);
          });
        }
      } catch (error) {
        console.error('Failed to get filter map point layer:', error);
        // 可以在这里添加错误处理逻辑，比如用户提示等
      }
    },
    //清除原有的所有图层
    cleanAllWarningLayers() {
      // 确保 top.mapUtil 和 top.mapUtil.layers 是可用的
      if (!top || !top.mapUtil || typeof top.mapUtil.layers !== 'object') {
        console.error('mapUtil or mapUtil.layers is not available');
        return;
      }

      // 使用常量定义正则表达式，以提高代码的可维护性
      const mapWarningLayerRegex = /MapWarningList/;

      // 获取所有层的键，并筛选出匹配 MapWarningList 的层
      const layerKeys = Object.keys(top.mapUtil.layers).filter(item => mapWarningLayerRegex.test(item));

      // 检查是否有匹配的层
      if (layerKeys.length === 0) {
        console.log('No warning layers found to clean');
        return;
      }

      // 安全地移除匹配的层
      try {
        top.mapUtil.removeAllLayers(layerKeys);
      } catch (error) {
        console.error('Failed to remove warning layers:', error);
      }
    },
    generateIconPath(item) {
      // 将生成icon路径的逻辑封装成函数，提高代码的可维护性
      const sourceType = item.source === 1 ? item.name : "手动录入";
      return `/map/img/${sourceType}-${item.level}.png`;
    },
    generateLayerId(item) {
      // 将生成layerid的逻辑封装成函数，提高代码的可维护性
      const sourceType = item.source === 1 ? item.name : "手动录入";
      return `MapWarningList-${sourceType}-${item.level}`;
    },
    //根据某个字段对数组进行分类
    groupBySource(array,key) {
      return array.reduce((accumulator, currentItem) => {
        if (!accumulator[currentItem[key]]) {
          accumulator[currentItem[key]] = [];
        }
        accumulator[currentItem[key]].push(currentItem);
        return accumulator;
      }, {});
    },
    //字典翻译
    getLabel(arr, value) {
      const item = arr.find(item => item.value === value);
      if (item === undefined) {
        // 处理未找到匹配项的情况，可能是抛出错误或返回默认值
        // throw new Error(`Value "${value}" not found in array.`);
        // 或者 return "默认标签"; 依赖具体业务需求
        return ""
      }
      return item.label;
    },
    //数组去重
    unique(arr) {
      return [...new Set(arr)];
    },
    // 手动实现的防抖函数 避免频繁勾选时调用getFilterMapPointLayer重复上图
    triggerGetFilterMapPointLayer() {
      clearTimeout(this.timer); // 清除之前的计时器
      this.timer = setTimeout(() => {
        this.getFilterMapPointLayer();
      }, 1500); // 设置新的延迟调用
    },
  },
  destroyed() {
    this.cleanAllWarningLayers()
  }
}
</script>

<style scoped lang="less">
  .bottomContainer {
    width: fit-content;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right {
      position: absolute;
      left: 2820px;
    }
  }
</style>