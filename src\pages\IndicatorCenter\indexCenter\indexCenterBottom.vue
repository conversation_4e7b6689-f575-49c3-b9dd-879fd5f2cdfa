<!--指标中心左侧页面-->
<template>
  <div class="bottomContainer">
    <titleTwo :titleText="'开发区运行指数'" width="900" :showDate="false"></titleTwo>
    <div class="radarChart" id="radarChart"></div>
    <titleTwo :titleText="'运行指数趋势图'" width="900" :showDate="false"></titleTwo>
    <div class="yxzsqsBottom" id="yxzsqsBottom"></div>
  </div>
</template>

<script>
import { getJscjhyxzs, getJscyxzsqst } from '@/api/IndicatorCenter'
import titleTwo from '@/components/titleCommon.vue'
export default {
  name: 'indexCenterBottom',
  data() {
    return {
      charts2Data: [
        {
          name: '产业经济',
          id: 8,
          list: [
            {
              sj: '2024-10',
              zs: null,
              bfb: 0.9789,
            },
            {
              sj: '2024-11',
              zs: null,
              bfb: 0.9822,
            },
            {
              sj: '2024-12',
              zs: null,
              bfb: 0.9154,
            },
            {
              sj: '2025-01',
              zs: null,
              bfb: 0.9452,
            },
            {
              sj: '2025-02',
              zs: null,
              bfb: 0.9801,
            },
            {
              sj: '2025-03',
              zs: null,
              bfb: 0.9234,
            },
          ],
        },
        {
          name: '城市治理',
          id: 9,
          list: [
            {
              sj: '2024-10',
              zs: null,
              bfb: 0.9563,
            },
            {
              sj: '2024-11',
              zs: null,
              bfb: 0.9893,
            },
            {
              sj: '2024-12',
              zs: null,
              bfb: 0.9038,
            },
            {
              sj: '2025-01',
              zs: null,
              bfb: 0.9782,
            },
            {
              sj: '2025-02',
              zs: null,
              bfb: 0.9788,
            },
            {
              sj: '2025-03',
              zs: null,
              bfb: 0.909,
            },
          ],
        },
        {
          name: '公共服务',
          id: 10,
          list: [
            {
              sj: '2024-10',
              zs: null,
              bfb: 0.9328,
            },
            {
              sj: '2024-11',
              zs: null,
              bfb: 0.9684,
            },
            {
              sj: '2024-12',
              zs: null,
              bfb: 0.9301,
            },
            {
              sj: '2025-01',
              zs: null,
              bfb: 0.9837,
            },
            {
              sj: '2025-02',
              zs: null,
              bfb: 0.9326,
            },
            {
              sj: '2025-03',
              zs: null,
              bfb: 0.9874,
            },
          ],
        },
        {
          name: '生态宜居',
          id: 11,
          list: [
            {
              sj: '2024-10',
              zs: null,
              bfb: 0.9134,
            },
            {
              sj: '2024-11',
              zs: null,
              bfb: 0.9584,
            },
            {
              sj: '2024-12',
              zs: null,
              bfb: 0.9737,
            },
            {
              sj: '2025-01',
              zs: null,
              bfb: 0.9758,
            },
            {
              sj: '2025-02',
              zs: null,
              bfb: 0.9771,
            },
            {
              sj: '2025-03',
              zs: null,
              bfb: 0.9193,
            },
          ],
        },
        {
          name: '其他',
          id: 38,
          list: [
            {
              sj: '2024-10',
              zs: null,
              bfb: 0.9639,
            },
            {
              sj: '2024-11',
              zs: null,
              bfb: 0.969,
            },
            {
              sj: '2024-12',
              zs: null,
              bfb: 0.9531,
            },
            {
              sj: '2025-01',
              zs: null,
              bfb: 0.9614,
            },
            {
              sj: '2025-02',
              zs: null,
              bfb: 0.9592,
            },
            {
              sj: '2025-03',
              zs: null,
              bfb: 0.9209,
            },
          ],
        },
      ],
      radarData: [],
    }
  },
  components: {
    titleTwo,
  },
  computed: {},
  mounted() {
    this.getData()
    // this.getData1()
    this.initCharts3()
  },
  methods: {
    getData() {
      getJscjhyxzs().then((res) => {
        let resdata = res.data.data
        resdata.forEach((item) => {
          item.value = item.bfb.toFixed(1)
          item.max = 100
        })
        this.radarData = resdata
        console.log('111', this.radarData)
        this.initRadarChart()
      })
    },
    getData1() {
      getJscyxzsqst().then((res) => {
        let resdata = res.data.data
        this.charts2Data = resdata
        console.log('111', this.charts2Data)
        this.initCharts3()
      })
    },
    initRadarChart() {
      let myChart = this.$echarts.init(document.getElementById('radarChart'))
      let total = 0
      let tarValue = 0
      this.radarData.forEach((item) => {
        total += Number(item.value)
      })
      tarValue = (total / this.radarData.length).toFixed(1)
      let option = {
        radar: {
          center: ['50%', '50%'],
          radius: '60%',
          splitNumber: 4,
          shape: 'polygon',
          name: {
            textStyle: {
              color: '#D6E7F9',
              fontSize: 28,
              padding: [3, 5],
            },
          },
          nameGap: 45,
          splitArea: {
            areaStyle: {
              color: ['rgba(0,192,255,0.05)', 'rgba(0,192,255,0.1)', 'rgba(0,192,255,0.15)', 'rgba(0,192,255,0.2)'],
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0,192,255,0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(0,192,255,0.3)',
            },
          },
          indicator: this.radarData,
        },
        series: [
          {
            type: 'radar',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#FFC460',
            },
            lineStyle: {
              color: '#FFC460',
              width: 2,
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(255, 196, 96, 0.8)' },
                { offset: 1, color: 'rgba(255, 196, 96, 0.2)' },
              ]),
            },
            data: [
              {
                value: this.radarData.map((item) => item.value),
                label: {
                  show: true,
                  formatter: function (params) {
                    return params.value
                  },
                  color: '#fff',
                  fontSize: 28,
                  distance: 15,
                  position: 'outside',
                },
              },
            ],
          },
        ],
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: '42%',
            style: {
              text: tarValue,
              textAlign: 'center',
              fill: '#FFC460',
              fontSize: 88,
            },
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
      myChart.getZr().on('click', (params) => {
        if (params.topTarget && params.topTarget.style.text) {
          this.radarData.forEach((item) => {
            if (item.name == params.topTarget.style.text) {
              this.$emit('openDialog', item)
            }
          })
        }
      })
    },
    initCharts3() {
      let listData = []
      let colorArr = ['#00C0FF', '#B76FD8', '#FFC460', '#A9DB52']
      this.charts2Data.map((item, index) => {
        listData.push({
          name: item.name,
          type: 'line',
          barWidth: 30,
          smooth: false,
          symbolSize: 10,
          itemStyle: {
            normal: {
              color: colorArr[index],
            },
          },
          data: this.charts2Data[index].list.map((item) => (Number(item.bfb) * 100).toFixed(2)),
        })
      })
      let myChart = this.$echarts.init(document.getElementById('yxzsqsBottom'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 14, // 设置宽度
          itemHeight: 14, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: '#D6E7F9',
            fontSize: 25,
          },
          right: '24%',
          top: '8%',
        },
        grid: {
          left: '0%',
          right: '0%',
          top: '20%',
          bottom: '2%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.charts2Data[0].list.map((item) => item.sj),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '',
            type: 'value',
            min:80,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, 30, 20, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: listData,
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.bottomContainer {
  margin: 80px 0 0 68px;
  .radarChart {
    width: 935px;
    height: 840px;
    margin-bottom: 40px;
  }
  .yxzsqsBottom {
    width: 935px;
    height: 780px;
  }
}
</style>