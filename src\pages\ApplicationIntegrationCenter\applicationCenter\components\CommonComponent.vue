<template>
  <div class="commonContainer">
    <Ktitle :titleText="title" />
    <div class="iframeWrap">
      <div class="iframeContainer" @click="emitParent">
        <iframe v-if="url" :src="url" class="iframe" frameborder="no"></iframe>
        <div v-else class="iframeBg"></div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  components: {
    Ktitle
  },
  methods: {
    emitParent () {
      this.$emit('showIfram', this.url,this.type)
    },
  }


}
</script>

<style scoped lang="less">
.commonContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  // background-color: coral;
  .iframeWrap {
    flex: 1;
    width: 100%;
    // background-color: antiquewhite;
    display: flex;
    justify-content: center;
    align-items: center;

    .iframeContainer {
      width: 1457px;
      height: 783px;
      background: url('~@/assets/application/bk.png') no-repeat center center;
      // background: url('~@/assets/application/sbk.png') no-repeat center center;
      background-size: 100% 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .iframe {
        cursor: pointer;
        width: 1347px;
        height: 701px;
        // background: url('~@/assets/application/iframe.png') no-repeat center center;
        background-size: 100% 100%;
        pointer-events: none;
      }

      .iframeBg {
        width: 528px;
        height: 308px;
        cursor: pointer;
        background: url("~@/assets/application/iframeBg.png") no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
}
</style>