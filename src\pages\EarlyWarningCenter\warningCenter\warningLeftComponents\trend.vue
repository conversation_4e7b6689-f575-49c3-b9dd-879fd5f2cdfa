<!--预警数趋势-->
<template>
  <div>
    <titleTwo :titleText="'预警数趋势'"></titleTwo>
    <div class="warningCharts1" id="warningCharts1"></div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import {getWarningQs} from "@/api/home";
export default {
  name: "trend",
  data() {
    return {
      data0:[],
      charts2Data: []
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.$nextTick(()=>{
      this.initCharts3()
    })
  },
  methods: {
    getEcharts3DBar() {
      getWarningQs({type: 3}).then(res => {
        this.data0 = res.data.data.map(item => ({
          name: item.name,
          value: item.num,
        }))
        var serveTBar = this.$echarts.init(document.getElementById('warningCharts1'));
        var colorArr = ["#0C628C", "#3887D5", "#2570BB"];
        var color = {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: colorArr[0],
            },
            {
              offset: 0.5,
              color: colorArr[0],
            },
            {
              offset: 0.5,
              color: colorArr[1],
            },
            {
              offset: 1,
              color: colorArr[1],
            },
          ],
        };
        var barWidth = 38;
        var option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            x: '10%',
            x2: '0%',
            y: '18%',
            y2: '15%',
          },
          legend: {
            show: false,
            data: ['本期', '同期'],
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          xAxis: {
            data: this.data0.map(item=>item.name),
            //坐标轴
            axisLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: '#77B3F1'
              },
              textStyle: {
                color: '#fff',
                fontSize: '28'
              }
            },
            type: 'category',
            axisLabel: {
              textStyle: {
                color: '#C5DFFB',
                fontWeight: 500,
                fontSize: '28'
              }
            },
            axisTick: {
              textStyle: {
                color: '#fff',
                fontSize: '28'
              },
              show: false,
            },
            splitLine: { show: false }
          },
          yAxis: {
            name:"单位：数据",
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: "#D6E7F9",
              padding: [10, -100, 10, 10],
            },
            //坐标轴
            axisLine: {
              show: true,
              lineStyle: {
                width: 1,
                color: '#214776'
              },
              textStyle: {
                color: '#fff',
                fontSize: '28'
              }
            },
            axisTick: {
              show: false
            },
            //坐标值标注
            axisLabel: {
              show: true,
              textStyle: {
                color: '#C5DFFB',
                fontSize: '28'
              }
            },
            //分格线
            splitLine: {
              lineStyle: {
                color: '#13365f',
                fontSize: '28'
              }
            }
          },
          series: [
            {
              z: 1,
              name: '本期',
              type: "bar",
              barWidth: barWidth,
              barGap: "0%",
              data: this.data0,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#0A789C",
                    },
                    {
                      offset: 1,
                      color: "#00C0FF ",
                    },
                  ]),
                  // barBorderRadius: 4,
                },
              },
            },
            { // 底部方块
              z: 2,
              name: '本期',
              type: "pictorialBar",
              data: this.data0,
              symbol: "diamond",
              symbolOffset: ["0%", "60%"],
              symbolSize: [barWidth, 12],
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#0A789C",
                    },
                    {
                      offset: 1,
                      color: "#00C0FF ",
                    },
                  ]),
                  // barBorderRadius: 4,
                },
              },
              tooltip: {
                show: false,
              },
            },
            { // 顶部方块
              z: 3,
              name: '本期',
              type: "pictorialBar",
              symbolPosition: "end",
              data: this.data0,
              symbol: "diamond",
              symbolOffset: ["-1%", "-55%"],
              symbolSize: [barWidth+2, (10 * (barWidth+28)) / barWidth],
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#0A789C",
                    },
                    {
                      offset: 1,
                      color: "#00C0FF ",
                    },
                  ]),
                  // barBorderRadius: 4,
                },
              },
              tooltip: {
                show: false,
              },
            },

          ],
        };
        serveTBar.setOption(option);
      })
    },
    initCharts3() {
      getWarningQs({type: 3}).then(res => {
        this.charts2Data = res.data.data.map(item => ({
          name: item.name,
          value1: item.hjjcsb,
          value2: item.jhdc,
          value3: item.kqzsb,
          value4: item.qtyj,
          value5: item.sdrl,
          value6: item.szsb,
          value7: item.zhdg,
          value8: item.zhjg,
          value9: item.zhlgt,
        }))
        let myChart = this.$echarts.init(document.getElementById("warningCharts1"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
            itemWidth: 14, // 设置宽度
            itemHeight: 14, // 设置高度
            itemGap: 16, // 设置间距
            textStyle: {
              color: "#D6E7F9",
              fontSize: 18,
            },
            left:"8%",
            top:"13%"
          },
          grid: {
            left: "8%",
            right: "6%",
            top: "34%",
            bottom: "-2%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts2Data.map(item => item.name),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                rotate: 20,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "预警数量/个",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: [10,-10,80,10],
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: "环境监测设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                },
              },
              data: this.charts2Data.map(item => item.value1),
            },
            {
              name: "金华地磁",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#22E8E8",
                },
              },
              data: this.charts2Data.map(item => item.value2),
            },
            {
              name: "空气站设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#A9DB52",
                },
              },
              data: this.charts2Data.map(item => item.value3),
            },
            {
              name: "其它预警",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#FFC460",
                },
              },
              data: this.charts2Data.map(item => item.value4),
            },
            {
              name: "手动录入",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#fa0423",
                },
              },
              data: this.charts2Data.map(item => item.value5),
            },
            {
              name: "水质设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#f66203",
                },
              },
              data: this.charts2Data.map(item => item.value6),
            },
            {
              name: "智慧灯杆",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#B76FD8',
                },
              },
              data: this.charts2Data.map(item => item.value7),
            },
            {
              name: "智慧井盖",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#e8c991',
                },
              },
              data: this.charts2Data.map(item => item.value8),
            },
            {
              name: "智慧垃圾桶",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#D2D2D2',
                },
              },
              data: this.charts2Data.map(item => item.value9),
            },
          ]
        };
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    }
  },
  watch: {}
}
</script>

<style scoped>
  .warningCharts1 {
    width: 935px;
    height: 390px;
  }
</style>