<template>
  <div id="zczbze<PERSON>hart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: "有限责任公司",
          value: 265.1
        },
        {
          name: "中型企业",
          value: 254.1
        },
        {
          name: "小型企业",
          value: 237.2
        },
        {
          name: "私营企业",
          value: 215.2
        },
        {
          name: "港澳台投资企业",
          value: 68.4
        },
        {
          name: "外商投资企业",
          value: 49.9
        }
      ]
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      const that = this
      let myChart = this.$echarts.init(document.getElementById("zczbzeChart"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: '{b}: <br/> {c}次',
        },
        grid: {
          top: "7%",
          left: "0%",
          right: "0%",
          bottom: "-10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            inside: true,
            textStyle: {
              color: "#D6E7F9",
              verticalAlign: "bottom",
              align: "left",
              padding: [0, 0, 5, 0],
              fontSize: 32
            },
            // formatter: function (params) {
            //   console.log('这是排名index',params)
            //   return ('TOP' + (that.chartsData.findIndex((item => item.name == params)) + 1)).toString() + '  ' + params
            // }
            formatter: function (value, index) {
              if (index == 0) {
                return '{titleOne|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 1) {
                return '{titleTwo|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 2) {
                return '{titleThree|TOP' + (index + 1) + '}' + '  ' + value
              } else {
                return '{title|TOP' + (index + 1) + '}' + '  ' + value
              }
            },
            rich: {
              titleOne: {
                width: 80,
                align: 'left',
                color: '#FF4949',
                fontSize: 32,
                fontWeight: '500'
              },
              titleTwo: {
                width: 80,
                align: 'left',
                color: '#DF8F30',
                fontSize: 32,
                fontWeight: '500'
              },
              titleThree: {
                width: 80,
                align: 'left',
                color: '#B76FD8',
                fontSize: 32,
                fontWeight: '500'
              },
              title: {
                width: 80,
                align: 'left',
                color: '#30B8D5',
                fontSize: 32,
                fontWeight: '500'
              }
            }
          },
          data: this.chartsData.map(item => item.name),
        },
        series: [
          {
            // cursor:"auto",
            type: "bar",
            name: "",
            showBackground: true,
            backgroundStyle: {
              color: '#2E3F54'
            },
            itemStyle: {
              barBorderRadius: [0, 10, 10, 0],
              color: function (params) {
                var colors = [
                  "#4587E7",
                  "#35AB33",
                  "#F5AD1D",
                  "#ff7f50",
                  "#da70d6",
                  "#32cd32",
                  "#6495ed",
                ]
                // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
                if (params.dataIndex == 0) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FF9B78", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#FF4949", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else if (params.dataIndex == 1) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#FAFF78", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#DF8F30", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else if (params.dataIndex == 2) {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#E2B8F6", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#B76FD8", //指100%处的颜色
                    },
                    ],
                    false
                  )
                } else {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [{
                      offset: 0,
                      color: "#2BDAFF", //指0%处的颜色
                    },
                    {
                      offset: 1,
                      color: "#078FF7", //指100%处的颜色
                    },
                    ],
                    false
                  )
                }
                // return colors[params.dataIndex];
              },
            },
            label: {
              show: true,
              position: [800, -40],
              color: "#fff",
              formatter: function (params) {
                return params.value + '亿元'
              },
              fontSize: 32,
              textStyle: {
                color: '#B5DAFF',
                fontFamily: 'Source Han Sans CN'
              }
            },
            barWidth: 8,
            color: "#539FF7",
            data: this.chartsData.map(item => item.value),
          }, {
            name: "",
            type: "bar",
            barWidth: 8,
            barGap: "-100%",
            data: this.chartsData.map(item => 150),
            itemStyle: {
              normal: {
                color: "#2E5171",
              },
            },
            z: 0,
          }]
      }

      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
#zczbzeChart {
  width: 100%;
  height: 100%;
}
</style>