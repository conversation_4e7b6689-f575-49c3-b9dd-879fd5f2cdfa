<!--视频监控-->
<template>
  <div class="rightPart">
    <div class="changWrap">
      <div class="countoWrap">
        <span class="txt_o">视频监控</span>
        <div class="countoContainer">
          <CounTo :val="number" />
        </div>
        <span class="txt_f">路</span>
      </div>
      <div class="freshImgWrap" @click="getVideoCount">
        <img class="freshImg" :src="require(`@/assets/common/fresh.png`)">
        <div class="hypClass">换一批</div>
      </div>
    </div>
    <div class="photoWrap">
      <div class="imgOne">
        <div class="videoItem" id="dwgzDom0"></div>
      </div>
      <div class="imgOne">
        <div class="videoItem" id="dwgzDom1"></div>
      </div>
    </div>
  </div>
</template>

<script>
import CounTo from "@/pages/home/<USER>/midBottom/CounTo.vue"
import venueVideo from "@/components/Video/VenueVideo";
import DHWs from '@/utils/DHWs.js'
import {getVideoNumber} from "@/api/video";
const DHWsInstance = DHWs.getInstance({
  reConnectCount: 2,
  connectionTimeout: 30 * 1000000,
  messageEvents: {
    loginState() {
      console.log('aaaa')
    },
  },
})
export default {
  name: "index",
  components: {
    CounTo,
    venueVideo,
  },
  data() {
    return {
      videoList: [
        "33079951001320081546",
        "33079951001320080457"
      ],
      ws: DHWsInstance,
      isLogin: false,
      number: "0"
    }
  },
  computed: {},
  mounted() {
    this.getVideoCount()
  },
  methods: {
    initVideo() {
      this.videoList = this.videoList.map((item,i) => {return {
        ctrlType: 'playerWin',
        ctrlCode: 'dwgzDom'+i,
        ctrlProperty: {
          displayMode: 1,
          splitNum: 1,
          channelList: [
            {
              channelId: item,
            },
          ],
        },
        visible: true,
        domId: 'dwgzDom'+i,
        dom: document.getElementById('dwgzDom' + i),
      }})
      this.getMinitorData()
    },
    //视频配置相关
    getMinitorData() {
      if (this.isLogin) {
        this.createAllvideo()
      } else {
        this.loginVideo()
      }
    },
    //视频创建
    createAllvideo() {
      let that = this
      setTimeout(function () {
        that.ws.createCtrl(that.videoList)
      }, 400)
    },
    //视频登陆
    loginVideo() {
      // let DHWsInstance = this.ws;
      const _this = this
      this.ws.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          this.ws.login({
            loginIp: '*************',
            loginPort: '7902',
            // userName: 'kfqywtg',
            // userPwd: 'kfq2024$',
            userName: 'csdn',
            userPwd: 'Jhcsdn2024$',
            token: '',
            https:1
          })
          this.ws.on('loginState', (res) => {
            if (res) {
              console.log('登录成功')
              _this.createAllvideo()
              this.isLogin = true
            } else {
              console.log('登录失败')
            }
          })
        } else {
          // 连接客户端失败
          console.log('连接客户端失败')
        }
      })
    },
    //视频销毁
    allVideoDestory() {
      this.ws.destroyCtrl(this.videoList)
    },
    //获取视频总数量
    getVideoCount() {
      getVideoNumber().then(res => {
        this.videoList = res.data.data.list
        this.number = res.data.data.total + "";
        this.initVideo()
      })
    }
  },
  destroyed() {
    this.allVideoDestory()
  },
  watch: {}
}
</script>

<style scoped lang="less">
.rightPart {
  flex: 1;
  height: 100%;
  // background-color: antiquewhite;
  box-sizing: border-box;
  padding-top: 30px;
  .changWrap {
    display: flex;
    justify-content: center;
    align-items: center;
    .countoWrap {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      // background-color: #22e8e8;
      .txt_o {
        font-size: 32px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #ffffff;
        margin-right: 8px;
        margin-bottom: 8px;
        letter-spacing: 2px;
      }
      .txt_t {
        font-size: 40px;
        font-family: Source Han Sans SC;
        font-weight: bolder;
        letter-spacing: 2px;
        color: #ffffff;
        // padding: 10px 0 0 0;
        background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .txt_f {
        font-size: 40px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        letter-spacing: 2px;
        color: #ffffff;
        // padding: 10px 0 0 0;
        background: linear-gradient(180deg, #ffffff 0%, #ffeccb 50.244140625%, #ffffff 53.0029296875%, #ffc460 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .countoContainer {
        margin: 0 8px 0 12px;
      }
    }
    .freshImgWrap {
      display: flex;
      align-items: center;
      margin-left: 90px;
      cursor: pointer;
      .freshImg {
        width: 32px;
        height: 32px;
      }
      .hypClass {
        font-size: 32px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #c0d6ed;
        margin-left: 12px;
      }
    }
  }
  .photoWrap {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 36px;
    box-sizing: border-box;
    // padding: 0 140px;
    .imgOne {
      width: 316px;
      height: 216px;
      background: url('~@/assets/common/spmc.png') no-repeat center center;
      background-size: 100% 100%;
      .videoItem {
        width: 316px;
        height: 216px;
      }
    }
  }
}
</style>