<template>
  <div class="mainContainer">
    <div class="mainContainerInner">
      <div class="innerLeft">
        <Wrapbox bg="left">
          <div class="leftWrapper animate__animated animate__fadeInLeft">
            <div class="leftTop">
              <CommonComponent
                ref="commonComponent"
                :title="appList[0]?appList[0].xtmc:'暂无内容'"
                :url="appList[0]?appList[0].url:null"
                @showIfram="showScreen"
              />
            </div>
            <div class="leftBot">
              <CommonComponent
                ref="commonComponent"
                :title="appList[1]?appList[1].xtmc:'暂无内容'"
                :url="appList[1]?appList[1].url:null"
                @showIfram="showScreen" />
            </div>
          </div>
        </Wrapbox>
      </div>
      <div class="innerMid animate__animated animate__fadeIn">
        <div class="application-midTop">
          <div class="midTop_l">
            <YyglComponent ref="yyglComponent" />
          </div>
          <div class="midTop_m">
            <GpyyComponent ref="gpyyComponent" />
          </div>
          <div class="midTop_r">
            <BmyyComponent ref="bmyyComponent" />
          </div>
        </div>
        <div class="midBot">
          <MidTable ref="midTable" />
        </div>
      </div>
      <div class="innerRight">
        <Wrapbox bg="right">
          <div class="rightWrapper animate__animated animate__fadeInRight">
            <div class="rightTop">
              <CommonComponent
                ref="commonComponent"
                :title="appList[2]?appList[2].xtmc:'暂无内容'"
                :url="appList[2]?appList[2].url:null"
                @showIfram="showScreen"
              />
            </div>
            <div class="rightBot">
              <CommonComponent
                ref="commonComponent"
                :title="appList[3]?appList[3].xtmc:'暂无内容'"
                :url="appList[3]?appList[3].url:null"
                @showIfram="showScreen"
              />
            </div>
          </div>
        </Wrapbox>
      </div>
      <transition enter-active-class="animate__animated animate__fadeIn"
        leave-active-class="animate__animated animate__fadeOut">
        <div v-show="showIframe" class="iframeWindow">
          <iframe v-if="iframeSrc" class="iframeWindowInner" :src="iframeSrc" ref="iframe" frameborder="no">
          </iframe>
          <div v-else class="iframeWindowInnerT"></div>
          <i class="el-icon-error closeBtn" @click="showIframe = false"></i>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import Wrapbox from "@/components/wrapbox.vue"
import MidTable from "@/pages/ApplicationIntegrationCenter/applicationCenter/midComponents/MidTable.vue"
import YyglComponent from "@/pages/ApplicationIntegrationCenter/applicationCenter/midComponents/YyglComponent.vue"
import GpyyComponent from "@/pages/ApplicationIntegrationCenter/applicationCenter/midComponents/GpyyComponent.vue"
import BmyyComponent from "@/pages/ApplicationIntegrationCenter/applicationCenter/midComponents/BmyyComponent.vue"
import CommonComponent from "@/pages/ApplicationIntegrationCenter/applicationCenter/components/CommonComponent"
export default {
  components: {
    Wrapbox,
    MidTable,
    YyglComponent,
    GpyyComponent,
    BmyyComponent,
    CommonComponent
  },
  data () {
    return {
      showIframe: false,
      iframeSrc: '',
      appList: []
    }
  },
  mounted() {
    this.$bus.$on("appList", (res) => {
      this.appList = res
    })
  },
  methods: {
    showScreen (url) {
      if (url) window.open(url)
      // if (type == 'wlpt') {
      //   window.open('http://vividshow.ictsoft.cn/dplus/view/1651476502523387906')
      // } else if (type == 'xnycyy') {
      //   window.open('http://**************/')
      // } else {
      //   console.log('显示弹窗', url)
      //   this.iframeSrc = url
      //   this.showIframe = true
      // }

    }
  }
}
</script>

<style scoped lang="less">
// .innerMid::after {
//   width: 100%;
//   height: 100%;
//   content: "";
//   background: url('~@/assets/application/midBgTwo.png') no-repeat center center;
//   background-size: 100% 100%;
//   opacity: .7;
//   top: 0;
//   left: 0;
//   bottom: 0;
//   right: 0;
//   position: absolute;
//   z-index: -1;
// }

.mainContainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: url('~@/assets/application/dtBg.png') no-repeat center center;
  background-size: 100% 100%;
  // background-color: #071f3a;

  .mainContainerInner {
    width: 100%;
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 0;

    .innerLeft {
      .leftWrapper {
        width: 100%;
        height: 100%;
        // background-color: aqua;
        // display: flex;
        // flex-direction: column;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;

        .leftTop {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 50px 0 0 0;
          // background-color: antiquewhite;
        }

        .leftBot {
          width: 100%;
          height: 100%;
          // background-color: chartreuse;
          box-sizing: border-box;
          padding: 50px 0 0 0;
        }
      }
    }

    .innerMid {
      width: 3426px;
      height: 1927px;
      // background-color: antiquewhite;
      background: url('~@/assets/application/midBgTwo.png') no-repeat center center;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 50px 108px 20px 108px;
      display: flex;
      flex-direction: column;
      // position: relative;

      .application-midTop {
        height: fit-content;
        width: 100%;
        // background-color: bisque;
        display: flex;
        justify-content: space-between;
        height: 853px;
        .midTop_l {
          width: 940px;
          height: 100%;
          // background-color: aqua;
        }

        .midTop_m {
          width: 940px;
          height: 100%;
          // background-color: chocolate;
        }

        .midTop_r {
          width: 940px;
          height: 100%;
          // background-color: chartreuse;
        }
      }

      .midBot {
        width: 100%;
        height: fit-content;
        margin-top: 17px;
        // background-color: aqua;
      }
    }

    .innerRight {
      .rightWrapper {
        width: 100%;
        height: 100%;
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;

        .rightTop {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 50px 0 0 0;
        }

        .rightBot {
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 50px 0 0 0;
        }
      }
    }

    .iframeWindow {
      position: absolute;
      bottom: 2px;
      left: 50%;
      transform: translate(-50%, 0);
      // background-color: blanchedalmond;
      width: 3424px;
      height: 1926px;
      z-index: 2;

      border-width: 0;

      .closeBtn {
        position: absolute;
        top: 20px;
        right: 20px;
        font-size: 80px;
        cursor: pointer;
        color: #e8e8e8;
        transition: 0.3s;

        &:hover {
          color: #409eff;
        }
      }

      .iframeWindowInner {
        width: 100%;
        height: 100%;
        border-width: 0;
      }

      .iframeWindowInnerT {
        width: 100%;
        height: 100%;
        border-width: 0;
        background: url('~@/assets/application/iframe.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
}
</style>