<template>
  <div>
    <div class="flow_chart" id="flowChart"></div>
  </div>
</template>

<script>
export default {
  name: 'flowChart',
  data() {
    return {
      charts2Data: [
        {
          name: '24:00',
          value1: 110,
          value2: 90,
        },
        {
          name: '03:00',
          value1: 180,
          value2: 200,
        },
        {
          name: '06:00',
          value1: 400,
          value2: 200,
        },
        {
          name: '09:00',
          value1: 300,
          value2: 400,
        },
        {
          name: '12:00',
          value1: 200,
          value2: 190,
        },
        {
          name: '15:00',
          value1: 80,
          value2: 110,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById('flowChart'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          // icon: "circle",
          icon: 'rect', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 21, // 设置宽度
          itemHeight: 21, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
          left: '40%',
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.charts2Data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: true,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：万辆',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '昨日',
            type: 'line',
            barWidth: 30,
            smooth: true,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: '#00C0FE',
              },
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 1)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, .6)', // 100% 处的颜色
                  },
                ],
                global: false,
              },
            },
            data: this.charts2Data.map((item) => item.value1),
          },
          {
            name: '今日',
            type: 'line',
            barWidth: 30,
            smooth: true,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: '#FCDF1D',
              },
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 212, 97, 1)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 212, 97, .6)', // 100% 处的颜色
                  },
                ],
                global: false,
              },
            },
            data: this.charts2Data.map((item) => item.value2),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.flow_chart {
  width: 938px;
  height: 600px;
  margin-top: 26px;
}
</style>
