<template>
  <div class="container">
    <div class="table">
      <div class="table-row title">
        <div class="table-cell title-cell">任务名称</div>
        <div class="table-cell title-cell">任务类型</div>
        <div class="table-cell title-cell">任务进度</div>
        <div class="table-cell title-cell">严重程度</div>
        <div class="table-cell title-cell">通知/督办</div>
      </div>
      <div class="table-container">
        <div class="table-row" v-for="(item, i) in tableData" :key="i">
          <div class="table-cell cursor" @click="showDbdialog(item)">{{ item.name }}</div>
          <div class="table-cell">{{ item.type }}</div>
          <div class="table-cell cursor" @click="showDetailDialog(item)">

            <div v-for="(obj,i) in statusList" :key="i" class="cell-container" v-show="item.status == obj.label">
              <img :src="obj.img" alt="" />
              <div style="margin-left: 10px">{{ obj.label }}</div>
            </div>

          </div>
          <div class="table-cell">{{ item.level }}</div>
          <div class="table-cell">
            <div class="cell-container">
              <div class="Notice" @click="item.isNotice = !item.isNotice">
                <img src="@/assets/Command/noticeA.png" alt="" class="cell-icon" v-if="item.isNotice" />
                <img src="@/assets/Command/notice.png" alt="" class="cell-icon" v-else />
                <div style="margin-left: 10px" :class="{ active: item.isNotice }" >通知</div>
              </div>
              <div class="Supervision" style="margin-left: 25px" @click="item.isSupervision = !item.isSupervision">
                <img src="@/assets/Command/supervisionA.png" alt="" class="cell-icon" v-if="item.isSupervision" />
                <img src="@/assets/Command/supervision.png" alt="" class="cell-icon" v-else />
                <div style="margin-left: 10px" :class="{ active: item.isSupervision }" >督办</div>
              </div>
            </div>
            <!--            <el-progress :percentage="item.progress" style="width: 269px;white-space: nowrap"></el-progress>-->
          </div>
        </div>
      </div>
    </div>
    <!-- 督办详情弹框 -->
    <Commondialog :dialog-flag="showFlag" title="执行进度" :dialogWidth="'560px'" @close="showFlag = false">
      <div class="db_box">
        <div class="img_box">
          <img class="point" src="@/assets/Command/db_point.png" alt="" />
          <img class="line" src="@/assets/Command/db_line.png" alt="" />
        </div>
        <div class="db_content">
          <div class="db_item">
            <div class="title blue_linear1">任务下发</div>
            <div class="content">已将事件推送至基层治理系统</div>
            <div class="time">{{ itemObj.time }}</div>
          </div>
        </div>
      </div>
    </Commondialog>

    <Commondialog title="任务详情" :dialogFlag="showDialog" @close="showDialog = false" dialogWidth="1000px">
      <gzdbDetail @close="showDialog = false" :id="id" :status="progress"  v-if="showDialog" @updateList="getList"/>
    </Commondialog>
  </div>
</template>

<script>
import Commondialog from "@/components/Commondialog";
import {getTaskList} from "@/api/EventTask";
import gzdbDetail from "@/pages/CommandAndDispatch/components/RightComponents/gzdbChild/gzdbDetail";
export default {
  name: 'gzdbTable',
  data() {
    return {
      tableData: [
        {
          name: '交通流量监控',
          type: '城市治理',
          level: '特急',
          progress: 50,
          status: '1',
          isNotice: false,
          isSupervision: true,
          time:'2024-05-13 10:40:22'
        },
        {
          name: '公民道德教育系列讲座',
          type: '文化建设',
          level: '紧急',
          progress: 25,
          status: '2',
          isNotice: false,
          isSupervision: true,
          time:'2024-05-13 08:22:22'
        },
        {
          name: '公共安全视频监控',
          type: '城市治理',
          level: '紧急',
          progress: 75,
          status: '3',
          isNotice: false,
          isSupervision: true,
          time:'2024-05-11 11:40:22'
        },
        {
          name: '垃圾收集与处理调度',
          type: '环境保护',
          level: '平急',
          progress: 0,
          status: '4',
          isNotice: false,
          isSupervision: true,
          time:'2024-05-08 15:50:12'
        },
        {
          name: '社区事件响应协调',
          type: '社区治理',
          level: '其他',
          progress: 0,
          status: '4',
          isNotice: false,
          isSupervision: true,
          time:'2024-05-05 18:11:12'
        },
        {
          name: '路灯故障报修处理',
          type: '基础设施',
          level: '紧急',
          progress: 25,
          status: '2',
          isNotice: false,
          isSupervision: true,
          time:'2024-04-22 10:40:22'
        }
      ],
      //督办详情弹框
      showFlag: false,
      itemObj: {},
      showDialog:false,
      id:"",
      progress:"",
      statusList: [
        {
          key: '1',
          label:"待分拨",
          img:require("@/assets/Command/red.png")
        },
        {
          key: '2',
          label:"待认领",
          img:require("@/assets/Command/blue.png")
        },
        {
          key: '3',
          label:"待处理",
          img:require("@/assets/Command/yellow.png")
        },
        {
          key: '4',
          label:"已完成",
          img:require("@/assets/Command/green.png")
        }
      ]
    }
  },
  components: {
    Commondialog,
    gzdbDetail
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getTaskList().then(res => {
        this.tableData = res.data.data.list.map(item => ({
          id: item.id,
          name: item.name,
          type: item.type,
          level: item.urgency,
          status: item.taskPhase,
          isNotice: false,
          isSupervision: true,
          time:'2024-05-13 10:40:22'
        }))
      })
    },
    showDbdialog(item) {
      this.itemObj = item
      this.showFlag = true
    },
    //详情弹窗
    showDetailDialog(item) {
      this.id = String(item.id);
      this.progress = item.status;
      this.showDialog = true;
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  .icon {
    width: 31px;
    height: 41px;
  }
  /deep/ .el-progress__text {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 30px !important;
    color: #ffffff;
    background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 16px;
  }
  /deep/ .el-progress-bar__outer {
    background: #223c50 !important;
  }
  /deep/ .el-progress-bar__inner {
    background: linear-gradient(270deg, #cbf2ff, #85e6ff, #00c0ff) !important;
  }
  .table {
    width: 1888px;
    height: 493px;
    .table-row {
      width: 100%;
      height: 62px;
      background: linear-gradient(0deg, rgba(21, 48, 64, 0.4) 0%, rgba(53, 117, 186, 0.4) 100%);
      border-radius: 8px;
      border-image: linear-gradient(0deg, #5389c0, #87c2ff) 10 10;
      margin-top: 10px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .table-cell {
        flex: 1;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #d6e7f9;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        .cell-container {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .Notice,
          .Supervision {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            cursor: pointer;
            .cell-icon {
              width: 28px;
              height: 32px;
            }
            .active {
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 32px;
              color: #d6e7f9;
              background: linear-gradient(180deg, #ff4949 0%, #ffcdcd 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
      .title-cell {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #77b3f1;
      }
    }
    .title {
      background: linear-gradient(0deg, rgba(21, 48, 70, 0.7) 0%, rgba(53, 117, 186, 0.7) 200%) !important;
    }
    .table-container {
      height: 420px;
      overflow-y: scroll;
    }
  }
}
.db_box {
  padding: 26px 26px 106px 26px;
  display: flex;
  height: 1500px;
  overflow-y: scroll;
  .img_box {
    display: flex;
    flex-direction: column;
    .point {
      width: 25px;
      height: 25px;
    }
    .line {
      width: 2px;
      height: 171px;
      margin-left: 11px;
    }
  }
  .db_content {
    flex: 1;
    margin-left: 36px;
    .db_item {
      .title {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 700;
        font-size: 32px;
        line-height: 44px;
      }
      .content {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        line-height: 36px;
        margin-top: 12px;
      }
      .time {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        line-height: 36px;
        margin-top: 12px;
      }
    }
  }
}
</style>
