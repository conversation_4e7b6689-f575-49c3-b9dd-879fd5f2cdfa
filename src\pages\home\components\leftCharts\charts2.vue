<template>
  <div>
    <div class="eventTabs">
      <div v-for="(item,i) in ['事件来源','事件类型']" :key="i" class="tabItem" :class="{active:eventChoose == i}" @click="typeChange(i)">{{item}}</div>
    </div>
    <div class="charts2" id="charts2"></div>
  </div>
</template>

<script>
import imgUrl from "@/assets/common/piebg.png";
import {getEventZc} from "@/api/EventTask";
export default {
  name: "charts2",
  data() {
    return {
      charts2Data:[
        {name:"网格采集",value:810876},
        {name:"智慧金开",value:1213},
        {name:"警情事件",value:2081},
        {name:"派出所综合指挥室",value:14},
      ],
      eventChoose:0, //时间组成默认选中
    }
  },
  computed: {},
  mounted() {
    this.initCharts2()
  },
  methods: {
    initCharts2() {
      let that = this
      getEventZc({type: this.eventChoose == 0?"事件来源":"事件类型"}).then(res => {
        that.charts2Data = res.data.data.map(item => ({
          name: item.label,
          value: item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById("charts2"));
        let imgUrl = require('@/assets/common/piebg.png')
        let option = {
          color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

          tooltip: {
            trigger: 'item',
            // formatter: '{b}: <br/> {d}%',
            formatter: '{b}: <br/> {c}个<br/> {d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          legend: {
            orient: 'vertical',
            left: '48%',
            top: '20%',
            bottom: '0%',
            icon: 'circle',
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: '#ffffff',
                  padding: [0, 20, 0, 15]
                },
                value: {
                  fontSize: 25,
                  color: '#FFC460',
                  // padding: [10, 0, 0, 15]
                },
              }
            },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              that.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2)
              return '{name|' + name + '}{value|' + tarValue + '件}'
            },
          },
          graphic: [
            {
              type: "image",
              id: "logo",
              left: "10.8%",
              top: "10.4%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['25%', '45%'],
              roseType: '',
              itemStyle: {
                borderRadius: 0,
              },
              label: {
                show: false,
              },
              data: this.charts2Data,
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
    typeChange(i) {
      this.eventChoose = i
      this.initCharts2()
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.charts2 {
  width: 941px;
  height: 380px;
}
.eventTabs {
  z-index: 999;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 560px;
  position: relative;
  top: 50px;
  left: 400px;
  .tabItem {
    cursor: pointer;
    text-align: center;
    width: 256px;
    height: 71px;
    line-height: 71px;
    font-size: 32px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #D6E7F9;
    background: url("@/assets/home/<USER>") no-repeat;
    background-size: cover;
  }
  .active {
    background: url("@/assets/home/<USER>") no-repeat;
  }
}
</style>