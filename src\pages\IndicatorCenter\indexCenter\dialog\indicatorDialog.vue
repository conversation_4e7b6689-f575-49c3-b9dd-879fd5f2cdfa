<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="indicator-container">
      <!-- 左卷轴 -->
      <div class="l-pic-index" style="z-index: 6"></div>
      <!-- 左阴影 -->
      <div class="l-pic-shadow"></div>
      <!-- 右卷轴 -->
      <div class="r-pic-index" style="z-index: 6"></div>
      <!-- 右阴影 -->
      <div class="r-pic-shadow"></div>
      <div class="l-bg-index"></div>
      <div class="r-bg-index"></div>
      <div class="main-index">
        <div class="title-dialog">
          <span class="blue-color">{{ listName }}</span>
          <i class="fanhui-icon" @click="close"></i>
        </div>
        <!-- 左右卷轴 -->
        <img
          class="middle-lb-left"
          src="@/pages/IndicatorCenter/indexCenter/dialog/img/swiper-left.png"
          alt=""
          @click="pre()"
        />
        <img
          class="middle-lb-right"
          src="@/pages/IndicatorCenter/indexCenter/dialog/img/swiper-right.png"
          alt=""
          @click="next()"
        />
        <div class="content-box">
          <div class="move-box">
            <div class="zbzx-dialog-left">
              <div class="zbzx-dialog-box" v-for="(item, index) in list" :key="index">
                <div class="part">
                  <div class="top-con">
                    <div>
                      <div class="con-title">
                        <span class="con-title-icon"></span>
                        <span>{{ item.name }}</span>
                      </div>
                      <div class="con-list">
                        <div
                          class="con-list-item"
                          @click="choseFourFun(el)"
                          v-for="(el, index1) in item.children"
                          :key="index1"
                        >
                          <div style="display: flex; justify-content: space-between; width: 90%">
                            <div class="name-div" style="display: flex; align-items: center">
                              <span>{{ el.indicatorName || '-' }}</span>
                            </div>
                            <div style="position: relative">
                              <span class="blue-color">
                                {{ el.indicatorValue || '-' }}{{ el.indicatorUnit || '-' }}
                              </span>
                              <span class="con-icon"></span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗 -->
      <div v-if="visible1">
        <indicatorDetail :visible="visible1" :id="detailid" @close="visible1 = false"></indicatorDetail>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import indicatorDetail from './indicatorDetail.vue'
import { getIndicatorData } from '@/api/IndicatorCenter/indicatorDialog.js'
import { getJscjhyxzs } from '@/api/IndicatorCenter'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
  },
  components: { indicatorDetail },
  data() {
    return {
      listId: '',
      winTop: '40px',
      listName: '',
      index: -1,
      list: [],
      orderList: [
        // 产业经济8，城市治理9，公共服务10，生态宜居11，其他38
      ],
      //
      detailid: null,
      visible1: false,
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    // $(document).ready(function () {
    //   // 左右卷轴
    //   $('.l-pic-index').animate({ left: '-2000px' }, 1450)
    //   $('.r-pic-index').animate({ right: '-1100px' }, 1450)
    //   // // 左右阴影
    //   $('.l-pic-shadow').animate({ left: '-1830px' }, 1450)
    //   $('.r-pic-shadow').animate({ right: '-930px' }, 1450)
    //   $('.l-bg-index').animate({ width: '3685px', left: '-2000px' }, 1500)
    //   $('.r-bg-index').animate({ width: '3685px', right: '-1100px' }, 1500, function () {
    // setTimeout(() => {
    //   $('.main-index').fadeIn(800)
    // }, 300)
    //   })
    // })

    this.listName = this.name
    this.listId = this.id
    this.initFun()
    getJscjhyxzs().then((res) => {
      this.orderList = res.data.data
      this.orderList.forEach((item, i) => {
        if (item.name == this.listName) {
          this.index = i
        }
      })
    })
  },
  methods: {
    initFun() {
      let that = this
      that.list = []
      getIndicatorData({ categoryId: this.listId }).then((res) => {
        this.list = this.handleList(res.data.data) //分类处理
      })
    },
    // 四级指标点击
    choseFourFun(item) {
      this.detailid = item.indicatorId
      this.visible1 = true
    },
    pre() {
      if (this.index <= 0) {
        return
      } else {
        if (this.index == 1) $('.middle-lb-left').addClass('cursor')
        this.index--
        this.listName = this.orderList[this.index].name
        this.listId = this.orderList[this.index].id
        this.initFun()
        $('.middle-lb-right').removeClass('cursor')
      }
    },
    next() {
      if (this.index == 4) {
        return
      } else {
        if (this.index == 3) $('.middle-lb-right').addClass('cursor')
        this.index++
        this.listName = this.orderList[this.index].name
        this.listId = this.orderList[this.index].id
        this.initFun()
        $('.middle-lb-left').removeClass('cursor')
      }
    },
    handleList(list) {
      let arr = []
      list.forEach((item) => {
        if (arr.length == 0 || arr.findIndex((x) => x.name == item.categoryName) == -1) {
          let obj = {
            name: item.categoryName,
            children: [item],
          }
          arr.push(obj)
        } else {
          let index = arr.findIndex((x) => x.name == item.categoryName)
          arr[index].children.push(item)
        }
      })
      return arr
    },
    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
@import '@/pages/IndicatorCenter/indexCenter/dialog/css/zbzx-dialog.css';
// 重置element-ui弹框
/deep/.el-dialog {
  // width: 3000px;
  // height: 1800px;
  // background: rgba(0, 15, 55, 0.9);
  background: transparent;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  margin-left: 2350px !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.title-container .close-btn {
  float: right;
  margin: 15px;
  cursor: pointer;
  font-size: 36px;
  color: white;
}
</style>
@/api/IndicatorCenter/indicatorDialog.js