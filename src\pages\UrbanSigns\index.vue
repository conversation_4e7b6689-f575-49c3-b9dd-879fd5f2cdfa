<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <UrbanSignsLeft class="animate__animated animate__fadeInLeft" @showDialog="showDialogFunc"/>
      </wrapbox>
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <UrbanSignsCenter @showDialog="showDialogFunc"/>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <UrbanSignsRight class="animate__animated animate__fadeInRight" @showDialog="showDialogFunc"/>
      </wrapbox>
    </div>


    <!--详情弹窗-->
    <Commondialog :title="dialogTitle" :dialogFlag="showDialog" @close="showDialog = false" dialogWidth="2300px">
      <zhibiaoDialog :detailData="itemObj" :keyDesc="keyList" :width="2200" v-if="showDialog"></zhibiaoDialog>
    </Commondialog>
  </div>
</template>

<script>
import Commondialog from "@/components/Commondialog";
import zhibiaoDialog from "@/pages/UrbanSigns/ZhibiaoDialog";
import wrapbox from '@/components/wrapbox'
import UrbanSignsLeft from "@/pages/UrbanSigns/components/UrbanSignsLeft";
import UrbanSignsRight from "@/pages/UrbanSigns/components/UrbanSignsRight";
import UrbanSignsCenter from "@/pages/UrbanSigns/components/UrbanSignsCenter";
export default {
  name: 'index',
  data() {
    return {
      itemObj: {},
      dialogTitle:"指标详情",
      showDialog:false,
      keyList: [
        {
          label: '指标定义',
          key: 'zbdy'
        },
        {
          label: '责任部门',
          key: 'zrbl'
        },
        {
          label: '所属系统',
          key: 'ssxt'
        },
        {
          label: '更新频率',
          key: 'gxpl'
        },
        {
          label: '阈值标准',
          key: 'yzbz'
        },
        {
          label: '数据更新时间',
          key: 'sjgxsj'
        }
      ],
      dataList: [
        {
          name:"生态宜居",
          number:"96",
          zbdy:"衡量城市生态环境质量、居住条件及居民生活满意度综合水平的指标。高生态宜居指数意味着拥有良好的生态环境和宜居条件，能够满足居民对高品质生活的需求。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "96"
            },
            {
              "name": "2024.02",
              "value": "97"
            },
            {
              "name": "2024.03",
              "value": "95"
            },
            {
              "name": "2024.04",
              "value": "97"
            },
            {
              "name": "2024.05",
              "value": "93"
            },
            {
              "name": "2024.06",
              "value": "98"
            },
            {
              "name": "2024.07",
              "value": "96"
            }
          ]
        },
        {
          name:"健康舒适",
          number:"91",
          zbdy:"健康舒适指数主要关注居民身体健康状况和日常生活环境的舒适度。高健康舒适指数表明居民能够享受到优质的医疗服务和健康的生活环境，从而提升整体生活质量。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "98"
            },
            {
              "name": "2024.02",
              "value": "93"
            },
            {
              "name": "2024.03",
              "value": "92"
            },
            {
              "name": "2024.04",
              "value": "92"
            },
            {
              "name": "2024.05",
              "value": "97"
            },
            {
              "name": "2024.06",
              "value": "95"
            },
            {
              "name": "2024.07",
              "value": "91"
            }
          ]
        },
        {
          name:"安全韧性",
          number:"95",
          zbdy:"安全韧性指数是衡量一个地区或城市在面对自然灾害、社会危机等突发事件时，保障居民安全、减少损失并迅速恢复正常秩序的能力的指标。高安全韧性指数意味着具备较强的风险抵御和快速恢复能力，能够为居民提供安全稳定的生活环境。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "97"
            },
            {
              "name": "2024.02",
              "value": "94"
            },
            {
              "name": "2024.03",
              "value": "98"
            },
            {
              "name": "2024.04",
              "value": "98"
            },
            {
              "name": "2024.05",
              "value": "98"
            },
            {
              "name": "2024.06",
              "value": "95"
            },
            {
              "name": "2024.07",
              "value": "95"
            }
          ]
        },
        {
          name:"交通便捷",
          number:"92",
          zbdy:"交通便捷指数是评价一个地区或城市交通网络完善程度、交通工具多样性、出行效率及便捷性的指标。高交通便捷指数说明该地区交通网络发达，出行方式多样且高效，极大地方便了居民的日常生活和工作。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "94"
            },
            {
              "name": "2024.02",
              "value": "96"
            },
            {
              "name": "2024.03",
              "value": "92"
            },
            {
              "name": "2024.04",
              "value": "93"
            },
            {
              "name": "2024.05",
              "value": "96"
            },
            {
              "name": "2024.06",
              "value": "97"
            },
            {
              "name": "2024.07",
              "value": "92"
            }
          ]
        },
        {
          name:"整洁有序",
          number:"95",
          zbdy:"整洁有序指数是衡量一个地区或城市环境卫生状况、市容市貌整洁度及城市管理水平的指标。高整洁有序指数表明该地区环境整洁、秩序井然，体现了良好的城市管理和居民文明素质。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "98"
            },
            {
              "name": "2024.02",
              "value": "95"
            },
            {
              "name": "2024.03",
              "value": "97"
            },
            {
              "name": "2024.04",
              "value": "94"
            },
            {
              "name": "2024.05",
              "value": "97"
            },
            {
              "name": "2024.06",
              "value": "92"
            },
            {
              "name": "2024.07",
              "value": "95"
            }
          ]
        },
        {
          name:"创新活力",
          number:"94",
          zbdy:"创新活力指数是评估一个地区或城市在科技创新、经济发展、文化创意等方面的活力和潜力的指标。高创新活力指数意味着该地区具备强大的创新能力和持续发展的动力，是推动经济社会高质量发展的关键因素。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "97"
            },
            {
              "name": "2024.02",
              "value": "94"
            },
            {
              "name": "2024.03",
              "value": "94"
            },
            {
              "name": "2024.04",
              "value": "92"
            },
            {
              "name": "2024.05",
              "value": "96"
            },
            {
              "name": "2024.06",
              "value": "97"
            },
            {
              "name": "2024.07",
              "value": "94"
            }
          ]
        },
        {
          name:"风貌特征",
          number:"93",
          zbdy:"风貌特征指数是衡量自然风貌特色及城市建设风格独特性的综合指标。高风貌特征指数表明该地区在保持历史文化底蕴的同时，注重城市建设的独特性和美学价值，为居民和游客提供了丰富多样的文化体验和视觉享受。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "95"
            },
            {
              "name": "2024.02",
              "value": "94"
            },
            {
              "name": "2024.03",
              "value": "96"
            },
            {
              "name": "2024.04",
              "value": "95"
            },
            {
              "name": "2024.05",
              "value": "93"
            },
            {
              "name": "2024.06",
              "value": "96"
            },
            {
              "name": "2024.07",
              "value": "93"
            }
          ]
        },
        {
          name:"多元包容",
          number:"93",
          zbdy:"多元包容指数评估的是地区在促进文化多样性、社会融合和包容性方面的表现。高多元包容指数表明该地区尊重并促进不同群体的和谐共处，营造了开放包容的社会氛围。",
          zrbl:"",
          ssxt:"金华经济技术开发区一体化智能化公共数据平台",
          gxpl:"每月一次",
          yzbz:"",
          sjgxsj:"2024.08.09",
          chartList: [
            {
              "name": "2024.01",
              "value": "95"
            },
            {
              "name": "2024.02",
              "value": "94"
            },
            {
              "name": "2024.03",
              "value": "96"
            },
            {
              "name": "2024.04",
              "value": "98"
            },
            {
              "name": "2024.05",
              "value": "93"
            },
            {
              "name": "2024.06",
              "value": "95"
            },
            {
              "name": "2024.07",
              "value": "93"
            }
          ]
        }
      ]
    }
  },
  components: {
    wrapbox,
    UrbanSignsLeft,
    UrbanSignsRight,
    UrbanSignsCenter,
    Commondialog,
    zhibiaoDialog
  },
  computed: {},
  mounted() {

  },
  methods: {
    showDialogFunc(str) {
      this.itemObj = this.dataList.filter(item => item.name == str)[0]
      this.showDialog = true
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: url('~@/assets/application/dtBg.png') no-repeat center center;
  background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .spsbBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .midBottom {
    position: absolute;
    left: 2135px;
    bottom: 0;
    z-index: 2;
  }
}
</style>
