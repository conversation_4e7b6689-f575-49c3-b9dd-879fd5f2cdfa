<template>
  <div class="yyglContainer">
    <Ktitle :titleText="'应用概览'" width="937"></Ktitle>
    <div class="pngWrap">
      <div class="pngInnerWrap">
        <div class="innerTop">
          <div class="boxWrap">
            <div class="numClass">{{apps[0].value}}</div>
            <div class="txtClass">{{apps[0].name}}</div>
          </div>
          <div class="boxWrap">
            <div class="numClass">{{apps[1].value}}</div>
            <div class="txtClass">{{apps[1].name}}</div>
          </div>
        </div>
        <div class="innerBot">
          <div class="boxWrap">
            <div class="numClass">{{apps[2].value}}</div>
            <div class="txtClass">{{apps[2].name}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
export default {
  components: {
    Ktitle
  },
  data() {
    return {
      apps:[
        {
          name:"应用总数",
          value:"31",
        },
        {
          name:"接入应用数",
          value:"10",
        },
        {
          name:"活跃应用数",
          value:"4",
        },
      ]
    }
  }
}
</script>

<style scoped lang="less">
.boxWrap {
  width: 292px;
  height: 340px;
  background: url('~@/assets/application/yygl.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .numClass {
    font-size: 42px;
    font-family: Bebas Neue;
    font-weight: bolder;
    color: #ffffff;

    background: linear-gradient(180deg, #ffffff 0%, #ffffff 50.244140625%, #ffeccb 53.0029296875%, #ffc460 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 52px;
  }
  .txtClass {
    font-size: 34px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    margin-top: 104px;
  }
}
.yyglContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .pngWrap {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 85px 32px 0 32px;
    .pngInnerWrap {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      // background-color: blue;
      .innerTop {
        display: flex;
        justify-content: space-between;
      }
      .innerBot {
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style>