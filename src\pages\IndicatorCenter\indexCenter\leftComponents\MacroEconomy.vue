<!--经济生态-宏观经济-->
<template>
  <div class="container">
    <pyramidCharts :id="'1'" :charts-data="chartsData"></pyramidCharts>
  </div>
</template>

<script>
import pyramidCharts from "@/components/pyramidCharts";
export default {
  name: "MacroEconomy",
  data() {
    return {
      chartsData:[
        {
          name:"第一产业",
          value:1759
        },
        {
          name:"第二产业",
          value:6574
        },
        {
          name:"第三产业",
          value:50438
        },
      ]
    }
  },
  components:{
    pyramidCharts
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
</style>