<template>
  <div id="bjlChart"></div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      let myChart = this.$echarts.init(document.getElementById("bjlChart"))

      var value = 60
      var color = new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: '#1DDAEB',
        },
        {
          offset: 1,
          color: '#1890FF',
        },
      ])
      let option = {
        series: [
          {
            type: 'gauge',
            splitNumber: 1,
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: 100,
            radius: '87%',
            center: ['50%', '75%'],
            axisLine: {
              show: false,
            },
            //刻度标签。
            axisTick: {
              show: false,
            },
            //刻度样式
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: '#fff',
              distance: -60,
              fontSize: 36
            },
            //指针
            pointer: {
              width: 24,
              length: '100%',
            },
            //指针样式
            itemStyle: {
              color: '#1890FF',
              borderWidth: 3,
              borderColor: '#1890FF',
              shadowColor: 'rgba(12,146,138,0.5)',
              shadowBlur: 6,
              shadowOffsetX: 0,
              shadowOffsetY: 5,
            },
            title: {
              //标题
              show: true,
              offsetCenter: [0, 140], // x, y，单位px
              textStyle: {
                color: '#fff',
                fontSize: 14, //表盘上的标题文字大小
                fontWeight: 400,
                lineHeight: 20,
              },
            },
            detail: {
              fontSize: 36,
              offsetCenter: [0, 90],
              valueAnimation: true,
              color: '#fff',
              fontFamily: 'Din',
              lineHeight: 50,
              formatter: function (value) {
                return '{titleOne|' + value + '%}' + '\n农业投诉举报办结率'
              },
              rich: {
                titleOne: {
                  color: '#FFC562',
                  fontSize: 36,
                  fontWeight: '500'
                }
              }
            },
            data: [
              {
                value: value,
                // name: '农业投诉举报办结率',
              },
            ],
          },
          // 外圈样式
          {
            type: 'gauge',
            radius: '100%',
            center: ['50%', '65%'],
            startAngle: 180,
            endAngle: 0,
            min: 0,
            max: 8000,
            title: {
              show: false,
            },
            detail: {
              show: false,
            },
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 26,
                color: [
                  [value / 100, color],
                  [1, 'rgba(225,225,225,0.4)'],
                ],
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            pointer: {
              show: false,
            },
            itemStyle: {
              normal: {
                color: '#54F200',
              },
            },
          },
          // 内圈样式
          {
            type: 'gauge',
            splitNumber: 10,
            radius: '40%',
            center: ['50%', '65%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            pointer: {
              show: false,
              width: 7,
              length: '60%',
            },
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 8,
                color: [[1, '#999']],
                opacity: 0.1,
              },
            },
            axisLabel: {
              show: false,
            }, //刻度标签。
            axisTick: {
              show: false,
            }, //刻度样式
            splitLine: {
              show: false,
            }, //分隔线样式
            detail: {
              show: false,
            },
            title: {
              show: false,
            },
          },
        ],
      }


      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
#bjlChart {
  width: 100%;
  height: 100%;
}
</style>