<template>
  <div class="centerContainer">
    <Ktitle :titleText="'产业运行'" :width="'3210'" />
    <div class="mainContainer">
      <div class="cyyxWrap">
        <!-- <TitleTwo :title-text="'产业运行'" :width="'3210'" style="margin-top: 22px;" /> -->
        <div class="cyyxClass">
          <div class="cyyxClass_l">
            <div class="cyyxClass_l_j">
              <CyyxMacroEconomy :id="'1'" :charts-data="chartsData" />
            </div>
            <div class="cyyxClass_l_f">
              <CountCard ref="countCard" />
            </div>
          </div>
          <div class="cyyxClass_l">
            <div class="cyyxClass_l_f">
              <CountCardSecond ref="countCardSecond" />
            </div>
            <div class="cyyxClass_l_b">
              <CyyxPie3D id="cyyxEcharts" :options="cyyxChartsData" :legendCustom="legend" :internalDiameterRatio="0"
                :scaleCustom="2" :zHeight="0.5"></CyyxPie3D>
            </div>
          </div>
        </div>
      </div>
      <div class="centerMid">
        <div class="gdzctzClass">
          <!-- <TitleTwo :title-text="'固定资产投资'" :width="'1605'" /> -->
          <Ktitle :titleText="'固定资产投资'" :width="'1605'" />
          <div class="innerTop">
            <div v-for="(item, i) in ['固定资产投资增速', '民间项目投资增速', '制造业投资增速']" :key="i" class="tabItem"
              :class="{ 'active': eventChoose == i }" @click="changeCharts(i)">{{ item }}</div>
          </div>
          <div class="gdzctzWrap">
            <GdzctzChart v-if="eventChoose == 0" ref="gdzctzChart" />
            <MjxmtzChart v-else-if="eventChoose == 1" ref="mjxmtzChart" />
            <ZzytzChart v-else="eventChoose == 2" ref="zzytzChart" />
          </div>
        </div>
        <div class="qyfzClass">
          <!-- <TitleTwo :title-text="'企业发展'" :width="'1605'" /> -->
          <Ktitle :titleText="'企业发展'" :width="'1605'" />

          <div class="qyfz_inner">
            <div class="qyfz_inner_l">
              <div class="tabItem" @click="changeQyfz(i)" v-for="(item, i) in ['零售额', '利税总额']" :key="i"
                :class="{ 'active': qyfzChoose == i }">
                <div class="tabItemInner">{{ item }}</div>
              </div>
            </div>
            <div class="qyfz_inner_r">
              <QyfzChart ref="qyfzChart" />
            </div>
          </div>
        </div>
      </div>
      <div class="yxjcWrap">
        <Ktitle :titleText="'运行监测'" :width="'3210'" />
        <div class="yxjcBot">
          <div class="yxjcBot_l">
            <div class="boxWrap">
              <div class="numClass">11896户</div>
              <div class="txtClass">
                <div>本市新增</div>
                <div>市场主体数</div>
              </div>
            </div>
            <div class="boxWrap">
              <div class="numClass">56857人</div>
              <div class="txtClass">
                <div>本月新登记劳动</div>
                <div>年龄流动人口数</div>
              </div>
            </div>
          </div>
          <div class="yxjcBot_r">
            <YxjcChart />
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
import TitleTwo from "@/components/titleTwo.vue"
import GdzctzChart from "@/pages/EconomicalEcology/components/chartsComponents/GdzctzChart.vue"
import MjxmtzChart from "@/pages/EconomicalEcology/components/chartsComponents/MjxmtzChart.vue"
import ZzytzChart from "@/pages/EconomicalEcology/components/chartsComponents/ZzytzChart.vue"
import QyfzChart from "@/pages/EconomicalEcology/components/chartsComponents/QyfzChart.vue"
import CyyxMacroEconomy from "@/pages/EconomicalEcology/components/chartsComponents/CyyxMacroEconomy.vue"
import CyyxPie3D from "@/pages/EconomicalEcology/components/chartsComponents/CyyxPie3D.vue"
import YxjcChart from "@/pages/EconomicalEcology/components/chartsComponents/YxjcChart.vue"
import CountCard from "@/pages/EconomicalEcology/components/centerComponents/CountCard.vue"
import CountCardSecond from "@/pages/EconomicalEcology/components/centerComponents/CountCardSecond.vue"

export default {
  components: {
    Ktitle,
    TitleTwo,
    GdzctzChart,
    QyfzChart,
    CyyxMacroEconomy,
    CyyxPie3D,
    CountCard,
    CountCardSecond,
    YxjcChart,
    MjxmtzChart,
    ZzytzChart
  },
  data () {
    return {
      eventChoose: 0,
      qyfzChoose: 0,
      chartsData: [
        {
          name: "第一产业",
          value: 2000
        },
        {
          name: "第二产业",
          value: 4000
        },
        {
          name: "第三产业",
          value: 6000
        },
      ],
      cyyxChartsData: [
        {
          value: 759,
          name: "第一产业"
        },
        {
          value: 574,
          name: "第二产业"
        },
        {
          value: 538,
          name: "第三产业"
        }
      ],
      legend: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 30,
        left: '65%',
        top: '35%',
      }
    }
  },
  methods: {
    changeCharts (index) {
      this.eventChoose = index
    },
    changeQyfz (index) {
      this.qyfzChoose = index
      if (index == 0) {
        let option = this.$refs.qyfzChart.myChart.getOption()
        option.yAxis[0].data = ['汽车类','石油及制品类','粮油制品类','家用电器和影像器材','服装、鞋帽、针纺织品类']
        option.series[0].data = [28.6, 10.1, 5.9, 2.9, 2.7]
        this.$refs.qyfzChart.myChart.setOption(option, true)
      } else {
        let option = this.$refs.qyfzChart.myChart.getOption()
        option.yAxis[0].data = ['医疗制造业','金属制品业','专业设备制造业','食品制造业','化学原料及化学制品制造业']
        option.series[0].data = [3.70, 3.01, 1.99, 1.57, 1.29]
        this.$refs.qyfzChart.myChart.setOption(option, true)

      }
    },
  }
}
</script>

<style scoped lang="less">
.centerContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;

  .mainContainer {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr 1fr;

    .cyyxWrap {
      display: flex;
      flex-direction: column;
      align-items: center;

      .cyyxClass {
        flex: 1;
        width: 100%;
        // background-color: #FFC460;
        display: flex;

        .cyyxClass_l {
          width: 50%;
          height: 100%;
          box-sizing: border-box;
          padding: 0 16px;
          display: flex;

          .cyyxClass_l_j {
            width: 50%;
            height: 100%;
            // background-color: #FFC460;
          }

          .cyyxClass_l_f {
            width: 50%;
            height: 100%;
          }

          .cyyxClass_l_b {
            width: 50%;
            height: 100%;
            // background-color: #FFC460;
          }
        }
      }
    }

    .centerMid {
      width: 100%;
      height: 100%;
      display: flex;

      .gdzctzClass {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .innerTop {
          width: 100%;
          display: flex;
          justify-content: space-around;
          align-items: center;
          margin-top: 26px;
          margin-bottom: 26px;

          .tabItem {
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 400px;
            height: 70px;
            font-size: 32px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #fff;
            background: url('~@/assets/home/<USER>') no-repeat center center;
            background-size: 100% 100%;
          }

          .active {
            background: url('@/assets/home/<USER>') no-repeat;
            background-size: 100% 100%;
          }
        }

        .gdzctzWrap {
          flex: 1;
          width: 100%;
        }
      }

      .qyfzClass {
        width: 50%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .qyfz_inner {
          flex: 1;
          width: 100%;
          height: 100%;
          display: flex;
          box-sizing: border-box;
          padding: 0 48px;
          align-items: center;

          .qyfz_inner_l {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            margin-right: 70px;

            .tabItem {
              cursor: pointer;
              display: flex;
              justify-content: center;
              align-items: center;
              width: 85px;
              height: 242px;
              font-size: 38px;
              font-family: Source Han Sans SC;
              font-weight: 400;
              color: #fff;
              background: url('~@/assets/economical/qyfzO.png') no-repeat center center;
              background-size: 100% 100%;

              .tabItemInner {
                width: 38px;
              }
            }

            .active {
              font-weight: bolder;
              background: url('@/assets/economical/qyfzT.png') no-repeat;
              background-size: 100% 100%;
            }
          }

          .qyfz_inner_r {
            flex: 1;
            width: 100%;
            height: 100%;
            // background-color: #FFC460;
          }
        }
      }
    }

    .yxjcWrap {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .yxjcBot {
        flex: 1;
        width: 100%;
        // background-color: aqua;
        display: flex;
        align-items: center;

        .yxjcBot_l {
          width: 1004px;
          height: 458px;
          background: url('~@/assets/economical/jxThree.png') no-repeat center center;
          background-size: 100% 100%;
          display: flex;
          justify-content: space-around;
          // align-items: center;

          .boxWrap {
            width: 292px;
            height: 340px;
            background: url('~@/assets/economical/xfssBg.png') no-repeat center center;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 96px;

            .numClass {
              font-size: 60px;
              font-family: Bebas Neue;
              font-weight: bolder;
              color: #ffffff;


              background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-top: 36px;
            }

            .txtClass {
              font-size: 36px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #ffffff;
              margin-top: 104px;
              display: flex;
              flex-direction: column;
              align-items: center;
            }
          }
        }

        .yxjcBot_r {
          flex: 1;
          height: 100%;
        }
      }
    }
  }
}
</style>