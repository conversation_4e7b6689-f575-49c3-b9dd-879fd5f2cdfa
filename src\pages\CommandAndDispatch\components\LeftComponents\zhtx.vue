<template>
  <div class="container">
    <Ktitle width="939.6" :titleText="'指挥体系'" :show-date="false">
      <el-select
        v-model="zhtxQueryParams.headquartersType"
        placeholder="请选择事件类型"
        @change="zhtxQuery"
        clearable
        style="width: 312px"
      >
        <el-option v-for="item in headquartersTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </Ktitle>
    <div class="container-tab">
      <div class="tab" v-for="(item,i) in tabList" :key="i" :class="{activeTab:chooseIndex == i}" @click="tabChange(i)">{{item.label}}</div>
    </div>
    <div class="container-table">
      <div class="container-table-row title">
        <div class="container-table-row-cell" v-for="(item,i) in tableTitle" :key="i">{{item}}</div>
      </div>
      <div class="table-inner">
        <div class="container-table-row" v-for="(item,i) in zhtxTableData" :key="i">
          <div class="container-table-row-cell" > <div class="cell-inner" :title="item.zw">{{item.zw}}</div> </div>
          <div class="container-table-row-cell" > <div class="cell-inner" :title="item.bm">{{item.bm}}</div> </div>
          <div class="container-table-row-cell" ><div class="cell-inner">{{item.xm}}</div></div>
          <div class="container-table-row-cell" ><div class="cell-inner" :title="formatPhone(item.lxdh)">{{formatPhone(item.lxdh)}}</div></div>
          <div class="container-table-row-cell" >
            <!-- <div class="icon phone" @click="call(item.lxdh,'phone')"></div> -->
            <div class="icon message" @click="zlxd(item)"></div>
            <!-- <div class="icon video" @click="call(item.lxdh,'video')"></div> -->
<!--            <div class="icon detail"></div>-->
          </div>
        </div>
      </div>
    </div>
    <div class="container-people">
      <div class="zbry-title">值班人员</div>
      <div class="zbry-content">
        <div class="zbry-content-item" v-for="(item,i) in zbryList" :key="i">
          <div class="zbry-content-item-left">
            <div class="zbry-icon leadericon" v-show="i == 0"></div>
            <div class="zbry-icon commonicon" v-show="i != 0"></div>
            <div class="leader-text" v-show="i == 0">领导：{{item.name}}</div>
            <div class="common-text" v-show="i != 0">值班员：{{item.name}}</div>
          </div>
          <div class="zbry-content-item-right">
            <div class="phone-text">联系电话：{{item.phone}}</div>
            <!-- <div class="icon phone" @click="call(item.phone,'phone')"></div> -->
          </div>
        </div>
      </div>
    </div>

    <!--指令下达-->
    <Commondialog title="指令下达" :dialogFlag="showDialog" @close="showDialog = false" :dialog-width="'1030px'">
      <zlxd @close="showDialog = false" :id="id" v-if="showDialog"/>
    </Commondialog>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import Commondialog from "@/components/Commondialog";
import zlxd from "@/pages/CommandAndDispatch/components/LeftComponents/childComponents/zlxd";
import {getZhtxList} from "@/api/command";
export default {
  name: "zhtx",
  data() {
    return {
      showDialog: false,
      id:"",
      tableTitle: ["职务", "部门", "姓名", "联系电话", ""],
      tabList: [
        {
          label:"指挥部",
          value:"1"
        },
        {
          label:"现场指挥部",
          value:"2"
        }
      ],
      chooseIndex: 0,
      zhtxQueryParams: {
        type:"1",
        headquartersType:"1"
      },
      zhtxTableData: [],
      zbryList: [
        {
          name:"盛新忠",
          phone:"13923568516"
        },
        {
          name:"余文斌",
          phone:"15921589838"
        },
        {
          name:"徐凯",
          phone:"18825413169"
        }
      ],
      headquartersTypeList: [
        {
          value: "1",
          label: "防汛防台抗旱指挥部"
        },
        {
          value: "2",
          label: "地质灾害应急指挥部"
        },
        {
          value: "3",
          label: "防震和减灾救灾指挥部"
        },
        {
          value: "4",
          label: "森林防灭火指挥部"
        }
      ]
    }
  },
  components: {
    Ktitle,
    Commondialog,
    zlxd
  },
  computed: {},
  mounted() {
    this.zhtxQuery()
  },
  methods: {
    tabChange(i) {
      this.chooseIndex = i;
      this.zhtxQueryParams.type = this.tabList[this.chooseIndex].value;
      this.zhtxQuery()
    },
    //查询指挥体系列表
    zhtxQuery() {
      getZhtxList(this.zhtxQueryParams).then(res => {
        this.zhtxTableData = res.data.rows.map(item => ({
          id: item.id,
          zw: item.position,
          bm: item.department,
          xm: item.name,
          lxdh: item.phone
        }))
      })
    },
    //指令下达
    zlxd(item) {
      this.id = item.id;
      this.showDialog = true;
    },
    call(number,type) {
      this.$EventBus.$emit("call",{number,type})
    },
    //将电话号码中间五位改成*号并返回
    formatPhone(phone) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .icon {
      width: 32px;
      height: 32px;
      background-size: cover;
      cursor: pointer;
    }
    .phone {
      background: url("~@/assets/Command/电话.png") no-repeat;
    }
    .message {
      background: url("~@/assets/Command/信息.png") no-repeat;
    }
    .video {
      background: url("~@/assets/Command/视频.png") no-repeat;
    }
    .detail {
      width: 60px !important;
      height: 60px !important;
      background: url("~@/assets/Command/baogao.png") no-repeat;
      background-size: 100% 100% !important;
    }
    .container-tab {
      display: flex;
      justify-content: center;
      align-items: center;
      .tab {
        width: 471px;
        height: 73px;
        line-height: 65px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #FFFFFF;
        text-align: center;
        background: url("~@/assets/Command/tab1.png") no-repeat;
        background-size: cover;
        cursor: pointer;
      }
      .activeTab {
        background: url("~@/assets/Command/tab2.png") no-repeat;
      }
    }
    .container-table {
      width: 942px;
      height: 572px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .container-table-row {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-shrink: 0;
        width: 99%;
        height: 52px;
        background: linear-gradient(0deg, #153040 0%, #3575BA 200%);
        border-radius: 8px;
        border-image: linear-gradient(0deg, #5389C0, #87C2FF) 10 10;
        border: 1px solid #3575BA;
        margin-bottom: 8px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 28px;
        color: #FFFFFF;
        .container-table-row-cell {
          flex: 1;
          text-align: center;
          display: flex;
          justify-content: space-evenly;
          align-items: center;
          .cell-inner {
            width: 170px;
            overflow: hidden;
            white-space: nowrap;
            text-align: center;
            text-overflow: ellipsis;
          }
        }
      }
      .title {
        background: linear-gradient(0deg, #153040 0%, #3575BA 100%) !important;
        border-image: linear-gradient(0deg, #5389C0, #87C2FF) 10 10 !important;
        border: 1px solid #3575BA !important;
        border-radius: 8px !important;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 30px;
        color: #77B3F1;
      }
      .table-inner {
        width: 100%;
        height: 512px;
        overflow-y: scroll;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
      }
      ::-webkit-scrollbar {
        width: 0;
      }

    }
    .container-people {
      width: 940px;
      height: 235px;
      background: url("~@/assets/Command/zbryBg.png") no-repeat;
      background-size: cover;
      margin-top: 53px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      .zbry-title {
        font-family: Source Han Sans CN;
        font-weight: 800;
        font-size: 40px;
        color: #00FFFF;
        text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
        background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #FFC461 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        width: 100%;
        text-align: center;
      }
      .zbry-content {
        width: fit-content;
        height: 132px;
        margin-top: 10px;
        .zbry-content-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .zbry-content-item-left {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .zbry-icon {
              width: 37px;
              height: 41px;
              background-size: cover;
            }
            .leadericon {
              background: url("~@/assets/Command/领导.png") no-repeat;
            }
            .commonicon {
              background: url("~@/assets/Command/gerenzhongxin.png") no-repeat;
            }
            .leader-text {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 32px;
              color: #FFD800;
              line-height: 54px;
              background: linear-gradient(180deg, #FFFFFF 0%, #FFB637 50.244140625%, #FFFCF3 53.0029296875%, #FFD8A1 99.365234375%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .common-text {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 32px;
              color: #FFD800;
              line-height: 54px;
              background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
          .zbry-content-item-right {
            flex: 1;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 60px;
            .phone-text {
              white-space: nowrap;
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 32px;
              color: #FFFFFF;
              margin-right: 17px;
            }
          }
        }
      }
    }
  }
</style>