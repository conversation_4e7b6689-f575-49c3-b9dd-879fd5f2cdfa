<template>
  <div>
    <div class="tabs">
      <div class="tab" v-for="(item,i) in tabs" :key="i" :class="{activeTab:tabChoose == i}" @click="tabClick(i)">
        {{item}}
      </div>
    </div>
    <div class="warningRightCharts7" id="warningRightCharts7"></div>
  </div>
</template>

<script>
import {getAirList} from "@/api/EventWarning";

export default {
  name: "air",
  data() {
    return {
      tabs:['CO','PM2.5','PM10','SO2','NO2'],
      tabChoose:0,
      charts1Data:[
        {date:"00",value:1000},
        {date:"04",value:2200},
        {date:"06",value:3200},
        {date:"08",value:2100},
        {date:"10",value:4800},
        {date:"12",value:2300},
        {date:"14",value:1500},
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts1()
  },
  methods: {
    tabClick(i) {
      this.tabChoose = i
      this.initCharts1()
    },
    initCharts1() {
      const that = this;
      getAirList({type: this.tabs[this.tabChoose]}).then(res => {
        this.charts1Data = res.data.data.map(item => ({
          date: item.label,
          value:item.value
        }))
        let myChart = this.$echarts.init(document.getElementById("warningRightCharts7"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: "3%",
            right: "3%",
            top: "10%",
            bottom: "1%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts1Data.map(item => item.date),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              // name: "事件总量/件",
              type: "value",
              // nameTextStyle: {
              //   fontSize: 24,
              //   color: "#D6E7F9",
              //   padding: [10,-100,10,10],
              // },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: that.tabs[that.tabChoose],
              type: "line", // 直线ss
              smooth: true,
              symbolSize: 0,
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#00C0FE'
                  },
                  {
                    offset: 1,
                    color: '#506D8F'
                  }
                ])
              },
              data: this.charts1Data.map(item => item.value),
            }
          ],
        };
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .tabs {
    width: 1935.6px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 56px;
    .tab {
      cursor: pointer;
      text-align: center;
      font-size: 40px;
      font-family: Source Han Sans SC;
      font-weight: 400;
      color: #7F93A8;
      width: 186px;
      height: 55px;
    }
    .activeTab {
      color: #D6E7F9;
      border-bottom: 4px solid #B5DAFF;
    }
  }
  .warningRightCharts7 {
    width: 1935.6px;
    height: 460px;
    margin-top: 40px;
  }
</style>