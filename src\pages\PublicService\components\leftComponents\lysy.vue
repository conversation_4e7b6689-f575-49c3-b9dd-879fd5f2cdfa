<!--老有所养-->
<template>
  <div class="botContainer">
    <Ktitle titleText="老有所养" />
    <div class="botop">
      <div class="zzsClass tyClass">
        <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'养老服务组织数'" :num="'20'" :unit="'个'" />
      </div>
      <div class="jgsClass tyClass">
        <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'养老机构数'" :num="'209'" :unit="'个'" />

      </div>
      <div class="hdsClass tyClass">
        <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'开展老年活动数'" :num="'11'" :unit="'次'" />
      </div>
      <div class="mydClass tyClass">
        <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'智慧养老服务满意度'" :num="'96'" :unit="'%'" />
      </div>
    </div>
    <div class="botbot">
      <div class="botbot_l">
        <div class="zccsClass tyClass">
          <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'助餐次数'" :num="'114.7'" :unit="'万次'" />
        </div>
        <div class="fwcsClass tyClass">
          <ToText :lineOneType="3" :numType="3" :unitType="3" :text="'服务次数'" :num="'75'" :unit="'次'" />
        </div>
      </div>
      <div class="botbot_r">
        <LysyPie3D id="lysyEcharts" :options="lysyChartsData" :legendCustom="legend" :internalDiameterRatio="0"
                   :scaleCustom="2" :zHeight="0.5"></LysyPie3D>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
import ToText from "@/components/ToText.vue"
import LysyPie3D from "@/pages/PublicService/components/chartsComponents/LysyPie3D";
export default {
  name: "lysy",
  components: {
    ToText,
    LysyPie3D,
    Ktitle
  },
  data() {
    return {
      lysyChartsData: [
        {
          value: 5.55,
          name: "老龄总量"
        },
        {
          value: 0.78,
          name: "高龄老人"
        }
      ],
      legend: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 30,
        left: '65%',
        top: '35%',
      }
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .botContainer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;

    .tyClass {
      width: 434px;
      height: 181px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      padding: 15px 0 0 170px;
    }

  // padding: 0 0 100px 0;
  .botop {
    width: 100%;
    display: flex;
    justify-content: space-between;
    box-sizing: border-box;
    margin-top: 100px;
    padding: 0 64px 0 94px;


    .zzsClass {
      background: url('~@/assets/publicService/zzs.png') no-repeat center center;
      background-size: 100% 100%;
    }

    .jgsClass {
      background: url('~@/assets/publicService/jgs.png') no-repeat center center;
      background-size: 100% 100%;
    }

    .hdsClass {
      background: url('~@/assets/publicService/hds.png') no-repeat center center;
      background-size: 100% 100%;
    }

    .mydClass {
      background: url('~@/assets/publicService/myd.png') no-repeat center center;
      background-size: 100% 100%;
      padding-left: 140px;
    }
  }

  .botbot {
    flex: 1;
    width: 100%;
    display: flex;

    .botbot_l {
      width: 50%;
      height: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      // background-color: aqua;

      .zccsClass {
        background: url('~@/assets/publicService/cs.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 66px;
      }

      .fwcsClass {
        background: url('~@/assets/publicService/fwcs.png') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 20px;

      }

    }

    .botbot_r {
      width: 50%;
      height: 100%;

    }
  }
}
</style>