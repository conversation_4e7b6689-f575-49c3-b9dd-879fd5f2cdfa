<template>
  <div>
    <div id="numberChart1"></div>
    <div class="indexs">
      <div class="name">{{total.name}}</div>
      <div class="info">
        <div class="value blue">{{total.value}} <span class="unit">{{total.unit}}</span> </div>
        <div class="value blue">{{total.percent}} <span class="unit">%</span> </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "charts1",
  data() {
    return {
      total:{
        name: "区管干部总人数",
        value: 150,
        unit:"人",
        percent:9,
      },
      chartsData:[
        {
          name: "区管正职干部人数",
          value: 70
        },
        {
          name: "区管副职干部人数",
          value: 50
        },
        {
          name: "区管其他干部人数",
          value: 30
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let that = this
      let myChart = this.$echarts.init(document.getElementById("numberChart1"));
      let option = {
        color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '50%',
          top: '40%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 30,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15]
              },
              value: {
                fontSize: 25,
                color: '#FFC460',
                // padding: [10, 0, 0, 15]
              },
            }
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total;
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '}{value|' + tarValue + '人  ' + p + '%}'
          },
        },
        graphic: [
          {
            type: "image",
            id: "logo",
            left: "10.4%",
            top: "16.4%",
            z: -10,
            bounding: "raw",
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.8, 0.8], //缩放
            style: {
              image: require("@/assets/common/echarts-bg.png"),
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['26%', '43%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 5,
            },
            label: {
              show: false,
            },
            data: this.chartsData,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped lang="less">
  #numberChart1 {
    width: 882px;
    height: 505px;
    margin-top: 124px;
  }
  .indexs {
    position: absolute;
    top: 1360px;
    left: 3250px;
    .name {
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #FFFFFF;
      text-align: center;
      margin-top: 31px;
      white-space: nowrap;
    }
    .info {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 22px;
      .value {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
      }
      .unit {
        font-size: 35px;
      }
    }
  }
</style>