<!--各类型预警信号数量-->
<template>
  <div class="nums">
    <titleTwo :titleText="'各类型预警信号数量'" :width="'1935.6'"></titleTwo>
    <div class="countLine">
      <div class="counts">
        <div class="countText">
          <span class="text">物联设备数量</span>
          <span class="number" v-for="(item,i) in nums.toString()" :key="i">
            <span class="numberText">{{ item }}</span>
          </span>
          <span class="unit">个</span>
        </div>
      </div>
    </div>
    <div class="itemBox">
      <div class="item" v-for="(item,i) in warnings" :key="i" :class="{mleft:i == 0 || i == 4}">
        <div class="value">{{item.value}}</div>
        <div class="name">{{item.name}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import {getYjjrList} from "@/api/warning";
export default {
  name: "warningNums",
  data() {
    return {
      nums:5300, //预警总数
      warnings:[
        {
          key:"dcTotal",
          name:"金华地磁(个)",
          value:1224,
        },
        {
          key:"dgTotal",
          name:"智慧灯杆(个)",
          value:128,
        },
        {
          key:"ljtTotal",
          name:"智慧垃圾桶(个)",
          value:5613,
        },
        {
          key:"hjjcTotal",
          name:"环境监测设备(个)",
          value:1099,
        },
        {
          key:"jgTotal",
          name:"智慧井盖(个)",
          value:3328,
        },
        {
          key:"szTotal",
          name:"水质设备(个)",
          value:18,
        },
        {
          key:"kqzTotal",
          name:"空气站设备(个)",
          value:288,
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getYjjrList().then(res => {
        this.nums = res.data.data.sbTotal;
        this.warnings.forEach((item,i) => {
          item.value = res.data.data[item.key]
        })
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .unit {
    font-size: 32px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #D6E7F9;
    background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 10px;
  }
  .nums {
    margin-top: 34px;
    .countLine {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 70px;
      .counts {
        width: 424px;
        height: 51px;
        background: url("@/assets/home/<USER>/Base.png") no-repeat;
        background-size: cover;
        .countText {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          position: relative;
          bottom: 40px;
          .text {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            margin-left: 37px;
            margin-right: 8px;
            white-space: nowrap;
          }
          .number {
            width: 45px;
            height: 63px;
            background: url("@/assets/common/countobg.png") no-repeat;
            background-size: cover;
            text-align: center;
            margin-left: 11px;
            .numberText {
              font-size: 60px;
              font-family: BN;
              font-weight: 400;
              color: #FFFFFF;
              line-height: 63px;
              background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }
        }
      }
    }
    .itemBox {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      flex-wrap: wrap;
      width: 1935.6px;
      height: 680px;
      .mleft {
        margin-left: 0 !important;
      }
      .item {
        width: 292px;
        height: 340px;
        background: url("@/assets/home/<USER>/warningitem.png");
        background-size: cover;
        text-align: center;
        margin-left: 218px;
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #FFFFFF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-top: 45px;
          margin-left: unset !important;
        }
        .name {
          font-size: 36px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          margin-top: 100px;
        }
      }
    }
  }
</style>