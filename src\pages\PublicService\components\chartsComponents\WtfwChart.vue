<template>
  <div class="chartContainer">
    <div class="tabList">
      <div class="tab-left">借阅数量统计</div>
      <div class="tab-right">
        <div class="tab-item" v-for="(item,i) in tabList" :key="i" :class="{tabActive: i == currentTab}" @click="dateChange(item,i)">{{item.name}}</div>
      </div>
    </div>
    <div id="wtfwChart" class="wtfwChart"></div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tabList: [
        {
          name:"日"
        },
        {
          name:"月"
        },
        {
          name:"年"
        }
      ],
      currentTab: 0,
      chartsData: [
        {
          name:"5-15",
          value:432
        },
        {
          name:"5-16",
          value:331
        },
        {
          name:"5-17",
          value:257
        },
        {
          name:"5-18",
          value:479
        },
        {
          name:"5-19",
          value:517
        },
        {
          name:"5-20",
          value:263
        },
        {
          name:"5-21",
          value:220
        }
      ]
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      let myChart = this.$echarts.init(document.getElementById("wtfwChart"))
      var data2 = this.chartsData.map(item => item.value)
      var data1 = []
      for (var i = 0; i < data2.length; i++) {
        data1.push({
          value: 8,
          label: data2[i],
        })
      }
      var barWidth = 21
      let option = {
        grid: {
          top: "25%",
          left: "10%",
          right: "0%",
          bottom: "20%",
          // containLabel: true,
        },
        legend: {
          show: false,
          // icon: "circle",
          right: '5%',
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false },
          data: this.chartsData.map(item => item.name),
        },
        yAxis: {
          name: "单位：本",
          type: 'value',
          nameTextStyle: {
            fontSize: 28,
            color: "#D6E7F9",
            padding: [10, 0, 20, 10],
          },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            // 上半截柱子
            // name: "接待游客人数",
            type: "bar",
            barWidth: barWidth,
            barGap: "-100%",
            z: 0,
            itemStyle: {
              color: "#0D4C71",
              opacity: 1,
            },
            data: data1,
          },
          {
            //下半截柱子
            name: "接待游客人数",
            type: "bar",
            barWidth: barWidth,
            barGap: "-100%",
            itemStyle: {
              opacity: 1,
              color: function (params) {
                return new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "#00C0FF", // 0% 处的颜色
                      // color: "red", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#84FFF2", // 100% 处的颜色
                    },
                  ],
                  false
                )
              },
            },
            data: data2,
          }
        ],
      }

      myChart.setOption(option)
    },
    dateChange(item,i) {
      this.currentTab = i;
      switch (item.name) {
        case "日":
          this.chartsData = [
            {
              name:"5-15",
              value:432
            },
            {
              name:"5-16",
              value:331
            },
            {
              name:"5-17",
              value:257
            },
            {
              name:"5-18",
              value:479
            },
            {
              name:"5-19",
              value:517
            },
            {
              name:"5-20",
              value:263
            },
            {
              name:"5-21",
              value:220
            }
          ];
          this.initCharts()
          break;
        case "月":
          this.chartsData = [
            {
              name:"1月",
              value:1217
            },
            {
              name:"2月",
              value:1421
            },
            {
              name:"3月",
              value:1212
            },
            {
              name:"4月",
              value:1523
            },
            {
              name:"5月",
              value:1386
            },
            {
              name:"6月",
              value:0
            },
            {
              name:"7月",
              value:0
            },
            {
              name:"8月",
              value:0
            },
            {
              name:"9月",
              value:0
            },
            {
              name:"10月",
              value:0
            },
            {
              name:"11月",
              value:0
            },
            {
              name:"12月",
              value:0
            }
          ];
          this.initCharts()
          break;
        case "年":
          this.chartsData = [
            {
              name:"2019",
              value:12412
            },
            {
              name:"2020",
              value:13214
            },
            {
              name:"2021",
              value:14139
            },
            {
              name:"2022",
              value:17391
            },
            {
              name:"2023",
              value:13793
            }
          ];
          this.initCharts()
          break;
      }
    }
  },
}
</script>

<style scoped lang="less">
.chartContainer {
  width: 100%;
  .tabList {
    position: relative;
    left: 390px;
    top: 30px;
    width: 550px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    .tab-left {
      font-family: Source Han Sans SC;
      font-weight: bold;
      font-size: 32px;
      color: #D6E7F9;
      text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
      background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .tab-right {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .tab-item {
        width: 96px;
        height: 42px;
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 32px;
        color: #D1D6DF;
        background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 300%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        cursor: pointer;
        text-align: center;
      }
      .tabActive {
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 32px;
        color: #FFFFFF !important;
        background: url("~@/assets/publicService/tabAbg.png") no-repeat;
        background-size: cover;
        -webkit-text-fill-color: unset !important;
      }
    }
  }
  .wtfwChart {
    width: 100%;
    height: 310px;
  }
}
</style>