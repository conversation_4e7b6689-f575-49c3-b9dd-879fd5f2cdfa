<template>
  <el-dialog
    :top="winTop"
    ref="commomDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="visible"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="container">
      <div class="top">指标详情</div>
      <div class="close mouse-pointer" @click="close"><i class="el-icon-close"></i></div>
      <div class="title">
        <img class="title-img" src="@/pages/IndicatorCenter/indexCenter/dialog/img/btn-left.png" alt="" />
        <span>{{ info.indicatorName || '-' }}</span>
        <span class="s-c-yellow-gradient">
          {{ info.indicatorValue || '-' }}
        </span>
        <span class="s-c-yellow-gradient">
          {{ info.indicatorunit || '-' }}
        </span>
        <img class="title-img" src="@/pages/IndicatorCenter/indexCenter/dialog/img/btn-right.png" alt="" />
      </div>
      <div class="contant">
        <div class="con-left">
          <nav style="padding: 30px 160px 0">
            <div class="flex-c">
              <div class="header-left"></div>
              <div class="smallHeader">基本信息</div>
              <div class="header-right"></div>
            </div>
          </nav>
          <div class="left-box">
            <div class="grid-item" v-for="(el, index) in listData2" :key="index">
              <img src="@/pages/IndicatorCenter/indexCenter/dialog/img/icon.png" alt="" />
              <span class="s-c-blue-gradient s-font-40">{{ el.name || '-' }}</span>

              <div v-if="index == 6 && info.name == '空气质量指数（AQI)'">
                <img src="@/pages/IndicatorCenter/indexCenter/dialog/img/yjjb.png" alt="" />
              </div>
              <div class="s-c-white s-font-40" :class="index === 0 ? 'auto' : ''" v-else>{{ el.value || '-' }}</div>
            </div>
          </div>
        </div>
        <div class="con-right">
          <nav style="padding: 30px 160px 0">
            <div class="flex-c">
              <div class="header-left"></div>
              <div class="smallHeader">指标趋势图</div>
              <div class="header-right"></div>
            </div>
          </nav>
          <div class="right-box">
            <div id="chart" style="width: 1200px; height: 600px; margin-top: 100px"></div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import { getIndicatorDetail, getIndicatorCharts } from '@/api/IndicatorCenter/indicatorDialog.js'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      winTop: '40px',
      info: {},
      listData2: [],
      dangerValue: '',
      emptyData: [
        {
          name: '指标定义',
          value: '--',
          key: 'description',
        },
        {
          name: '责任部门',
          value: '--',
          key: 'departmentName',
        },
        // {
        //   name: '所属系统',
        //   value: '--',
        // },
        // {
        //   name: '业务责任人',
        //   value: '--',
        // },
        // {
        //   name: '分管领导',
        //   value: '--',
        // },
        {
          name: '更新频率',
          value: '--',
          key: 'frequence',
        },
        // {
        //   name: '阈值标准',
        //   value: '--',
        // },
        // {
        //   name: '阈值结果',
        //   value: '--',
        // },
        {
          name: '数据更新时间',
          value: '--',
          key: 'zbUpdateTime',
        },
      ],
      zbqstData: [],
    }
  },
  watch: {
    if(newVal) {
      let that = this
      this.$nextTick(() => {
        setTimeout(() => {
          that.winTop =
            (document.documentElement.clientHeight - $(that.$refs.commomDialog.$el).find('.el-dialog').height()) / 2 +
            'px'
        }, 100)
      })
    },
  },
  mounted() {
    getIndicatorDetail({ indicatorId: this.id }).then((res) => {
      this.info = res.data.data
      this.listData2 = JSON.parse(JSON.stringify(this.emptyData))
      this.listData2.forEach((item) => {
        item.value = res.data.data[item.key]
      })
    })
    getIndicatorCharts({ indicatorId: this.id }).then((res) => {
      this.zbqstData = res.data.data.map((item) => {
        return {
          name: item.indicatorTime,
          value: item.indicatorValue,
          unit: item.indicatorUnit,
        }
      })
      this.getChart(this.zbqstData)
    })
  },
  methods: {
    getChart(data) {
      let xData = data.map((x) => x.name)
      let yData = data.map((x) => x.value)
      let myEcharts = this.$echarts.init(document.getElementById('chart'))
      let option = {
        color: ['#e86056', '#F5CC53', '#6AE4B2'],
        title: {
          show: data.length == 0 ? true : false, // 无数据时展示 title
          textStyle: {
            color: '#fff',
            fontSize: 35,
          },
          text: '暂无数据',
          left: 'center',
          top: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          selectedMode: false,
          top: '0px',
          right: '24%',
          textStyle: {
            color: '#fff',
            fontSize: 28,
            fontFamily: 'SourceHanSansCN-Medium',
          },
          itemWidth: 18,
          itemHeight: 8,
        },
        grid: {
          top: '20%',
          left: '10%',
          right: '5%',
          bottom: '2%',
          containLabel: true,
        },

        xAxis: [
          {
            type: 'category',
            data: xData,
            splitLine: { show: false },
            axisTick: {
              //y轴刻度线
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisLabel: {
              interval: 0,
              rotate: xData.length > 7 ? 40 : 0,
              textStyle: {
                color: '#ccc',
                fontSize: 28,
                fontFamily: 'SourceHanSansCN-Medium',
              },
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: data[0]?.unit ? data[0].unit : '',
            nameTextStyle: {
              color: '#ccc',
              fontSize: 28,
              fontFamily: 'SourceHanSansCN-Medium',
              padding: [20, 20],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#ccc',
                fontSize: 28,
                fontFamily: 'SourceHanSansCN-Medium',
              },
            },
          },
        ],
        series: [
          {
            cursor: 'auto',
            name: '',
            symbol: 'circle', //设定为实心点
            symbolSize: 15, //设定实心点的大小
            type: 'line',
            data: yData,
            barWidth: 20,
            itemStyle: {
              normal: {
                color: 'rgb(0,136,212)',
                borderColor: 'rgba(0,136,212,0.2)',
                borderWidth: 30,
                lineStyle: {
                  color: 'rgba(0,136,212)',
                },
              },
            },
            label: {
              show: true, //开启显示
              position: 'top', //在上方显示
              textStyle: {
                //数值样式
                color: '#FFFFFF',
                fontFamily: 'SourceHanSansCN-Regular',
                fontSize: 28,
              },
              // formatter: (params) => {
              //   if (params.data[2] !== '000') {
              //     return params.data[2]
              //   } else {
              //     return ''
              //   }
              // },
            },
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(0, 136, 212, 0.3)',
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(0, 136, 212, 0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(0, 0, 0, 0.1)',
                shadowBlur: 40,
              },
            },
          },
        ],
      }
      myEcharts.setOption(option)
    },

    close() {
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
@import '@/pages/IndicatorCenter/indexCenter/dialog/css/zbzx-dialog.css';
// 重置element-ui弹框
/deep/.el-dialog {
  // width: 3000px;
  // height: 1800px;
  // background: rgba(0, 15, 55, 0.9);
  margin-top: 200px;
  background: transparent;
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  margin-left: 2350px !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.title-container .close-btn {
  float: right;
  margin: 15px;
  cursor: pointer;
  font-size: 36px;
  color: white;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.container {
  margin-top: 400px;
  width: 2602px;
  height: 1434px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/back.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.close {
  position: absolute;
  right: 40px;
  top: 50px;
  font-size: 60px;
  color: white;
  font-weight: bold;
}

.top {
  width: 100%;
  height: 93px;
  line-height: 93px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/top-back.png') no-repeat;
  background-size: 100% 100%;
  text-align: center;
  font-size: 60px;
  font-weight: bold;
  color: #ffffff;
  position: relative;
  top: 50px;
}

.title {
  width: 100%;
  text-align: center;
  font-size: 50px;
  color: #ffffff;
  margin: 100px auto 30px;
}

.title-img {
  margin-bottom: -7px;
}

.contant {
  width: 100%;
  height: 1090px;
  display: flex;
  padding: 0px 50px;
  box-sizing: border-box;
}

.con-left {
  width: 50%;
}

.con-right {
  width: 50%;
}

.left-box {
  padding: 0px 150px;
  box-sizing: border-box;
}

.grid-item {
  margin-top: 40px;
  display: flex;
}

.grid-item > img {
  width: 31px;
  height: 31px;
  margin-top: 13px;
}

.grid-item > span {
  margin-left: 30px;
  white-space: nowrap;
}

.grid-item > div {
  margin-left: 30px;
}

.mouse-pointer {
  cursor: pointer;
}

.mouse-not {
  cursor: not-allowed;
}

.auto {
  max-height: 230px;
  overflow-y: auto;
}

/*色卡*/
.color-card {
  width: 662px;
  height: 100px;
  /* background: rgba(12, 38, 65, 0.9);
      border: 1px solid; */
  /* padding: 18px 0px 0px 24px; */
  box-sizing: border-box;
  position: relative;
  top: -27px;
}

.color {
  width: 660px;
  height: 35px;
  background: linear-gradient(
    92deg,
    #caf681 0%,
    #ffd053 25%,
    #ffd053 40%,
    #ffd053 45%,
    #ffa144 52%,
    #ed1f33 80%,
    #fd0100 88%
  );
  /* opacity: 0.8; */
  position: absolute;
  top: 55px;
  border-radius: 20px;
}

.number {
  width: 451px;
  height: 20px;
  justify-content: space-between;
  display: flex;
  /* display: flex; */
  position: absolute;
  top: 35px;
  left: 80px;
}

.bg {
  width: 12px;
  height: 31px;
  display: block;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/下三角.png');
  background-size: 100% 100%;
}

.kd {
  position: absolute;
  top: -29px;
  font-size: 24px;
}

.desc {
  font-size: 24px;
  color: #fff;
  position: absolute;
  left: -40px;
  top: 19px;
  line-height: 35px;
}
.s-font-40 {
  font-size: 40px;
}
.s-c-blue-gradient {
  background: linear-gradient(to bottom, #ccf4ff, #ffffff, #00baf8, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}
.s-c-white {
  color: #ffffff;
}

.flex-c {
  display: flex;
  align-items: center;
}
.smallHeader {
  font-family: SourceHanSansCN-Medium;
  font-size: 36px;
  padding: 0 20px;
  font-weight: 800;
  font-stretch: normal;
  line-height: 38px;
  letter-spacing: 3px;
  background: linear-gradient(to bottom, #f0ffff, #74b4f4, #83b8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
  white-space: nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.header-left {
  width: 100%;
  height: 50px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/title-left.png');
  background-size: 147px 57px;
  background-repeat: no-repeat;
  background-position: 100% 0;
}
.header-right {
  width: 100%;
  height: 50px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/title-right.png');
  background-size: 147px 57px;
  background-repeat: no-repeat;
}
</style>
