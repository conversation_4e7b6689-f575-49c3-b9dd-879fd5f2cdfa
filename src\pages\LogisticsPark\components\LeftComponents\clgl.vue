<template>
  <div class="container">
    <Ktitle :titleText="'车辆管理'" width="939.6" :show-date="false" />
    <div class="indexList">
      <div class="indexItem" v-for="(item,i) in indexList" :key="i">
        <div class="indexItemName">{{item.name}}</div>
        <div class="indexItemNumber">{{item.value}}</div>
      </div>
    </div>
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 10px"
      :height="265"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "clgl",
  components: {
    Ktitle,
    zhtxTable
  },
  data() {
    return {
      indexList: [
        {
          name:"车辆数量",
          value:"39"
        },
        {
          name:"货运量",
          value:"50.34"
        }
      ],
      tableData: [
        {"lx":"厢式货车","carNumber":"浙G88A56","hyl":"12吨","name":"刘明伟","phone":15205798856,"lng":119.38613,"lat":29.08665,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G66B78","hyl":"8吨","name":"李程欢","phone":13805791234,"lng":119.407,"lat":29.10132,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G33C90","hyl":"5吨","name":"王坚强","phone":18605797788,"lng":119.37108,"lat":29.04829,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G44D12","hyl":"20吨","name":"赵敏伟","phone":13905795566,"lng":119.38738,"lat":29.04015,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G22E34","hyl":"15吨","name":"刘洋","phone":15005799900,"lng":119.40065,"lat":29.01385,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G77F56","hyl":"9吨","name":"陈晨","phone":13605793344,"lng":119.63421,"lat":29.0611,"type":"car"},
        {"lx":"危险品运输车","carNumber":"浙G11G78","hyl":"3吨","name":"周海涛","phone":15305796677,"lng":119.66207,"lat":29.07757,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G55H90","hyl":"10吨","name":"吴磊","phone":18005792233,"lng":119.62548,"lat":29.07192,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G99J12","hyl":"7吨","name":"郑渊洁","phone":13505798877,"lng":119.60108,"lat":29.05589,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G33K34","hyl":"18吨","name":"孙浩飞","phone":15105794455,"lng":119.59639,"lat":29.06452,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G66L56","hyl":"11吨","name":"黄燕","phone":13705796688,"lng":119.60017,"lat":29.07446,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G22M78","hyl":"6吨","name":"郭凯","phone":15905799977,"lng":119.6056,"lat":29.04444,"type":"car"},
        {"lx":"危险品运输车","carNumber":"浙G88N90","hyl":"4吨","name":"徐静","phone":18105793322,"lng":119.65027,"lat":29.05783,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G55Q34","hyl":"16吨","name":"胡波","phone":15505797766,"lng":119.62254,"lat":29.06208,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G99R56","hyl":"13吨","name":"马丽","phone":13405795588,"lng":119.60931,"lat":29.05499,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G77S78","hyl":"22吨","name":"罗翔","phone":18205791122,"lng":119.64897,"lat":29.05621,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G33T12","hyl":"10吨","name":"谢明","phone":15605798899,"lng":119.60857,"lat":29.0208,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙A87654","hyl":"12吨","name":"张伟","phone":15934567890,"lng":119.58751,"lat":29.06763,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G98765","hyl":"8吨","name":"李天华","phone":13812345678,"lng":119.60107,"lat":29.04629,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙GC5432","hyl":"15吨","name":"王鹏","phone":18654321098,"lng":119.64003,"lat":29.09121,"type":"car"},
        {"lx":"牵引车","carNumber":"浙GD7543","hyl":"20吨","name":"赵敏","phone":13798765432,"lng":119.62487,"lat":29.01323,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙GE8654","hyl":"10吨","name":"刘鹏","phone":15267890123,"lng":119.62326,"lat":29.00037,"type":"car"},
        {"lx":"自卸车","carNumber":"浙G45078","hyl":"18吨","name":"陈伟","phone":13623456789,"lng":119.5942,"lat":28.99342,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G56239","hyl":"25吨","name":"周涛","phone":15109876543,"lng":119.38816,"lat":28.98431,"type":"car"},
        {"lx":"罐式货车","carNumber":"浙GT6720","hyl":"14吨","name":"吴磊","phone":18012345678,"lng":119.4196,"lat":28.95742,"type":"car"},
        {"lx":"平板货车","carNumber":"浙GU8901","hyl":"9吨","name":"孙敏伟","phone":13598764321,"lng":119.39376,"lat":28.99466,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G89012","hyl":"11吨","name":"郑悦","phone":15345678901,"lng":119.62768,"lat":29.07899,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G8A568","hyl":"12吨","name":"张辉","phone":15800134567,"lng":119.6181,"lat":29.044,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G2B876","hyl":"8吨","name":"李辉","phone":13567890123,"lng":119.63675,"lat":29.0447,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G5C346","hyl":"15吨","name":"王强","phone":18612345678,"lng":119.59778,"lat":29.01058,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G1D754","hyl":"20吨","name":"赵伟","phone":13987654321,"lng":119.42074,"lat":29.0457,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G6E865","hyl":"10吨","name":"刘伟","phone":15200001234,"lng":119.39024,"lat":29.0878,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G3F245","hyl":"18吨","name":"陈静","phone":13654321098,"lng":119.38735,"lat":29.0794,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G9G643","hyl":"9吨","name":"周帅","phone":15912345678,"lng":119.36172,"lat":29.03836,"type":"car"},
        {"lx":"冷藏车","carNumber":"浙G4H365","hyl":"13吨","name":"吴峰","phone":18001234567,"lng":119.35525,"lat":29.03414,"type":"car"},
        {"lx":"集装箱货车","carNumber":"浙G7I481","hyl":"22吨","name":"郑洁","phone":13765432109,"lng":119.62362,"lat":29.08496,"type":"car"},
        {"lx":"自卸货车","carNumber":"浙G0J906","hyl":"11吨","name":"孙浩","phone":15098765432,"lng":119.59041,"lat":28.97872,"type":"car"},
        {"lx":"厢式货车","carNumber":"浙G1245A","hyl":"15吨","name":"张敏","phone":13800138000,"lng":119.61193,"lat":29.01804,"type":"car"},
        {"lx":"平板货车","carNumber":"浙G2343B","hyl":"22吨","name":"李娜","phone":15900159001,"lng":119.65189,"lat":29.07422,"type":"car"},

      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '车牌号',
          key: 'carNumber',
          flex: 3,
        },
        {
          label: '车辆类型',
          key: 'lx',
          flex: 3,
        },
        {
          label: '货运量',
          key: 'hyl',
          flex: 2,
        },
      ],
    }
  },
  computed: {},
  created() {
    this.tableData.forEach((item,i) => item["index"] = (i + 1))
  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .indexList {
      width: 100%;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 14px;
      .indexItem {
        width: 301px;
        height: 172px;
        background: url("@/assets/Logistics/carManageBg.png") no-repeat;
        background-size: cover;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;

        .indexItemName {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 36px;
          color: #FFFFFF;
          margin-top: -25px;
        }
        .indexItemNumber {
          font-family: BN;
          font-weight: 400;
          font-size: 72px;
          color: #9AA9BF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-top: 10px;
        }
      }
    }
  }
</style>