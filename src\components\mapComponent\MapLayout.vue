<template>
  <div class="mapContainer" id="mapInstance">
    <!--视频设备列表-->
    <div class="spsbPanelWrap">
      <div class="spsbBtn" @click="SpPopClick">视频设备</div>
      <transition
        enter-active-class="animate__animated animate__fadeInLeft"
        leave-active-class="animate__animated animate__fadeOut"
      >
        <div v-show="showSpsb">
          <SpsbPanel />
        </div>
      </transition>
    </div>
    <!--感知设备列表-->
    <div class="gzsbPanelWrap">
      <transition
        enter-active-class="animate__animated animate__fadeInRight"
        leave-active-class="animate__animated animate__fadeOut"
      >
        <div v-show="showGzsb">
          <GzsbPanel />
        </div>
      </transition>
      <div class="gzsbBtnWrap">
        <div class="gzsbBtn" @click="showGzsb = !showGzsb">感知设备</div>
      </div>
    </div>
    <!--地图图层服务-->
    <div class="layerPanelWrap">
      <div class="layerBtn" @click="layerPopClick">图层</div>
      <transition
        enter-active-class="animate__animated animate__fadeInLeft"
        leave-active-class="animate__animated animate__fadeOut"
      >
        <div v-show="showLayer">
          <LayerPanel ref="LayerPanel" style="z-index: 99" />
        </div>
      </transition>
    </div>
    <!--地图基础功能列表-->
    <div class="mapBaseFuncList">
      <img class="znzClass" :src="require(`@/assets/common/znz.png`)" @click="shijiao" />
      <img class="fdClass" :src="require(`@/assets/common/3d.png`)" @click="toErD(2)" />
      <img class="fdClass" :src="require(`@/assets/common/2d.png`)" @click="toErD(3)" />
      <img class="fdClass" :src="require(`@/assets/common/fd.png`)" @click="zoomIn" />
      <img class="fdClass" :src="require(`@/assets/common/sx.png`)" @click="zoomOut" />
    </div>
    <!--地图功能列表-->
    <div class="MapFuncLists">
      <MapFuncList
        @funClick="funClick"
        @openPage="openPage"
        @closeFunDialog="closeFunDialog"
        @showNumberKeyboard="showNumberKeyboard"
      />
    </div>
    <!--政务服务弹窗-->
    <Commondialog :title="dialogTitle" :dialogFlag="zwfwDialogShow" @close="zwfwDialogShow = false">
      <CommonDialogTable :title-with-key="titleList" :table-data="tableData"></CommonDialogTable>
    </Commondialog>
    <!-- 事件类型弹框 -->
    <Commondialog
      :dialog-flag="sjlxDialogShow"
      :title="dialogTitle"
      :dialogWidth="'1300px'"
      @close="sjlxDialogShow = false"
    >
      <EventDetails :showFlag="sjlxDialogShow" :id="eventId" v-if="sjlxDialogShow" />
    </Commondialog>
    <!--预警信息弹窗-->
    <Commondialog :title="dialogTitle" :dialogFlag="warnDialogShow" @close="warnDialogShow = false">
      <WarningPopup />
    </Commondialog>
    <!--视频弹窗-->
    <VideoPopup :showFlag="showFlag" @closeVideo="showFlag = false" :videoKey="paramsVideo" />
    <!--地图功能弹窗-->
    <fun-wrap-pop :name="funcName" v-show="funPopShow" class="funPop" />
    <!--拨号键盘弹窗-->
    <NumberKeyboard v-show="showKeyboard" @close="showKeyboard = false" @call="call" />
    <!--公用详情弹窗-->
    <Commondialog :title="dialogTitle" :dialogFlag="showCommonDialog" @close="showCommonDialog = false">
      <CommonDialogDetail :detailData="itemObj" :keyDesc="keyList"></CommonDialogDetail>
    </Commondialog>
    <!--视频通话弹窗-->
    <Commondialog
      :title="dialogTitle"
      :dialogFlag="showVideoCall"
      @close="showVideoCall = false"
      :dialog-width="'2950px'"
    >
      <callVideo :number="number"></callVideo>
    </Commondialog>
    <!--电话弹窗弹窗-->
    <Commondialog
      :title="dialogTitle"
      :dialogFlag="showPhoneCall"
      @close="showPhoneCall = false"
      :dialog-width="'2950px'"
    >
      <callPhone :number="number"></callPhone>
    </Commondialog>
    <div v-if="visible">
      <qyhxDetail :allMessage="allMessage" :visible="visible" @close="visible = false"></qyhxDetail>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import WarningPopup from '@/components/mapComponent/components/WarningPopup.vue'
import VideoPopup from '@/components/mapComponent/components/VideoPopup.vue'
import { mapMutations } from 'vuex'
import SpsbPanel from '@/components/mapComponent/components/SpsbPanel.vue'
import GzsbPanel from '@/components/mapComponent/components/GzsbPanel.vue'
import LayerPanel from '@/components/mapComponent/components/LayerPanel'
import MapFuncList from '@/components/mapComponent/MapFuncList'
import funWrapPop from '@/components/mapComponent/components/funWrapPop'
import Commondialog from '@/components/Commondialog'
import CommonDialogTable from '@/components/CommonDialogTable'
import EventDetails from '@/components/EventDetails' //事件详情
import kfqJon from '@/data/kfq.json'
import CommonDialogDetail from '@/components/CommonDialogDetail'
import NumberKeyboard from '@/components/mapComponent/components/NumberKeyboard'
import callPhone from '@/components/callPhone'
import callVideo from '@/components/callVideo'
import qyhxDetail from '@/pages/qyzf/components/qyhxIndex'
import { getEventDetail } from '@/api/EventTask'
export default {
  components: {
    WarningPopup,
    SpsbPanel,
    GzsbPanel,
    VideoPopup,
    LayerPanel,
    MapFuncList,
    funWrapPop,
    Commondialog,
    CommonDialogTable,
    EventDetails,
    CommonDialogDetail,
    NumberKeyboard,
    callPhone,
    callVideo,
    qyhxDetail
  },
  data() {
    return {
      showSpsb: false,
      showGzsb: false,
      showLayer: false,
      paramsVideo: '',
      showFlag: false,
      videoCode: '',
      funcName: '',
      funPopShow: false,
      dialogTitle: '',
      zwfwDialogShow: false,
      showCommonDialog: false,
      showVideoCall: false,
      showPhoneCall: false,
      showKeyboard: false,
      keyList: [],
      titleList: [],
      tableData: [],
      itemObj: {},
      //事件内容id
      eventId: '',
      sjlxDialogShow: false,
      PointkeyList: [
        {
          title: '公共服务',
          key: 'ggfw',
        },
        {
          title: '综治工作',
          key: 'zzgz',
        },
        {
          title: '经济生态',
          key: 'jjst',
        },
        {
          title: '政务服务',
          key: 'zwfw',
        },
        {
          title: '环境监测',
          key: 'hjjc',
        },
        {
          title: '市政设施',
          key: 'szss',
        },
        {
          title: '交通管理',
          key: 'jtgl',
        },
        {
          title: '应急管理',
          key: 'yjgl',
        },
        {
          title: '监管执法',
          key: 'jgzf',
        },
        {
          title: '环境保护',
          key: 'hjbh',
        },
        {
          title: '经济发展',
          key: 'jjfz',
        },
        {
          title: '文化建设',
          key: 'whjs',
        },
        {
          title: '社区治理',
          key: 'sqzl',
        },
        {
          title: '基础设施',
          key: 'jcss',
        },
        {
          title: '卫生健康',
          key: 'wsjk',
        },
        {
          title: '预警',
          key: 'iotWarn',
        },
        {
          title: '预警',
          key: 'ManualWarn',
        },
        {
          title: '预警',
          key: 'peopleWarn',
        },
        {
          title: '事件',
          key: 'event',
        },
        {
          title: '舆情',
          key: 'yqxx',
        },
        {
          title: '物流园区',
          key: 'logisticsPark',
        },
        {
          title: '货车',
          key: 'car',
        },
      ],
      warnDialogShow: false,
      kfqJon: kfqJon,
      number: '',
      visible: false,
      allMessage: [],
    }
  },
  mounted() {
    this.initMap()
    this.$EventBus.$on('call', (e) => {
      this.call(e)
    })

    this.$EventBus.$on('flyTo', (e) => {
      this.CommonFly(e)
    })

    this.$EventBus.$on('zoomin', () => {
      this.zoomIn()
    })

    this.$EventBus.$on('loadPolygon', (res) => {
      this.CommonLoadPolygon(res)
    })

    this.$EventBus.$on('clearPolygon', () => {
      this.clearPolygon()
    })

    this.$EventBus.$on('drawPoint', (type, params, key) => {
      this.addMarker(type, params, key)
    })

    this.$EventBus.$on('rmAllLayer', (params, key) => {
      this.removeLayers(params, key)
    })

    //通用打开视频监控方法
    this.$EventBus.$on('openVideo', (videoCode) => {
      this.CommonOpenVideo(videoCode)
    })

    //通用打点事件 markerList:点位数组 layerId:图层id(唯一) imgUrl:点位图标 isClick:是否有点击事件 isBlur:是否有hover弹窗
    this.$EventBus.$on('CommonDrawPoint', (markerList, layerId, imgUrl, isCluster, isClick, isBlur) => {
      this.CommonPointDraw(markerList, layerId, imgUrl, isCluster, isClick, isBlur)
    })

    //企业制服
    this.$EventBus.$on('qyzfPoints', (markerList, layerId, imgUrl, isCluster, isClick, isBlur) => {
      this.CommonPointDraw1(markerList, layerId, imgUrl, isCluster, isClick, isBlur)
    })
  },

  methods: {
    SpPopClick() {
      this.showSpsb = !this.showSpsb
      this.showLayer = false
    },
    layerPopClick() {
      this.showLayer = !this.showLayer
      this.showSpsb = false
      if (this.showLayer == false) {
        this.$refs.LayerPanel.qxALLfun()
      }
    },
    closeFunDialog() {
      this.funPopShow = false
    },
    toErD(num) {
      top.ArcGisUtils.change2DOr3D(view, `${num}D`)
    },

    ...mapMutations('mapStatus', ['ADD_MARKER', 'DELETE_MARKER']),

    initMap() {
      console.log('initMap')

      // 地图初始视角
      let opts_home = {
        x: 119.65842342884746,
        y: 28.97890877935061,
        z: 10280.48295974452,
        // heading: 354.2661149152386,
        // tilt: 47.902020858006175,
        basemap: 'image2023',
        // basemap: 'KAIFAQU',
        mapDiv: 'mapInstance',
        token: '727d5ddcc1ae4d1f9f5aaccab7100a54',
        onload: () => {
          // 流光线
          mapUtil.tool.addEffect('flowline')
          this.mapMask([8, 40, 73, 0.9])
          // 白模
          // mapUtil.loadModelLayer({
          //   layerid: 'bmLayer',
          //   type: 'bm',
          // })
          ArcGisUtils.loadArcgisLayer(view, {
            type: 'scene',
            url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BM_kaifa_4490/SceneServer',
            id: 'KAIFAQUBAIMO',
            elevationInfo: {
              mode: 'on-the-ground',
            },
          })
        },
      }
      mapUtil.initMap(opts_home)
      // this.mapMask([8, 40, 73, 0.9])

      // const view = ArcGisUtils.initSceneView({
      //   divId: "mapInstance",
      //   // camera: defaultCamera,
      //   basemap: "KAIFAQU",
      //   viewingModeExtend: "global",
      //   token: "727d5ddcc1ae4d1f9f5aaccab7100a54"
      // })

      // ArcGisUtils.loadArcgisLayer(view, {
      //   type: "scene",
      //   url: "https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/BM_kaifa_4490/SceneServer",
      //   id: "KAIFAQUBAIMO",
      //   elevationInfo: {
      //     mode: "on-the-ground",
      //   },
      // })
    },

    mapMask(color) {
      top.mapUtil.mask({
        layerid: 'mask',
        data: this.kfqJon.data, // res 是要素集合
        style: {
          strokeWidth: 1.5,
          strokeColor: [29, 252, 254, 1], //多边形轮廓颜色透明度
          fillColor: color, //多边形填充色
          height: 50,
        },
        onclick: null,
      })
    },

    // 视频设备，感知设备打点方法
    addMarker(type, params, key) {
      // type['a':视频设备,'b':感知设备] params['1':父节点,'2':子节点] key[点位信息]
      console.log(key)
      let roadPointData = []
      switch (
        params // 判断选中节点
      ) {
        case 1: // 父节点
          if (key.children && key.children.length > 0) {
            //视频类型聚合
            this.CommonPointDraw(
              key.children,
              String(key.value),
              type == 'a' ? `/map/img/spdw.png` : `/map/img/gzsb.png`,
              type == 'a' ? true : false,
              true,
              true
            )
          }
          break
        case 2: // 子节点
          if (key.lng && key.lat) {
            roadPointData.push(key)
          }
          // console.log('打点的时候做了什么事',type, params, key)
          this.ADD_MARKER(String(key.value))
          this.CommonPointDraw(
            roadPointData,
            String(key.value),
            type == 'a' ? `/map/img/spdw.png` : `/map/img/gzsb.png`,
            type == 'a' ? true : false,
            true,
            true
          )
          break
      }
    },

    //通用打点方法 例子:iconImg:"/map/img/sj.png" layerid:"事件点位markerArray"
    CommonPointDraw(markerList, layerid, iconImg, isCluster, isClick, isBlur) {
      let PointData = []
      markerList.forEach((ele) => {
        if (ele.lng && ele.lat) {
          let marker = {
            lng: ele.lng,
            lat: ele.lat,
            data: ele,
          }
          PointData.push(marker)
        }
      })
      top.mapUtil.loadPointLayer({
        data: PointData,
        layerid: layerid,
        iconcfg: { image: iconImg, iconSize: 1 },
        onclick: isClick ? this.CommonPointClickFunc : false,
        onblur: isBlur ? this.CommonPointBlur : false,
        cluster: isCluster, //是否聚合
      })
    },

    //通用打点方法 例子:iconImg:"/map/img/sj.png" layerid:"事件点位markerArray"
    CommonPointDraw1(markerList, layerid, iconImg, isCluster, isClick, isBlur) {
      let PointData = []
      markerList.forEach((ele) => {
        if (ele.lng && ele.lat) {
          let marker = {
            lng: ele.lng,
            lat: ele.lat,
            data: ele,
          }
          PointData.push(marker)
        }
      })
      top.mapUtil.loadPointLayer({
        data: PointData,
        layerid: layerid,
        iconcfg: { image: iconImg, iconSize: 1 },
        onclick: isClick ? this.CommonPointClickFunc1 : false,
        onblur: isBlur ? this.CommonPointBlur : false,
        cluster: isCluster, //是否聚合
      })
    },

    //通用点位点击方法
    CommonPointClickFunc(e) {
      console.log(e, 'pointClick')
      this.CommonFly(e)
      this.dialogTitle =
        e.data.type != 'video' ? this.PointkeyList.find((item) => item.key == e.data.type).title + '详情' : ''
      switch (e.data.type) {
        //物联预警弹窗
        case 'iotWarn':
          this.keyList = [
            {
              label: '设备ID',
              key: 'title',
            },
            {
              label: '所属产品',
              key: 'name',
            },
            {
              label: '告警描述',
              key: 'content',
            },
            {
              label: '告警时间',
              key: 'time',
            },
            {
              label: '告警级别',
              key: 'levelDescribe',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          break
        //手动预警弹窗
        case 'ManualWarn':
          this.keyList = [
            {
              label: '预警标题',
              key: 'title',
            },
            {
              label: '发布单位',
              key: 'name',
            },
            {
              label: '预警内容',
              key: 'content',
            },
            {
              label: '发布时间',
              key: 'time',
            },
            {
              label: '预警级别',
              key: 'levelDescribe',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          break
        //人员预警弹窗
        case 'peopleWarn':
          this.keyList = [
            {
              label: '事件ID',
              key: 'title',
            },
            {
              label: '事件名称',
              key: 'name',
            },
            {
              label: '社区信息',
              key: 'content',
            },
            {
              label: '发生时间',
              key: 'time',
            },
            {
              label: '预警等级',
              key: 'levelDescribe',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          this.CommonLoadPolygon(e)
          break
        //视频类型
        case 'video':
          this.CommonOpenVideo(e.data.videoCode)
          break
        //政务服务类型
        case 'zwfw':
          this.titleList = e.data.titleList
          this.tableData = e.data.tableData
          this.zwfwDialogShow = true
          break
        //舆情信息类型
        case 'yqxx':
          this.keyList = [
            {
              label: '标题',
              key: 'title',
              isHalf: true,
            },
            {
              label: '发生时间',
              key: 'date',
              isHalf: true,
            },
            {
              label: '链接',
              key: 'url',
              isHalf: true,
            },
            {
              label: '来源',
              key: 'laiyuan',
              isHalf: true,
            },
            {
              label: '内容',
              key: 'content',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          break
        //物流园区点位
        case 'logisticsPark':
          this.keyList = [
            {
              label: '园区名称',
              key: 'name',
            },
            {
              label: '园区类型',
              key: 'lx',
            },
            {
              label: '详细地址',
              key: 'address',
            },
            {
              label: '主导产业',
              key: 'zdcy',
            },
            {
              label: '建筑面积',
              key: 'jzmj',
            },
            {
              label: '园区联系人',
              key: 'yqlxr',
            },
            {
              label: '联系电话',
              key: 'phone',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          break
        //货车点位
        case 'car':
          this.keyList = [
            {
              label: '货车类型',
              key: 'lx',
            },
            {
              label: '车牌号',
              key: 'carNumber',
            },
            {
              label: '货运量',
              key: 'hyl',
            },
            {
              label: '驾驶员姓名',
              key: 'name',
            },
            {
              label: '驾驶员联系信息',
              key: 'phone',
            },
          ]
          this.itemObj = e.data.info ? e.data.info : e.data
          this.showCommonDialog = true
          break
        default:
          // this.itemObj = e.data.info?e.data.info:e.data
          this.eventId = e.data.info ? e.data.info.id : e.data.id
          this.sjlxDialogShow = true
          break
      }
    },
    //企业智服点位点击方法
    CommonPointClickFunc1(e) {
      console.log(e, 'pointClick1')
      this.allMessage = [
        {
          qymc: e.data.name,
          tyshxydm: e.data.code,
        },
      ]
      this.visible = true
    },

    //通用打开视频方法
    CommonOpenVideo(videoCode) {
      this.showFlag = true
      this.paramsVideo = String(videoCode)
    },

    //通用飞行方法
    CommonFly(e) {
      top.mapUtil.flyTo({
        destination: [e.esX, e.esY],
        zoom: 17,
        offset: [0, 220],
      })
    },

    //通用加载区块
    CommonLoadPolygon(e) {
      this.clearPolygon()
      let geojsonData = {
        type: 'FeatureCollection',
        features: [e.data ? e.data.feature : e.feature],
      }
      top.mapUtil.loadPolygonLayer({
        layerid: 'PolygonLayer',
        data: geojsonData,
        style: {
          strokeColor: [255, 50, 40, 0.9], //多边形轮廓颜色透明度
          fillColor: [193, 210, 240, 0.2], //多边形填充色
        },
        onclick: function (e) {
          console.log('多边形点击事件')
        },
      })
    },

    //清除区块
    clearPolygon() {
      if (top.mapUtil.layers.PolygonLayer) {
        top.mapUtil.removeLayer('PolygonLayer')
      }
    },

    //通用点位hover方法
    CommonPointBlur(e) {
      console.log(e, 'eeeeee')
      let str = this.getBlurContent(e.data)
      let objData = {
        layerid: 'mouseHover',
        position: [e.lng, e.lat],
        content: str,
        offset: [50, -70],
      }
      top.mapUtil._createPopup(objData)
    },

    //获取悬浮展示模板
    getBlurContent(data) {
      console.log(data)
      switch (data.type) {
        //政务服务类
        case 'zwfw':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">受理窗口数: </div>
                          <div class="value">${data.slcks}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">自助窗口数: </div>
                          <div class="value">${data.zzcks}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">目前人流量: </div>
                          <div class="value">${data.mqrll}</div>
                      </div>
                  </div>`
          break
        case 'iotWarn':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">所属产品: </div>
                          <div class="value">${data.name}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">告警级别: </div>
                          <div class="value">${data.levelDescribe}</div>
                      </div>
                  </div>`
          break
        case 'ManualWarn':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">预警标题: </div>
                          <div class="value">${data.name}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">预警等级: </div>
                          <div class="value">${data.levelDescribe}</div>
                      </div>
                  </div>`
          break
        case 'peopleWarn':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">事件名称: </div>
                          <div class="value">${data.name}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">预警等级: </div>
                          <div class="value">${data.levelDescribe}</div>
                      </div>
                  </div>`
          break
        case 'yqxx':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">舆情名称: </div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="value">${data.value}</div>
                      </div>
                  </div>`
          break
        //物联感知设备
        case 'iotPoint':
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">设备类型: </div>
                          <div class="value">${data.typeName}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">经度: </div>
                          <div class="value">${data.lng}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">纬度: </div>
                          <div class="value">${data.lat}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">状态信息: </div>
                          <div class="value">${data.status}</div>
                      </div>
                  </div>`
          break
        case 'video':
          return `<div class="hoverContainer" style="color: white;">
                    <div class="hoverContainer-item">
                        <div class="key">点位名称: </div>
                        <div class="value">${data.label}</div>
                    </div>
                </div>`
          break
        case 'ryll':
          return `<div class="hoverContainer" style="color: white;">
                    <div class="hoverContainer-item">
                          <div class="key">类型: </div>
                          <div class="value">${data.name}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">镇（街道、乡）: </div>
                          <div class="value">${data.town}</div>
                      </div>
                </div>`
          break
        case 'cbzy':
          return `<div class="hoverContainer" style="color: white;">
                    <div class="hoverContainer-item">
                          <div class="key">街道: </div>
                          <div class="value">${data.town}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">级别: </div>
                          <div class="value">${data.level}</div>
                      </div>
                </div>`
          break
        case 'qyzf':
          return `<div class="hoverContainer" style="color: white;">
                    <div class="hoverContainer-item">
                          <div class="key">企业代号: </div>
                          <div class="value">${data.code}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">企业名称: </div>
                          <div class="value">${data.name}</div>
                      </div>
                </div>`
          break
        default:
          return `<div class="hoverContainer" style="color: white;">
                      <div class="hoverContainer-item">
                          <div class="key">类型: </div>
                          <div class="value">${data.lx}</div>
                      </div>
                      <div class="hoverContainer-item">
                          <div class="key">阶段: </div>
                          <div class="value">${data.jd}</div>
                      </div>
                  </div>`
          break
      }
    },

    shijiao() {
      top.mapUtil.tool.backHome()
    },

    zoomIn() {
      top.mapUtil.tool.zoomIn()
    },

    zoomOut() {
      top.mapUtil.tool.zoomOut()
    },

    // 清除地图
    rmLayer(id, key) {
      top.mapUtil.removeLayer(String(key))
    },

    removeAllLayers(id, key) {
      top.mapUtil.removeAllLayers(['点位1', '点位2'])
    },

    removeLayers(params, key) {
      // switch (params) {                              // 判断选中节点
      //   case 1:                                      // 父节点
      //     if (key.children && key.children.length > 0) {
      //       key.children.forEach(el => {
      //         this.removeLayers(2, el)
      //       })
      //     }
      //     break
      //   case 2:                                      // 子节点
      //     top.mapUtil.removeLayer(String(key.value))
      //     this.DELETE_MARKER(String(key.value))
      //     break
      // }

      top.mapUtil.removeLayer(String(key.value))
      this.DELETE_MARKER(String(key.value))
    },

    funClick(res) {
      //判断重复点击
      if (this.funcName == res) {
        this.funPopShow = !this.funPopShow
      } else {
        this.funcName = res
        this.funPopShow = true
      }
    },
    openPage(url) {
      this.$router.push(url)
    },
    //显示拨号键盘
    showNumberKeyboard() {
      this.showKeyboard = !this.showKeyboard
    },

    // res: {number: '111111111111',type: 'phone'/'video'}
    call(res) {
      this.number = res.number
      if (res.type == 'phone') {
        this.dialogTitle = '语音通话'
        this.showPhoneCall = true
      } else {
        this.dialogTitle = '视频通话'
        this.showVideoCall = true
      }
    },
  },
  beforeDestroy() {
    this.$EventBus.$off('zoomin')
    this.$EventBus.$off('drawPoint')
    this.$EventBus.$off('rmAllLayer')
    this.$EventBus.$off('CommonDrawPoint')
    this.$EventBus.$off('loadPolygon')
    this.$EventBus.$off('clearPolygon')
    this.$EventBus.$off('call')
    this.$EventBus.$off('flyTo')
  },
}
</script>

<style lang="less" scoped>
.mapContainer {
  width: 100%;
  height: 100%;
  // width: 680px;
  // height: 160px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background-color: #052c4d;

  .spsbPanelWrap {
    position: absolute;
    top: 160px;
    left: 2097px;
    z-index: 2;
    // background-color: beige;
    display: flex;

    .spsbBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .gzsbPanelWrap {
    position: absolute;
    top: 229px;
    right: 2097px;
    z-index: 2;
    // background-color: beige;
    display: flex;
    .gzsbBtnWrap {
      display: flex;
      flex-direction: column;
      align-items: center;

      .gzsbBtn {
        width: 60px;
        height: 260px;
        padding: 36px 0 24px 24px;
        margin: 158px 13px 0 0;
        cursor: pointer;
        font-size: 38px;
        font-family: FZZhengHeiS-DB-GB;
        font-weight: 400;
        color: #feffff;
        background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }

  .layerPanelWrap {
    position: absolute;
    top: 510px;
    left: 2097px;
    z-index: 2;
    // background-color: beige;
    display: flex;

    .layerBtn {
      padding: 36px 0 24px 24px;
      margin: 158px 0 0 13px;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      width: 60px;
      height: 260px;
      background: url('~@/assets/common/lspsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .mapBaseFuncList {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    top: 910px;
    left: 2117px;
    .znzClass {
      width: 80px;
      height: 80px;
      margin-top: 105px;
      cursor: pointer;
    }

    .fdClass {
      width: 56px;
      height: 56px;
      margin-top: 32px;
      cursor: pointer;
    }
  }

  .MapFuncLists {
    position: absolute;
    top: 750px;
    right: 2117px;
  }

  .funPop {
    position: absolute;
    top: 229px;
    right: 2220px;
  }

  /******************************** 原生地图popup样式修改 ********************************/
  /deep/ .mapPopup {
    width: 1592px !important;
    height: fit-content;
    background: rgba(3, 24, 39, 0.88);
    border: 1px solid #afdcfb;
    box-shadow: 0px 3px 35px 0px #000000;
    border-radius: 50px;
    display: flex !important;
    flex-direction: column !important;
    justify-content: space-between !important;
  }

  /deep/ .header {
    height: 84px;
    background: linear-gradient(0deg, #073446, #00aae2);
    border-radius: 50px 50px 0 0;
    padding: 0 33px 0 55px;
  }

  /deep/ .header::before {
    content: none;
  }

  /deep/ .header::after {
    content: none;
  }

  /deep/ .title {
    line-height: normal;
    font-size: 48px;
    font-family: Source Han Sans CN;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0px 4px 16px rgba(0, 0, 0, 0.6);
    letter-spacing: 2px;
  }

  /deep/ .closeBtn {
    width: 33px;
    height: 33px;
    color: #ffffff;
    font-size: 33px;
  }

  /deep/ .body {
    flex: 1;
  }

  /deep/ .bodyContent {
    padding: 0;
    width: 100%;
    height: 100%;
  }

  /deep/ .bodyContent > div {
    width: 100%;
    height: 100%;
  }

  /deep/ .body::before {
    content: none;
  }

  /deep/ .bodyWrapp {
    width: 100%;
    height: 100%;
    // background-color: chartreuse;
    display: flex;
    box-sizing: border-box;
    padding: 44px 66px 69px 55px;

    .contentLeft {
      width: 50% !important;
      height: 100% !important;
      // background-color: antiquewhite;
      box-sizing: border-box;
      border-right: 2px solid #056c92;

      .subtitle {
        font-size: 38px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #f1de77;
        background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .lineClass {
        display: flex;
        width: 100%;

        .lineLabelClass {
          font-size: 36px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #f1de77;
          background: linear-gradient(180deg, #ffffff 0%, #cbf2ff 50.244140625%, #ffffff 53.0029296875%, #00c0ff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          width: 100px;
        }

        .lineContentClass {
          // width: 65%;
          font-size: 36px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #d6e7f9;
        }
      }
    }
  }
}
</style>