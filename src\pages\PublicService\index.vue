<template>
  <div class="mainContainer">
    <div class="filterContainer">
      <TimeFilter ref="timeFilter" />
    </div>
    <div class="mainContainerInner">
      <PublicServiceLeft />
      <CenterWrapBox>
        <PublicServiceCenter />
      </CenterWrapBox>
      <PublicServiceRight />
    </div>
  </div>
</template>

<script>
import CenterWrapBox from '@/components/mapComponent/centerWrapBox.vue'
import PublicServiceLeft from '@/pages/PublicService/components/PublicServiceLeft.vue'
import PublicServiceRight from '@/pages/PublicService/components/PublicServiceRight.vue'
import PublicServiceCenter from '@/pages/PublicService/components/PublicServiceCenter.vue'
import TimeFilter from "@/components/TimeFilter.vue"


export default {
  components: {
    CenterWrapBox,
    PublicServiceLeft,
    PublicServiceRight,
    PublicServiceCenter,
    TimeFilter
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.mainContainer {
  position: relative;
  width: 100%;
  height: calc(100% - 230px);
  margin-top: 230px;
  z-index: 999;
  background: url('~@/assets/application/dtBg.png') no-repeat center center;
  background-size: 100% 100%;
  .filterContainer {
    position: absolute;
    top: -3.3%;
    right: 1.5%;
  }

  .mainContainerInner {
    width: 100%;
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: 0;
  }
}
</style>