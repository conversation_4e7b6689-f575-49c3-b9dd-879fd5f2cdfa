<!--
 * @Description:
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-05-14 10:43:50
 * @LastEditors: wjb
 * @LastEditTime: 2024-05-16 09:10:22
-->
<template>
  <div class="gr_main">
    <div class="gr_item" v-for="(item, index) in listData" :key="index">
      <img :src="item.icon" alt="" class="gr_icon" />
      <div class="gr_right">
        <div class="name">{{ item.name }}</div>
        <img src="@/assets/eventTaskCenter/midBottom/dialog/sj_line.png" alt="" class="gr_line" />
        <div class="count_box">
          <div class="count">{{ item.count }}</div>
          <div class="unit">件</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    //显示flag
    flag: {
      type: Boolean,
      default: false,
    },
    listData: {
      type: Array,
      default: () => {
        return []
      },
    }
  },
  data() {
    return {
      // listData: [],
    }
  },
  mounted() {
    // let that = this
    // if (that.title == '任务概况') {
    //   this.listData = [
    //     {
    //       name: '环境保护',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon1.png'),
    //       count: 53,
    //     },
    //     {
    //       name: '经济发展',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon2.png'),
    //       count: 176,
    //     },
    //     {
    //       name: '文化建设',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon3.png'),
    //       count: 56,
    //     },
    //     {
    //       name: '社区治理',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon4.png'),
    //       count: 173,
    //     },
    //     {
    //       name: '城市治理',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon5.png'),
    //       count: 88,
    //     },
    //     {
    //       name: '应急响应',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon6.png'),
    //       count: 163,
    //     },
    //     {
    //       name: '基础设施',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon7.png'),
    //       count: 70,
    //     },
    //     {
    //       name: '卫生健康',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/rw_icon8.png'),
    //       count: 46,
    //     },
    //   ]
    // } else if (that.title == '事件概况') {
    //   this.listData = [
    //     {
    //       name: '公共安全',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon1.png'),
    //       count: 112,
    //     },
    //     {
    //       name: '城市治理',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon2.png'),
    //       count: 78,
    //     },
    //     {
    //       name: '政务服务',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon3.png'),
    //       count: 112,
    //     },
    //     {
    //       name: '环境监测',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon4.png'),
    //       count: 65,
    //     },
    //     {
    //       name: '市政设施',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon5.png'),
    //       count: 45,
    //     },
    //     {
    //       name: '交通管理',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon6.png'),
    //       count: 67,
    //     },
    //     {
    //       name: '应急响应',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon7.png'),
    //       count: 87,
    //     },
    //     {
    //       name: '市场监管',
    //       icon: require('@/assets/eventTaskCenter/midBottom/dialog/sj_icon8.png'),
    //       count: 89,
    //     },
    //   ]
    // }
  },
  watch: {},
  methods: {},
}
</script>

<style lang="less" scoped>
.gr_main {
  padding: 150px 0 50px 120px;
  display: flex;
  flex-wrap: wrap;
  .gr_item {
    margin-right: 128px;
    margin-bottom: 100px;
    display: flex;
    align-content: center;
    align-items: center;
    .gr_icon {
      width: 133px;
      height: 160px;
    }
    .gr_right {
      text-align: center;
      .name {
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 32px;
        color: #dcefff;
        line-height: 32px;
      }
      .gr_line {
        width: 240px;
        height: 26px;
      }
      .count_box {
        margin-top: 24px;
        display: flex;
        align-items: baseline;
        justify-content: center;
        .count {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 56px;
          color: #ffffff;
        }
        .unit {
          font-family: Source Han Sans CN, Source Han Sans CN;
          font-weight: 500;
          font-size: 32px;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
