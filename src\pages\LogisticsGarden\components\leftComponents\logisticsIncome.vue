<template>
  <div>
    <div class="income_chart" id="incomeChart"></div>
  </div>
</template>

<script>
export default {
  name: 'incomeChart',
  data() {
    return {
      charts2Data: [
        {
          name: '24:00',
          value1: 300,
          value2: 140,
        },
        {
          name: '03:00',
          value1: 400,
          value2: 330,
        },
        {
          name: '06:00',
          value1: 380,
          value2: 200,
        },
        {
          name: '09:00',
          value1: 320,
          value2: 150,
        },
        {
          name: '12:00',
          value1: 600,
          value2: 450,
        },
        {
          name: '15:00',
          value1: 440,
          value2: 380,
        },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById('incomeChart'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'horizontal',
          icon: 'rect', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 21, // 设置宽度
          itemHeight: 21, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
          },
          left: '40%',
        },
        grid: {
          left: '8%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.charts2Data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：万元',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '昨日',
            type: 'line',
            barWidth: 30,
            smooth: true,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: '#00C0FE',
              },
            },
            data: this.charts2Data.map((item) => item.value1),
          },
          {
            name: '今日',
            type: 'line',
            barWidth: 30,
            smooth: true,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: '#FCDF1D',
              },
            },
            data: this.charts2Data.map((item) => item.value2),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.income_chart {
  width: 938px;
  height: 600px;
  margin-top: 26px;
}
</style>
