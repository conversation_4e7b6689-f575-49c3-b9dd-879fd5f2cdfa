<template>
  <div class="yyglContainer">
    <Ktitle :titleText="'部门应用占比'" width="937"></Ktitle>
    <div class="pngWrap">
      <div class="pngInnerWrap">
        <div class="innerTop">
          <div v-for="(item,i) in ['饼图','柱状图','折线图']" :key="i" class="tabItem" :class="{active:eventChoose == i}" @click="changeCharts(i)">{{item}}</div>
        </div>
        <div class="innerBot">
          <el-carousel arrow="never" height="100%" indicator-position="none" :autoplay="true" class="zmdClass" ref="elCarousel" @change="changeCarousel">
            <el-carousel-item class="zmdItemClass" name="pie">
              <PieChart ref="pieChart" />
            </el-carousel-item>
            <el-carousel-item class="zmdItemClass" name="histogram">
              <HistogramChart ref="histogramChart" />
            </el-carousel-item>
            <el-carousel-item class="zmdItemClass" name="line">
              <LineChart ref="lineChart" />
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import PieChart from "@/pages/ApplicationIntegrationCenter/applicationCenter/chartsComponents/PieChart.vue"
import HistogramChart from "@/pages/ApplicationIntegrationCenter/applicationCenter/chartsComponents/HistogramChart.vue"
import LineChart from "@/pages/ApplicationIntegrationCenter/applicationCenter/chartsComponents/LineChart.vue"
export default {
  components: {
    Ktitle,
    PieChart,
    HistogramChart,
    LineChart
  },
  data () {
    return {
      eventChoose: 0
    }
  },
  methods: {
    changeCharts (index) {
      switch (index) {
        case 0:
          this.$refs.elCarousel.setActiveItem('pie')
          break
        case 1:
          this.$refs.elCarousel.setActiveItem('histogram')
          break
        case 2:
          this.$refs.elCarousel.setActiveItem('line')
          break
      }
      this.eventChoose = index
    },
    changeCarousel (e) {
      this.eventChoose = e
    }
  }
}
</script>

<style scoped lang="less">
.yyglContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .pngWrap {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 85px 0px 0 0px;
    .pngInnerWrap {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // background-color: blue;
      .innerTop {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 82px;
        .tabItem {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 256px;
          height: 71px;
          font-size: 32px;
          font-family: Source Han Sans SC;
          font-weight: 400;
          color: #d6e7f9;
          background: url('~@/assets/home/<USER>') no-repeat center center;
          background-size: 100% 100%;
          margin-right: 28px;
        }
        .active {
          background: url('@/assets/home/<USER>') no-repeat;
        }
      }
      .innerBot {
        flex: 1;
        width: 100%;
        // background-color: aqua;
        .zmdClass {
          width: 100%;
          height: 100%;
          // background-color: antiquewhite;
          .zmdItemClass {
            width: 100%;
            height: 100%;
            // background-color: chartreuse;
          }
        }
      }
    }
  }
}
</style>