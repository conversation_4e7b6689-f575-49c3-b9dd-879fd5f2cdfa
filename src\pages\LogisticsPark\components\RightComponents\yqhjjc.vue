<template>
  <div class="container">
    <Ktitle :titleText="'园区环境监测'" width="1935.6" :show-date="false">
      <el-select v-model="value" placeholder="请选择" @change="change">
        <el-option
          v-for="(item,i) in options"
          :key="i"
          :label="item.name"
          :value="item.name">
        </el-option>
      </el-select>
    </Ktitle>
    <div class="containerList">
      <div class="containerItem" v-for="(item,i) in indexList" :key="i">
        <div class="top">
          <div class="icon" :style="{background: 'url('+item.icon+') no-repeat'}"></div>
          <div class="value">{{item.value}}</div>
          <div class="unit">{{item.unit}}</div>
        </div>
        <div class="bottom">{{item.name}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
export default {
  name: "yqhjjc",
  components: {
    Ktitle
  },
  data() {
    return {
      value:"中南高科金西创业园",
      options:[
        {"name":"中南高科金西创业园","PM2.5":34,"Co":1.76,"So2":57,"园区噪音情况":25},
        {"name":"中盈小微园","PM2.5":35,"Co":1.36,"So2":55,"园区噪音情况":33},
        {"name":"金华中盈科技发展有限公司","PM2.5":28,"Co":2.38,"So2":38,"园区噪音情况":41},
        {"name":"金华市新嘉智能科技有限公司","PM2.5":29,"Co":2.75,"So2":59,"园区噪音情况":34},
        {"name":"浙江菁英电商管理服务有限公司","PM2.5":31,"Co":2.45,"So2":39,"园区噪音情况":49},
        {"name":"金华市智缘物业服务有限公司","PM2.5":33,"Co":2.4,"So2":34,"园区噪音情况":28},
        {"name":"浙江北大科技园有限公司","PM2.5":37,"Co":2.73,"So2":25,"园区噪音情况":28},
        {"name":"金华科技园创业服务中心有限公司","PM2.5":39,"Co":2.64,"So2":54,"园区噪音情况":22},
        {"name":"浙江菁英电子商务产业园","PM2.5":41,"Co":1.48,"So2":39,"园区噪音情况":52},
        {"name":"物联科技大厦","PM2.5":30,"Co":2.08,"So2":25,"园区噪音情况":46},
        {"name":"浙中仙华科创园","PM2.5":29,"Co":1.73,"So2":58,"园区噪音情况":36},
        {"name":"金开金西智造园","PM2.5":26,"Co":2.09,"So2":41,"园区噪音情况":53},
        {"name":"金华之心","PM2.5":26,"Co":1.67,"So2":55,"园区噪音情况":47},
        {"name":"中谷文化产业园","PM2.5":31,"Co":1.46,"So2":24,"园区噪音情况":32},
        {"name":"金西机电科技产业园","PM2.5":34,"Co":2.29,"So2":45,"园区噪音情况":28},
        {"name":"金开现代智造园-金华中科孵化器有限公司","PM2.5":32,"Co":1.47,"So2":41,"园区噪音情况":44},
        {"name":"浙中网络经济中心","PM2.5":29,"Co":1.55,"So2":29,"园区噪音情况":22},
        {"name":"模具城","PM2.5":33,"Co":1.98,"So2":57,"园区噪音情况":20},
        {"name":"浙中仙华科创园","PM2.5":37,"Co":1.98,"So2":41,"园区噪音情况":50},
        {"name":"金华市新嘉智创园","PM2.5":23,"Co":2.18,"So2":59,"园区噪音情况":48},
        {"name":"金华北大科技园（二期）（未名科创大厦）","PM2.5":27,"Co":2.81,"So2":36,"园区噪音情况":25},
        {"name":"美保龙科技园","PM2.5":35,"Co":1.38,"So2":33,"园区噪音情况":22},
        {"name":"晴天科技园","PM2.5":41,"Co":2.51,"So2":59,"园区噪音情况":57},
        {"name":"金华亚泰小微园","PM2.5":26,"Co":2.1,"So2":45,"园区噪音情况":24},
        {"name":"金华置信产业园","PM2.5":29,"Co":2.76,"So2":55,"园区噪音情况":41},
      ],
      indexList: [
        {
          icon:require("@/assets/Logistics/空气质量.png"),
          name:"PM2.5",
          value:"34",
          unit:"μg/m³"
        },
        {
          icon:require("@/assets/Logistics/一氧化碳.png"),
          name:"Co",
          value:"3",
          unit:"mg/m³"
        },
        {
          icon:require("@/assets/Logistics/单体企业-二氧化硫排放量.png"),
          name:"So2",
          value:"1",
          unit:"μg/m³"
        },
        {
          icon:require("@/assets/Logistics/噪音.png"),
          name:"园区噪音情况",
          value:"87",
          unit:"分贝"
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.change()
  },
  methods: {
    change() {
      this.indexList.forEach((item,i) => {
        let matchItem = this.options.filter(obj => obj.name == this.value)
        item.value = matchItem[0][item.name]
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .containerList {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin: 25px 0 54px 0;
      .containerItem {
        width: 400px;
        height: 163px;
        background: url("@/assets/Logistics/enviromentBg.png") no-repeat;
        background-size: cover;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: center;
        .top {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .icon {
            width: 56px;
            height: 53px;
            background-size: cover;
          }
          .value {
            font-family: Source Han Sans CN;
            font-weight: bold;
            font-size: 60px;
            color: #9AA9BF;
            background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .unit {
            font-family: Source Han Sans CN;
            font-size: 60px;
            color: #9AA9BF;
            background: linear-gradient(180deg, #FFFFFF 0%, #22E8E8 50.244140625%, #FFFFFF 53.0029296875%, #CAFFFF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .bottom {
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 36px;
          color: #E3EDFF;
        }
      }
    }
  }
</style>