<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 09:45:26
 * @LastEditors: wjb
 * @LastEditTime: 2025-04-14 10:03:48
-->
<template>
  <div class="container">
    <zhtxTable :tableData="tableData" :title-with-key="titleList" style="margin-top: 20px;margin-left:24px;" :height="760"></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from '@/components/zhtxTable'
import { getJscYczb } from '@/api/IndicatorCenter'
export default {
  name: 'ccqkfxRight',
  data() {
    return {
      tableData: [],
      titleList: [
        {
          label: '统计截止时间',
          key: 'zbTime',
          flex: 1,
        },
        {
          label: '指标名称',
          key: 'indicatorName',
          flex: 3,
        },
        {
          label: '指标值',
          key: 'indicatorValue',
          flex: 1,
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable,
  },
  computed: {},
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      getJscYczb().then((res) => {
        this.tableData = res.data.data
      })
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
</style>