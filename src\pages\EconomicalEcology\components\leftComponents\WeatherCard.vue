<template>
  <div :class="{ 'cardBox': !hasBottom, 'cardBoxTwo': hasBottom }">
    <div class="topTxtWrap">
      <img :src="imgSrc" class="imgClass">
      <div :class="{ 'txtClass': true, 'txtColorOne': isRed, 'txtColorTwo': !isRed }">
        <div class="numClass">{{ num }}</div>
        <div class="unitClass">{{ unit }}</div>
      </div>
    </div>
    <div class="boTxtWrap">{{ text }}</div>
  </div>
</template>

<script>
export default {
  props: {
    hasBottom: {
      type: Boolean,
      default: false
    },
    imgSrc: {
      type: String,
      default: ''
    },
    isRed: {
      type: Boolean,
      default: false
    },
    num: {
      type: String,
      default: ''
    },
    unit: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
  },
  components: {
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.cardBox {
  width: 348px;
  height: 183px;
  background: url('~@/assets/economical/sthjbgone.png') no-repeat center center;
  background-size: 100% 100%;
  padding: 24px 0 0 20px;
  box-sizing: border-box;
}

.cardBoxTwo {
  width: 348px;
  height: 203px;
  background: url('~@/assets/economical/sthjbgtwo.png') no-repeat center center;
  background-size: 100% 100%;
  padding: 24px 0 0 20px;
  box-sizing: border-box;
}

.topTxtWrap {
  display: flex;
  align-items: center;

  .imgClass {
    width: 55px;
    height: 55px;
    margin-right: 24px;
  }

  .txtClass {
    display: flex;
    align-items: baseline;
    font-weight: bolder;

    .numClass {
      font-size: 60px;
      margin-right: 12px;
    }

    .unitClass {
      font-size: 32px;
    }
  }

  .txtColorOne {
    background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFFFFF 53.0029296875%, #FFC460 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .txtColorTwo {
    background: linear-gradient(180deg, #FFFFFF 0%, #CAFFFF 50.244140625%, #FFFFFF 53.0029296875%, #22E8E8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.boTxtWrap {
  font-size: 36px;
  font-family: Source Han Sans CN;
  letter-spacing: 2px;
  color: #E3EDFF;
  margin-left: 79px;
}
</style>