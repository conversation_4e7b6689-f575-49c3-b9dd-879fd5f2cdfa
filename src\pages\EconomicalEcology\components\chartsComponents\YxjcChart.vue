<template>
  <div id="yxjcChart"></div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      let myChart = this.$echarts.init(document.getElementById("yxjcChart"))
      var colors = {
        type: 'linear',
        x: 0, x2: 1, y: 0, y2: 0,
        colorStops: [{
          offset: 0,
          color: '#FFD461'
        }, {
          offset: 0.5,
          color: '#FFD461'
        }, {
          offset: 0.5,
          color: '#A68728'
        }, {
          offset: 1,
          color: '#A68728'
        }]
      }


      var barWidth = 60

      let option = {
        grid: {
          top: "20%",
          left: "10%",
          right: "0%",
          bottom: "10%",
          // containLabel: true,
        },
        legend: {
          show: true,
          icon: "circle",
          right: '5%',
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: '28'
          }
        },
        xAxis: {
          data: ['苏孟乡', '秋滨街道', '三江街道', '西关街道', '江南街道', '汤溪镇', '罗埠镇','洋埠镇'],
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#77B3F1'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          type: 'category',
          axisLabel: {
            textStyle: {
              color: '#C5DFFB',
              fontWeight: 500,
              fontSize: '28'
            }
          },
          axisTick: {
            textStyle: {
              color: '#fff',
              fontSize: '28'
            },
            show: false,
          },
          splitLine: { show: false }
        },
        yAxis: {
          name: "单位：亿元",
          type: 'value',
          nameTextStyle: {
            fontSize: 28,
            color: "#D6E7F9",
            padding: [10, 0, 20, 10],
          },
          //坐标轴
          axisLine: {
            show: true,
            lineStyle: {
              width: 1,
              color: '#214776'
            },
            textStyle: {
              color: '#fff',
              fontSize: '28'
            }
          },
          axisTick: {
            show: false
          },
          //坐标值标注
          axisLabel: {
            show: true,
            textStyle: {
              color: '#C5DFFB',
              fontSize: '28'
            }
          },
          //分格线
          splitLine: {
            lineStyle: {
              color: '#13365f',
              fontSize: '28'
            }
          }
        },
        series: [
          {
            z: 1,
            type: 'bar',
            barWidth: barWidth,
            data: [220, 182, 191, 234, 290, 330, 310, 310],
            itemStyle: {
              normal: {
                color: colors
              }
            },
          }, {
            z: 2,
            name: '限上社会消费品零售总额',
            type: 'pictorialBar',
            data: [1, 1, 1, 1, 1, 1, 1],
            symbol: 'diamond',
            symbolOffset: [0, '50%'],
            symbolSize: [barWidth, 10],
            itemStyle: {
              normal: {
                color: colors
              }
            },
          }, {
            z: 3,
            // name: '上部1',
            type: 'pictorialBar',
            symbolPosition: 'end',
            data: [220, 182, 191, 234, 290, 330, 310],
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [barWidth - 4, 10 * (barWidth - 4) / barWidth],
            itemStyle: {
              normal: {
                borderColor: '#FFE7A8',
                borderWidth: 2,
                color: '#FFE7A8'
              }
            },
          }
        ]
      }

      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
#yxjcChart {
  width: 100%;
  height: 100%;
}
</style>