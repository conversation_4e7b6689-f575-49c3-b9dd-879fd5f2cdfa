<!--办件-->
<template>
  <div class="handContainer">
    <div class="hand-item" v-for="(item,i) in list" :key="i">
      <img :src="item.img" alt="">
      <div class="info">
        <div class="name">{{item.name}}</div>
        <div class="value blue"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "handling",
  data() {
    return {
      list:[
        {
          value: 26979,
          name: "“一件事”办件总量",
          unit:"件",
          img:require("@/assets/partyBuilding/hand1.png")
        },
        {
          value: 239,
          name: "公务员当月办件数量",
          unit:"件",
          img:require("@/assets/partyBuilding/hand2.png")
        },
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .handContainer {
    width: 1851px;
    height: 279px;
    background-size: cover;
    background: url("@/assets/partyBuilding/背景框.png");
    margin: 139px 0 0 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .hand-item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 770px;
      margin-left: 155px;
      img {
        width: 134px;
        height: 115px;
      }
      .info {
        margin-left: 42px;
        .name {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #FFFFFF;
        }
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          margin-top: 13px;
        }
        .unit {
          font-size: 35px;
        }
      }
    }
  }
</style>