<!--干部指标-->
<template>
  <div class="cadre">
    <div class="cadre-item" v-for="(item,i) in list" :key="i">
      <img :src="item.img" alt="">
      <div class="name">{{item.name}}</div>
      <div style="display: flex;justify-content: space-evenly;align-items: center;margin-top: 22px">
        <div class="value blue">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
        <div class="value blue" v-if="item.percent">{{item.percent}} <span class="unit">%</span> </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "cadre",
  data() {
    return {
      list:[
        {
          name: "区管女干部人数",
          value: 43,
          unit:"人",
          percent:29,
          img:require("@/assets/partyBuilding/干部1.png")
        },
        {
          name: "区管35岁以下干部人数",
          value: 13,
          unit:"人",
          percent:9,
          img:require("@/assets/partyBuilding/干部2.png")
        },
        {
          name: "区管研究生干部人数",
          value: 3,
          unit:"人",
          img:require("@/assets/partyBuilding/干部3.png")
        },
        {
          name: "区管干部平均年龄",
          value: 45,
          unit:"岁",
          img:require("@/assets/partyBuilding/干部4.png")
        },
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .cadre {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 151px;
    .cadre-item {
      width: 288px;
      height: 467px;
      img {
        width: 288px;
        height: 288px;
      }
      .name {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
        text-align: center;
        margin-top: 31px;
        white-space: nowrap;
      }
      .value {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
      }
      .unit {
        font-size: 35px;
      }
    }
  }
</style>