<template>
  <div class="wireWrap">
    <div :class="{'horizontaline':isLeft,'verticallineThree':!isLeft}"></div>
    <div :class="{'verticallineOne':isLeft,'verticallineTwo':!isLeft}"></div>
    <div :class="{'verticallineTwo':isLeft,'verticallineOne':!isLeft}"></div>
    <div :class="{'verticallineThree':isLeft,'horizontalineTwo':!isLeft}"></div>
  </div>
</template>

<script>
export default {
  props: {
    direction: {
      type: String,
      default: 'left',
    }
  },
  computed: {
    isLeft () {
      return this.direction == 'left' ? true : false
    }
  }

}
</script>

<style lang="less" scoped>
.wireWrap {
  display: flex;
  align-items: center;
  width: 100%;
  .horizontaline {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, #517796 0%, #00c0ff 100%);
  }
  .horizontalineTwo {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, #00c0ff 0%, #517796 100%);
  }
  .verticallineOne {
    width: 2px;
    height: 14px;
    background: #00c0ff;
  }
  .verticallineTwo {
    width: 2px;
    height: 36px;
    background: #00c0ff;
    margin-left: 6px;
  }
  .verticallineThree {
    width: 2px;
    height: 50px;
    background: #00c0ff;
    margin-left: 6px;
  }
}
</style>