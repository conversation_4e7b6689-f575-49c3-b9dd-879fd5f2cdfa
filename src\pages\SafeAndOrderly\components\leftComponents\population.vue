<!--城市人口 和 常住人口趋势-->
<template>
  <div class="population">
    <city></city>
    <PermanentResidence></PermanentResidence>
  </div>
</template>

<script>
import city from "@/pages/SafeAndOrderly/components/leftComponents/populations/city";
import PermanentResidence from "@/pages/SafeAndOrderly/components/leftComponents/populations/PermanentResidence";
export default {
  name: "population",
  data() {
    return {}
  },
  components:{
    city,
    PermanentResidence
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .population {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
  }
</style>