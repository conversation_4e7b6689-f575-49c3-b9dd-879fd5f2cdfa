<template>
  <div>
    <pie3D :id="'personComprisePieCharts'" :type="true" :options="chartsData" :internalDiameterRatio="1.8" :scaleCustom="1.5" :legendCustom="legend"></pie3D>
  </div>
</template>

<script>
import pie3D from "@/components/pie3D";
export default {
  name: "personComprisePieCharts",
  data() {
    return {
      chartsData:[
        {
          name:"人员类型一",
          value:176
        },
        {
          name:"人员类型二",
          value:186
        },
        {
          name:"人员类型三",
          value:165
        },
        {
          name:"人员类型四",
          value:186
        },
      ],
      legend:{
        itemWidth: 30,
        itemHeight: 15,
        itemGap: 30,
        left: '55%',
        top: '27%',
      }
    }
  },
  components:{
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>