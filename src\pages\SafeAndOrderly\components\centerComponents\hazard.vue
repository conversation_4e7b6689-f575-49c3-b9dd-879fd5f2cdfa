<!--城市危险源-->
<template>
  <div class="hazardList">
    <div class="hazard-item" v-for="(item,i) in hazards" :key="i">
      <div class="info">
        <img :src="item.icon" alt="">
        <div class="info-list">
          <div class="name">{{item.name}}</div>
          <div class="value red"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
        </div>
      </div>
      <div class="progress">
        <div class="schedule" :style="{width:item.percent * 5.12 + 'px'}"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "hazard",
  data() {
    return {
      hazards: [
        {
          name:"最大危险源",
          value:4,
          unit:"个",
          icon:require("@/assets/safe/城市危险源1.png"),
          percent:55
        },
        {
          name:"火点监控",
          value:20,
          unit:"个",
          icon:require("@/assets/safe/城市危险源2.png"),
          percent:55
        },
        {
          name:"重大危险源当日预警数",
          value:0,
          unit:"个",
          icon:require("@/assets/safe/城市危险源3.png"),
          percent:55
        },
        {
          name:"危险化学品数量",
          value:1236,
          unit:"个",
          icon:require("@/assets/safe/城市危险源4.png"),
          percent:55
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .hazardList {
    width: 100%;
    height: 284px;
    margin-top: 52px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .hazard-item {
      width: 604px;
      height: 284px;
      background: url("@/assets/safe/hazardBackground.png");
      background-size: cover;
      .info {
        width: 495px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 40px 0 0 55px;
        img {
          width: 105px;
          height: 143px;
        }
        .info-list {
          display: flex;
          justify-content: flex-end;
          align-items: flex-end;
          flex-direction: column;
          .name {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            white-space: nowrap;
          }
          .value {
            font-size: 60px;
            font-family: BN;
            font-weight: 400;
          }
          .unit {
            font-size: 25px;
            white-space: nowrap;
            margin-left: 5px;
          }
        }
      }
      .progress {
        z-index: 9;
        width: 512px;
        height: 7px;
        border-radius: 100px;
        background: #2F4157;
        margin: 32px 0 0 56px;
        .schedule {
          height: 9px;
          border-radius: 100px;
          z-index: 10;
          background: linear-gradient(270deg, #FFECCB, #FFE4A8, #FFC460);
        }
      }
    }
  }
</style>