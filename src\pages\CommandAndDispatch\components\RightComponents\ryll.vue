<template>
  <div class="container">
    <Ktitle width="939.6" :titleText="'人员力量'" :show-date="false">
      <div class="setImg" @click="popShow = !popShow"></div>
    </Ktitle>
    <div class="container-content">
      <div class="scroll-content">
        <div class="container-content-item" v-for="(item,i) in peopleList" :key="i">
          <div class="container-content-item-left" @click="pointOnMap(item)"></div>
          <div class="container-content-item-right" @click="showDetail(item)">
            <div class="text">{{item.name}}</div>
            <div class="value">{{item.value}} <div class="unit">人</div></div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel">
      <resourceAllocation :list="List" @change="listChange" v-show="popShow" labelKey="name" />
    </div>
    <Commondialog :title="dialogTitle" :dialogFlag="showDialog" @close="showDialog = false" dialogWidth="1700px">
      <CommonDialogTable :show-index="true" :title-with-key="titleList" :table-data="tableData" :width="1600" :height="730"></CommonDialogTable>
    </Commondialog>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import resourceAllocation from "@/components/ResourceAllocation";
import Commondialog from "@/components/Commondialog";
import CommonDialogTable from "@/components/CommonDialogTable";
import {getRyllList, getRyllTj} from "@/api/command";
export default {
  name: "ryll",
  data() {
    return {
      List: [],
      peopleList: [],
      popShow: false,
      dialogTitle:"",
      showDialog:false,
      tableData: [],
      titleList:[],
    }
  },
  components: {
    Ktitle,
    resourceAllocation,
    Commondialog,
    CommonDialogTable
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getRyllTj().then(res => {
        this.List = res.data.data.map(item => ({
          name: item.name?item.name:"",
          value: item.num?item.num:"/",
          checked: true
        }))
      })
    },
    listChange(res) {
      this.peopleList = res;
    },
    showDetail(item) {
      this.dialogTitle = item.name + "详情"
      this.getTableKeyList(item.name)
      getRyllList({pageSize: 1000,pageNum: 1,type: item.name}).then(res => {
        this.tableData = res.data.rows
        this.tableData.forEach((obj,i) => {obj.index = i + 1})
        this.showDialog = true
      })
    },
    //点位上图
    pointOnMap(item) {
      const that = this;
      if (top.mapUtil.layers[item.name + "MarkerArray"]) {
        top.mapUtil.removeLayer(item.name + "MarkerArray")
      } else {
        getRyllList({pageSize: 1000,pageNum: 1,type: item.name}).then(res => {
          let pointData = res.data.rows.map(obj => ({
            type: "ryll",
            lng:obj.lon.trim(),
            lat:obj.lat.trim(),
            name: obj.type?obj.type:"-",
            town: obj.town?obj.town:"-"
          }))
          this.$EventBus.$emit('CommonDrawPoint', pointData, item.name + "MarkerArray", that.getPointIcon(item.name), true, false, true);
        })
      }
    },
    //获取不同类型点位图标
    getPointIcon(type) {
      switch (type) {
        case "医疗卫生":
          return "/map/img/医疗卫生.png"
        case "消防救援队伍":
          return "/map/img/消防救援.png"
        case "应急专家":
          return "/map/img/应急专家.png"
        case "应急救援队伍":
          return "/map/img/应急救援.png"
      }
    },
    getTableKeyList(type) {
      switch (type) {
        case "医疗卫生":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"名称",
              key:"name",
              flex: 3
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 2.2
            },
            {
              label:"地址",
              key:"address",
              flex: 4
            },
            {
              label:"队长姓名",
              key:"dzName",
              flex: 1.5
            },
            {
              label:"队长联系方式",
              key:"dzPhone",
              flex: 2
            },
            {
              label:"队员人数",
              key:"dyNum",
              flex: 2
            }
          ]
          break;
        case "消防救援队伍":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"名称",
              key:"name",
              flex: 3
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 2
            },
            {
              label:"地址",
              key:"address",
              flex: 4
            },
            {
              label:"队长姓名",
              key:"dzName",
              flex: 1.5
            },
            {
              label:"队长联系方式",
              key:"dzPhone",
              flex: 2
            }
          ]
          break;
        case "应急专家":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"姓名",
              key:"name",
              flex: 1
            },
            {
              label:"所在单位",
              key:"unit",
              flex: 3
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 1.8
            },
            {
              label:"单位地址",
              key:"address",
              flex: 3
            },
            {
              label:"联系方式",
              key:"phone",
              flex: 2
            }
          ]
          break;
        case "应急救援队伍":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"名称",
              key:"name",
              flex: 3.2
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 1.8
            },
            {
              label:"地址",
              key:"address",
              flex: 3
            },
            {
              label:"队长姓名",
              key:"dzName",
              flex: 1
            },
            {
              label:"队长联系方式",
              key:"dzPhone",
              flex: 2
            }
          ]
          break;
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    ::-webkit-scrollbar-thumb {
      background-color: #1C92D9;
      -webkit-border-radius: 6px;
      border-radius: 6px;
    }
    ::-webkit-scrollbar {
      width: 14px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 6px;
      background-color: #10213E;
    }
    .setImg {
      width: 68px;
      height: 67px;
      background: url("~@/assets/Command/peizhi.png");
      background-size: cover;
      cursor: pointer;
    }
    .panel {
      position: fixed;
      top: 135px;
      left: 749px;
    }
    .container-content {
      width: 939.6px;
      height: 370px;
      overflow-x: scroll;
      .scroll-content {
        width: fit-content;
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-wrap: wrap;
        flex-direction: column;
      }
    }
    .container-content-item {
      width: fit-content;
      height: 101px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-top: 50px;
      cursor: pointer;
      .container-content-item-left {
        width: 120px;
        height: 101px;
        background: url("~@/assets/Command/ryllBg.png") no-repeat;
        background-size: cover;
        margin-right: 10px;
      }
      .container-content-item-right {
        width: 359px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        .text {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 32px;
          color: #FFFFFF;
        }
        .value {
          font-family: BN;
          font-weight: 400;
          font-size: 60px;
          color: #9AA9BF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .unit {
            font-size: 30px;
            margin: 7px 0 0 10px;
          }
        }

      }
    }
  }
</style>