<!--旅游服务-->
<template>
  <div class="rightFive">
    <Ktitle titleText="旅游服务" :width="'950'" />
    <div class="lyfwWrap">
      <div class="lyfwTop">
        <div class="tyWrap">
          <div class="gdzkBoxInner">
            <img :src="require('@/assets/publicService/rs.png')" class="imgClass">
            <ToText :lineOneType="3" :text="'本年累计接待游客人数'" :num="'96.3'" :unit="'万人'" />
          </div>
        </div>
        <div class="tyWrap">
          <div class="gdzkBoxInner">
            <img :src="require('@/assets/publicService/jqsl.png')" class="imgClass">
            <ToText :lineOneType="3" :text="'国家3A级以上景区数量'" :num="'3'" :unit="'个'" />
          </div>
        </div>
        <div class="tyWrap">
          <div class="gdzkBoxInner">
            <img :src="require('@/assets/publicService/sr.png')" class="imgClass">
            <ToText :lineOneType="3" :text="'本年累计接待游客收入'" :num="'16.1'" :unit="'亿元'" />
          </div>
        </div>
        <div class="tyWrap">
          <div class="gdzkBoxInner">
            <img :src="require('@/assets/publicService/lssl.png')" class="imgClass">
            <ToText :lineOneType="3" :text="'旅行社数量'" :num="'46'" :unit="'个'" />
          </div>
        </div>
      </div>
      <LyfwChart />
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
import ToText from "@/components/ToText.vue"
import LyfwChart from "@/pages/PublicService/components/chartsComponents/LyfwChart.vue"
export default {
  name: "lyfw",
  components:{
    Ktitle,
    ToText,
    LyfwChart
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .rightFive {
    width: 940px;
    height: fit-content;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 30px;

  // background-color: bisque;
  .lyfwWrap {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    box-sizing: border-box;

    .lyfwTop {
      width: 100%;
      height: 383px;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;

      .tyWrap {
        width: 100%;
        height: 100%;
        // background-color: antiquewhite;
        display: flex;
        // justify-content: center;
        align-items: center;
      }

      .gdzkBoxInner {
        display: flex;
        align-items: center;

        .imgClass {
          width: 116px;
          height: 109px;
        }
      }
    }
  }
}
</style>