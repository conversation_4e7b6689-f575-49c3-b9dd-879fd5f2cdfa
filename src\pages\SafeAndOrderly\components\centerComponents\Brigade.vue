<!--消防队伍-->
<template>
  <div>
    <titleTwo :title-text="'消防队伍'"></titleTwo>
    <div class="box">
      <div class="item" v-for="(item,i) in list" :key="i">
        <img :src="item.img" alt="">
        <div class="info">
          <div class="name">{{item.name}}</div>
          <div class="value blue"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "Brigade",
  data() {
    return {
      list:[
        {
          name:"消防救援站",
          value:2,
          unit:"个",
          img:require("@/assets/safe/消防队伍1.png")
        },
        {
          name:"消防队伍",
          value:14,
          unit:"支",
          img:require("@/assets/safe/消防队伍2.png")
        },
        {
          name:"消防人员",
          value:261,
          unit:"人",
          img:require("@/assets/safe/消防队伍3.png")
        },
        {
          name:"消防微站",
          value:196,
          unit:"站",
          img:require("@/assets/safe/消防队伍4.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .box {
    width: 935px;
    height: 460px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    .item {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      img {
        width: 152px;
        height: 141px;
      }
      .info {
        width: 200px;
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        flex-direction: column;
        margin-left: 32px;
        .name {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
          white-space: nowrap;
        }
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
        }
        .unit {
          font-size: 25px;
          white-space: nowrap;
          margin-left: 5px;
        }
      }
    }
  }
</style>