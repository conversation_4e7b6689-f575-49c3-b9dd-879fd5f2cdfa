<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 09:35:29
 * @LastEditors: wjb
 * @LastEditTime: 2025-05-27 08:53:34
-->
<!--生态环境-->
<template>
  <div>
    <div class="map-con">
      <div id="viewDiv"></div>
      <!-- <div v-if="!isMap1" ref="myEchart" class="map-echart" id="ecartDiv" style="display: block; z-index: 9"></div> -->
      <yj-dialog v-if="currentMapType === '应急资源'" class="yj-dialog"/>
      <div class="map-btn">
        <li v-for="(item,index) in mapBtnData" :key="index" :class="currentMapType==item?'active':''"
          :style="{cursor:isMap?'pointer':'not-allowed'}" @click="clickMapBtn(item)">{{item}}</li>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface, getCsdnInterface2 } from '@/api/csdnIndexApi'
import view$ from 'dingtalk-jsapi/api/biz/map/view'
import YjDialog from './YjDialog.vue'
export default {
  name: 'leftMap',
  components: {
    YjDialog
  },
  data() {
    return {
      //异常指标类型分布
      yczblxfbData: [
        {
          name: '美好生活',
          value: 1,
        },
        {
          name: '宜居宜游',
          value: 0,
        },
        {
          name: '营商环境',
          value: 1,
        },
        {
          name: '城市安全',
          value: 1,
        },
      ],
      time1: null,
      dom1: null,
      tdData: [],
      // mapBtnData: ['人口热力', '道路拥堵', '应急资源', '预警事件'],
      mapBtnData: ['人口热力', '道路拥堵'],
      currentMapType: '',
      mapArr: ['婺城区', '金义新区', '兰溪市', '东阳市', '义乌市', '永康市', '浦江县', '武义县', '磐安县', '开发区'],
      wgDataUrlEnum2: {
        婺城区: '/static/data/grid/wcq.json',
        金义新区: '/static/data/grid/jdq.json',
        东阳市: '/static/data/grid/dys.json',
        义乌市: '/static/data/grid/yws.json',
        永康市: '/static/data/grid/yks.json',
        兰溪市: '/static/data/grid/lxs.json',
        浦江县: '/static/data/grid/pjx.json',
        武义县: '/static/data/grid/wyx.json',
        磐安县: '/static/data/grid/pax.json',
        开发区: '/static/data/grid/kfq.json',
      },
      status: '待确认',
      myChartMap: null,
      popData: [
        {
          name: '婺城区',
          value: [119.4519, 28.8376, 96.275],
        },
        {
          name: '开发区',
          value: [119.3486, 29.0306, 96.35],
        },
        // {
        //   name: '开发区',
        //   value: [],
        // },
        {
          name: '金东区',
          value: [119.809, 29.2252, 95.925],
        },
        {
          name: '武义县',
          value: [119.6326, 28.6567, 95.375],
        },
        {
          name: '浦江县',
          value: [119.8832, 29.5985, 95.125],
        },
        {
          name: '磐安县',
          value: [120.5155, 28.9847, 94.425],
        },
        {
          name: '兰溪市',
          value: [119.3303, 29.2411, 95.3],
        },
        {
          name: '义乌市',
          value: [120.1009, 29.3648, 96.3],
        },
        {
          name: '东阳市',
          value: [120.3104, 29.156, 96.05],
        },
        {
          name: '永康市',
          value: [120.07191733802836, 28.860051888118883, 96.5],
        },
      ],
      tootipObj: {
        婺城区: {
          mhsh: '97',
          yjyy: '96.7',
          yshj: '94.9',
          csaq: '96.5',
        },
        东阳市: {
          mhsh: '96.3',
          yjyy: '96.2',
          yshj: '95.5',
          csaq: '96.2',
        },
        金东区: {
          mhsh: '96.8',
          yjyy: '96.2',
          yshj: '94',
          csaq: '96.7',
        },
        兰溪市: {
          mhsh: '96.8',
          yjyy: '96.6',
          yshj: '91',
          csaq: '96.8',
        },
        浦江县: {
          mhsh: '96.8',
          yjyy: '96.4',
          yshj: '90.6',
          csaq: '96.7',
        },
        义乌市: {
          mhsh: '96.6',
          yjyy: '96.1',
          yshj: '96.2',
          csaq: '96.3',
        },
        永康市: {
          mhsh: '96.7',
          yjyy: '96.8',
          yshj: '95.9',
          csaq: '96.6',
        },
        武义县: {
          mhsh: '96.5',
          yjyy: '96.6',
          yshj: '91.8',
          csaq: '96.6',
        },
        磐安县: {
          mhsh: '96.7',
          yjyy: '96.2',
          yshj: '88',
          csaq: '96.8',
        },
        开发区: {
          mhsh: '96.6',
          yjyy: '96.2',
          yshj: '95.9',
          csaq: '96.7',
        },
      },
      currentNodeName: '',
      currentNodeIndex: null,
      isMap: false,
      isMap1: false,
    }
  },
  computed: {},
  mounted() {
    this.initMap()
    // this.getChart01('chart01', this.yczblxfbData)
    // 表格滚动
    // this.dom1 = document.getElementById('box0')
    // this.mouseleaveEvent1()
    // this.getGeoJon()
    // // top.emiter && top.emiter.on('qxData', (res) => {
    // //   this.echartsMapClick(res)
    // // })
    // this.initApi('开发区')
  },
  methods: {
    initMap() {
      let opts_rk = {
        x: 119.50820897951105,
        y: 29.057243711279295,
        z: 50688.33974887617,
        heading: 348.4893178835531,
        tilt: 0.4938159010161647,
        // x: 119.64453432575661,
        // y: 29.072854271640995,
        // z: 607.68468163535,
        // heading: 350.09674854729406,
        // tilt: 62.3145678030856,
        basemap: 'vector',
        ground: {
          opacity: 1, // 设置地图背景
          surfaceColor: '#2d4a67',
        },
        onload: () => {
          console.log('地图加载完成！')
        },
      }
      mapUtilZBZX.initMap(opts_rk)
      this.isMap = true
      viewZBZX.when(()=>{
        this.loadMask()
        this.clickMapBtn('人口热力')
      })
    },
    loadMask() {
      axios({
        method: "get",
        url: "/map/kfq.json",
      }).then((res) => {
        console.log("hhh",res.data)
        mapUtilZBZX.mask({
          layerid: "mask",
          data: res.data.data, // res 是要素集合
          style: {
            strokeWidth: 1.5,
            strokeColor: [29, 252, 254, 1], //多边形轮廓颜色透明度
            fillColor: [29, 40, 73, 0.9], //多边形填充色
            height: 50,
          },
          onclick: null,
        });
      })
    },
    initApi(value) {
      getCsdnInterface('ywtg_zbzx_zb_ls', { area_name: value }).then((res) => {
        let data = res.data.data
          .sort((a, b) => {
            return new Date(b.指标生成日期).getTime() - new Date(a.指标生成日期).getTime()
          })
          .filter((item) => {
            return item.指标值 != '-'
          })
        const arraa = ['网格人口异常聚集预警数', '当日8890受理数', '市场主体注销量同比']
        this.tdData = data.filter((item) => {
          return arraa.includes(item.指标名称)
        })
        // if (data.length > 24) {
        //   // 按 指标生成日期 降序排序，保证指标名称是市场主体注销量同比的在前24
        //   const zbIndex = data.findIndex((item) => {
        //     return item.指标名称 == '市场主体注销量同比'
        //   })
        //   if (zbIndex > 24) {
        //     // zbIndex和23位互换
        //     const temp = data[23]
        //     data[23] = data[zbIndex]

        //   }
        // } else {
        //   this.gjzbList = res.sort((a, b) => {
        //     return new Date(b.指标生成日期).getTime() - new Date(a.指标生成日期).getTime()
        //   }).slice(0, 24)
        // }
      })
    },
    getGeoJon() {
      async function getData() {
        let res = await fetch('/map/bounds.json')
        return res.json()
      }
      getData().then((result) => {
        echarts.registerMap('jh', result)
        this.myChartMap = echarts.init(this.$refs.myEchart)
        this.initEcharts()
        getCsdnInterface('baiduydindex').then((res) => {
          // this.popData = res;
          // res.forEach((item) => {
          //   this.popData.forEach((i) => {
          //     if (item.nodeName == i.name) {
          //       i.value.push(item.yongduIndex)
          //     }
          //   })
          // })
          // this.initEcharts()
        })
      })
    },
    //画图
    initEcharts() {
      let _this = this
      this.$nextTick(() => {
        let option = {
          // 悬浮窗
          tooltip: {
            show: true,
            trigger: 'item',
            // formatter: (params) => {
            //   if (params.componentSubType == 'map' && params.name != '开发区') {
            //     //params是echarts的属性
            //     let res = '' //变量一个res
            //     res = params.data //res等于params下的数据
            //     let names = params.name
            //     //return回调一个模板字符串，自定义提示框的形状
            //     // ${this.tootipObj[params.name].mhsh || '--'}
            //     // console.log(params.name)
            //     return `
            //  <div class="tootip-con">
            //   <div class="t-name">${params.name}四大分类指标</div>
            //   <div class="t-content">
            //     <li class="title-qy">美好生活:<span class="val-qy">${_this.tootipObj[names].mhsh}</span></li>
            //     <li class="title-qy">宜居宜游:<span class="val-qy">${_this.tootipObj[names].yjyy}</span></li>
            //     <li class="title-qy">营商环境:<span class="val-qy">${_this.tootipObj[names].yshj}</span></li>
            //     <li class="title-qy">城市安全:<span class="val-qy">${_this.tootipObj[names].csaq}</span></li>
            //     </div>
            //  </div>
            //   `
            //   }
            // },
            rich: {
              b: {
                align: 'left',
                lineHeight: 45,
                fontSize: 32,
                color: '#fff',
              },
            },
            textStyle: {
              color: 'rgba(212, 232, 254, 1)',
              fontSize: 32,
            },
            borderWidth: 0,
            backgroundColor: 'translate',
          },
          geo: [
            {
              show: true,
              map: 'jh',
              zoom: 1.4,
              roam: false,
              regions: [],
              zlevel: 5,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 1,
              itemStyle: {
                areaColor: 'transparent',
              },
              silent: true,
            },
            {
              show: true,
              map: 'jh',
              zoom: 1.4,
              roam: false,
              zlevel: 4,
              layoutCenter: ['50.2%', '50.2%'],
              layoutSize: '90%',
              aspectScale: 1,
              itemStyle: {
                borderWidth: 1,
                borderColor: 'rgba(22, 186, 212,0.8)',
                shadowColor: 'rgba(80, 183, 140,0.5)',
                shadowOffsetY: 5,
                shadowBlur: 15,
                areaColor: 'rgba(5,21,35,0.1)',
              },
              silent: true,
            },
            {
              show: true,
              map: 'jh',
              zoom: 1.4,
              roam: false,
              zlevel: 3,
              layoutCenter: ['50.3%', '50.3%'],
              layoutSize: '90%',
              aspectScale: 1,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
          ],
          // 数据
          series: [
            {
              type: 'map',
              map: 'jh',
              zoom: 1.4,
              roam: false,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 1,
              selectedMode: false,
              label: {
                //初始标签样式
                show: true,
                color: '#fff',
                position: 'inside',
                distance: 0,
                fontSize: 50,
              },
              itemStyle: {
                areaColor: {
                  image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                  repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
                },
                borderColor: '#70e8e9',
                borderWidth: 3,
                emphasis: {
                  label: {
                    show: true,
                    color: '#fff',
                    fontSize: 50,
                  },
                  areaColor: '#3fdaff',
                  // areaColor: 'rgba(228 ,236 ,240 ,0.8)',
                  // opacity: 0.3,
                  // areaColor: {
                  //   image: this.$refs.mapSvg, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串
                  //   repeat: 'repeat', // 是否平铺, 可以是 'repeat-x', 'repeat-y', 'no-repeat'
                  // },
                  borderColor: '#3fdaff',
                  borderWidth: 20,
                },
              },
              zlevel: 99,
              data: [],
            },
            {
              type: 'effectScatter',
              coordinateSystem: 'geo',
              zlevel: 999,
              data: _this.popData,
              symbolSize: 10,
              label: {
                normal: {
                  show: true,
                  formatter: function (params) {
                    return '{tline|' + '运行指数' + '}{fline|' + params.data.value[2] + '}'
                  },
                  position: 'top',
                  backgroundColor: '#0a0b0b99',
                  padding: [0, 0],
                  borderRadius: 3,
                  fontSize: 32,
                  fontWeight: 500,
                  color: '#ffffff',
                  rich: {
                    fline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 32,
                      // lineHeight: 45,
                      width: 80,
                      align: 'center',
                      height: 40,
                      backgroundColor: '#ff7817',
                    },
                    tline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 40,
                      lineHeight: 45,
                      width: 160,
                      align: 'center',
                      height: 40,
                    },
                  },
                },
                emphasis: {
                  show: true,
                },
              },
              itemStyle: {
                color: '#ff7817',
              },
            },
          ],
        }
        this.myChartMap.setOption(option)
        this.myChartMap.off('click')
        this.myChartMap.on('click', this.echartsMapClick)
      })
    },
    echartsMapClick(params) {
      // console.log(params);
      this.currentNodeName = params.name
      if (this.currentNodeIndex == params.dataIndex) {
        this.myChartMap.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentNodeIndex,
        })
        this.currentNodeIndex = null
        top.emiter.emit('qxName', '金华市')
      } else {
        this.myChartMap.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentNodeIndex,
        })
        this.myChartMap.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
        })
        this.currentNodeIndex = params.dataIndex
        top.emiter.emit('qxName', this.currentNodeName)
      }
    },
    clickMapBtn(name) {
      console.log(name)
      if (!this.isMap || this.currentMapType == name) return
      this.currentMapType = name
      // top.emiter.emit('currentMapType', this.currentMapType)
      mapUtilZBZX.removeAllLayers([])
      this.flyTo()
      this.loadMask()
      if (name == '人口热力') {
        console.log(document.getElementById('ecartDiv'))
        this.isMap1 = true
        console.log("1111")
        this.addMapHot()
      } else if (name == '道路拥堵') {
        this.isMap1 = true
        this.addRoadMap()
      } else if (name == '预警事件') {
        this.isMap1 = true
        this.addYjMap()
      } else if (name == '应急资源') {
        this.isMap1 = true
        document.getElementById('yj').contentWindow.postMessage('reset', '*')
      } else if (name == '运行指数'){
        this.getGeoJon()
        this.isMap1 = false
      }
    },
    flyTo() {
      let opts_rk = {
        x: 119.50820897951105,
        y: 29.057243711279295,
        z: 50688.33974887617,
        heading: 348.4893178835531,
        tilt: 0.4938159010161647,
      }
      mapUtilZBZX.flyTo(opts_rk)
    },
    //添加人口热力
    addMapHot() {
      const mapData = {
        layerid: 'rkztHot0',
        type: 'dynamic',
      }
      mapUtilZBZX.loadHeatmapLayer(mapData)
    },
    //添加拥堵路况
    addRoadMap() {
      // 新地图添加路况
      mapUtilZBZX.loadTrafficLayer({
        layerid: 'icon_road_2',
      })
      this.pointRoadFun()
    },
    openDialog(name) {
      // let topm = 0;
      // let height = 0;
      // if (name == '网格情况') {
      //   topm = '1130px'
      //   height = '650px'
      // } else {
      //   topm = '1480px'
      //   height = '260px'
      // }
      let leftData2 = {
        type: 'openIframe',
        name: 'yj-dialog',
        src: '/static/citybrain/zbzx/commont/yj-dialog.html',
        width: '312px',
        height: '330px',
        left: '2880px',
        top: '1510px',
        zIndex: '900',
        argument: {
          type: name,
        },
      }
      top.postMessage(JSON.stringify(leftData2), '*')
    },
    //添加预警
    addYjMap() {
      getCsdnInterface('fxyj_yjxx', { status: '待确认', time: '周', type: '' }).then((res) => {
        this.addAllPoint(res.data.data)
      })
    },
    // 给拥堵上点
    pointRoadFun() {
      let that = this
      let str = "'婺城区','金东区','武义县','浦江县','磐安县','兰溪市','义乌市','东阳市','永康市','开发区'"
      getCsdnInterface('/cstz_baiduydd', { addressName: str }).then((res) => {
        for (let i = 0; i < res.data.data.length; i++) {
          let time = new Date(res.data.data[i].insert_time)
          let h = time.getHours(),
            m = time.getMinutes()
          res.data.data[i].insert_time = h.toString().padStart(2, '0') + ':' + m.toString().padStart(2, '0')
          res.data.data[i].idx = Number(res.data.data[i].idx)
        }
        var roadPointData = []
        res.data.data.map((ele) => {
          let roadName = `${ele.roadName}`
          let address = ele.description ? ele.description + ele.direction : '--'
          let arr = ele.location.split(',')
          let pointArr = that.transTo4490(arr)
          let point = pointArr[0] + ',' + pointArr[1]
          let str = {
            data: {
              item: ele,
              title: roadName,
              linkStates: ele.linkStates,
              key: ['指数', '时速', '拥堵距离', '持续时间'],
              value: [ele.idx, ele.speed, ele.distance, ele.durationMin],
              distance: ele.distance,
            },
            address: address,
            point: point,
            lng: pointArr[0],
            lat: pointArr[1],
            type: '路况拥堵点',
            idx: ele.idx,
            location: ele.location,
            linkStates: ele.linkStates,
          }
          roadPointData.push(str)
        })
        mapUtilZBZX.loadPointLayer({
          data: roadPointData,
          layerid: '拥堵点1',
          iconcfg: { image: '拥堵', iconSize: 5 },
          onclick: this.onclick,
        })
        clearTimeout(this.timeOut)
      })
    },
    onclick(e, list) {
      mapUtilZBZX.removeLayer('mouseente01')
      mapUtilZBZX.removeLayer('mouseente02')
      if (e.data.chn_code) {
        mapUtilZBZX.flyTo({
          destination: [e.data.gps_x, e.data.gps_y],
          // zoom: 15,
          offset: [0, -666],
        })
        let item = {
          obj: {
            // chn_name: e.data.chn_name,
            chn_name: e.data.video_name,
            pointList: list,
          },
          video_code: e.data.chn_code,
          csrk: true,
        }
        let iframe1 = {
          type: 'openIframe',
          name: 'video_main_code',
          src: baseURL.url + '/static/citybrain/csrk_3840/tckz/commont/video_main_code.html',
          width: '100%',
          height: '100%',
          left: '0',
          top: '0',
          zIndex: '1000',
          argument: item,
        }
        window.parent.lay.openIframe(iframe1)
      } else if (e.type == '路况拥堵点') {
        let coor = [e.lng, e.lat]
        let arr = {
          name: e.data.key,
          value: e.data.value,
        }
        let countStr = ''
        for (let index = 0; index < arr.name.length; index++) {
          countStr += `<div style="margin:0 10px">${arr.name[index]}：${arr.value[index]}</div>`
        }
        let str = `
               <div
                style="
                  position: relative;
                  background: url('/static/citybrain/csdn/img/du_bg.png') no-repeat;
                  background-size: 100% 100%;
                  width: max-content;
                  min-height: 320px;
                "
               >
                  <nav  style="display: flex; justify-content: space-between; margin: 0 20px">
                    <h2  style="
                      margin-top: 20px;
                      white-space: nowrap;
                      font-size: 38px;
                      display: flex;
                      background: linear-gradient(to bottom, #df5151, #f4f1ff, #ff4949, #e03e3e);
                      -webkit-background-clip: text;
                      color: transparent;">
                      <img src="/static/citybrain/csdn/img/dupoint.png" width="50px" alt="" />
                      ${e.data.title}${e.data.item.eventSource}（${e.data.item.insert_time}）
                      <span style="font-size: 32px !important;font-style: italic;">${e.data.item.congestAffectArea}</span>
                    </h2>
                    <span style="cursor: pointer; margin-left: 20px; font-size: 40px;width:34px;color: #fff" onclick="this.parentNode.parentNode.style.display = 'none'">x</span>
                  </nav>
                  <header style="font-size: 32px;margin: 20px 20px 10px 20px; display: flex; justify-content: space-between;padding:10px;box-sizing:border-box;">
                    <div style="width: 80%; overflow: hidden;display:flex">
                      <img src="/static/citybrain/csdn/img/address.png" width="40px" height="50px" style="margin-right:10px" />
                      <div>
                        <p  style="background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
                        -webkit-background-clip: text;
                        color: transparent;font-size:36px !important;
                        width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis
                      "
                          title="${e.address}">${e.address}</p>
                      <p  style="color: #fff;width: 500px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis" title="${e.data.item.congestTailDesc}">源头：${e.data.item.congestTailDesc}</p>
                      </div>
                    </div>
                    <div style="font-size: 32px;color: #fff; background: rgba(216, 81, 81, 0.897);padding: 2px 5px;border-radius: 5px;height:40px;line-height:40px;">${e.data.item.extraEventStatus}</div>
                  </header>
                  <footer style="color: #05BE94;white-space: nowrap;display: flex;justify-content: space-around;margin: 0 20px;font-size: 32px;">
                    ${countStr}
                  </footer>
                </div> `

        let objData = {
          layerid: e.layerid,
          position: coor,
          offset: [55, -40],
          closeButton: true,
          content: str,
        }
        mapUtilZBZX._createPopup(objData)
        this.roadMapFun(e)
      }
    },
    roadMapFun(obj) {
      let that = this
      let arrLngLats = obj.data.linkStates
      // 畅通 缓行 拥堵 严重拥堵
      // 由于调用了地图绿色的路况了所以只要除了畅通以外的数据
      this.rmAllLayer(['hxRoad_index', 'ydRoad_index', 'yzydRoad_index'])
      mapUtilZBZX.removeAllLayers(['camera-index-3840'])
      //  清除所有视频
      for (let i = 0; i < this.videoMapId.length; i++) {
        let id = this.videoMapId[i]
        this.rmLayer('拥堵视频' + id)
        this.rmLayer('roadText' + id)
      }
      let hxData = []
      let ydData = []
      let yzydData = []
      let point = obj.location.split(',')
      let pointArr = that.transTo4490(point)
      this.videoMapId = []
      let arrList = []
      for (let key of Object.keys(arrLngLats)) {
        let line = arrLngLats[key]
        let arrData = line.split(';')
        let lineArr = []
        let lineStr = ''
        for (let i = 0; i < arrData.length; i++) {
          // 排序
          // let str=that.roadSort(arrData[i])
          // 不排序
          let str = arrData[i]
          let arr = str.split(/[,;]/)
          let coords = that.transTo4490(arr)
          let coordsStr = coords.join(',')
          lineArr.push(coordsStr)
          obj.idx >= 1.5 && obj.idx < 2
            ? hxData.push(coords)
            : obj.idx >= 2 && obj.idx < 4
            ? ydData.push(coords)
            : obj.idx >= 4
            ? yzydData.push(coords)
            : ''
        }
        lineStr = lineArr.join(';')

        let len = Math.ceil(lineArr.length / 2 - 1)
        let linePoint = lineArr[len].split(',')

        that.videoMapId.push(key)
        mapUtilZBZX.loadTextLayer({
          layerid: 'roadText' + key,
          data: [
            {
              pos: [linePoint[0], linePoint[1]], //上文字经纬度
              //内容
              text: obj.data.distance,
            },
          ], //数据
          style: {
            size: 40, //文字大小
            color: [252, 198, 42, 1], //文字颜色
          },
        })
        mapUtilZBZX.loadRoadVideo({
          layerid: '拥堵视频' + key,
          videoType: '拥堵视频' + key,
          distance: 30,
          lineStr: lineStr,
          // onclick: this.onclick,
          callback: (e) => {
            e.forEach((item) => {
              arrList.push(item)
            })
          },
        })
      }
      that.addPoint(arrList)
    },
    async addPoint(arrList) {
      let forData = []
      for (let i = 0; i < arrList.length; i++) {
        if (!forData.some((e) => e.id == arrList[i].id)) forData.push(arrList[i])
      }
      let arr = []
      for (let i = 0; i < forData.length; i++) {
        let res = await getCsdnInterface('/xxwh_dwzlmore', { code: forData[i].chn_code }).then((res) => {
          let cameraType = res.data.data[0].isHighAltitude == 1 ? '4' : res.data.data[0].cameraType
          return {
            code: res.data.data[0].chn_code,
            pointId: 'camera-index-3840',
            data: res.data.data[0],
            point: res.data.data[0].gps_x + ',' + res.data.data[0].gps_y,
            lng: res.data.data[0].gps_x,
            lat: res.data.data[0].gps_y,
            status: res.data.data[0].is_online,
            cameraType: cameraType,
            pointType: this.getPointType(res.data.data[0].is_online, cameraType),
          }
        })
        arr.push(res)
      }
      console.log(arr)
      this.getManyPoint(arr)
    },
    getPointType(is_online, cameraType) {
      let arr = is_online + '-' + cameraType
      let obj = {
        枪机在线: '1-1',
        枪机离线: '0-1',
        球机在线: '1-2',
        球机离线: '0-2',
        半球机在线: '1-3',
        半球机离线: '0-3',
        高点在线: '1-4',
        高点离线: '0-4',
      }
      for (key in obj) {
        if (obj[key] == arr) {
          return key
        }
      }
    },
    //一次绘制多种不同类型的点
    getManyPoint(pointData, pointId) {
      mapUtilZBZX.loadPointLayer({
        layerid: 'camera-index-3840',
        data: pointData,
        onclick: this.onclick,
        onblur: this.onblur,
        cluster: true, //是否定义为聚合点位：true/false
        iconcfg: {
          image: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
          iconSize: 0.5,
          iconlist: {
            field: 'pointType',
            list: [
              {
                value: '枪机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiangji.png`,
              },
              {
                value: '枪机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiangji.png`,
              },
              {
                value: '球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-qiuji.png`,
              },
              {
                value: '球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-qiuji.png`,
              },
              {
                value: '半球机在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-banqiu.png`,
              },
              {
                value: '半球机离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-banqiu.png`,
              },
              {
                value: '高点在线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-zx-gaodian.png`,
              },
              {
                value: '高点离线',
                size: '50',
                src: `${baseURL.url}/static/EGS(v1.0.0)/lib/EGS(v1.0.0)/image/spritesImage/camera-lx-gaodian.png`,
              },
            ],
          },
        },
      })
    },
    // 处理路况的排序
    roadSort(item) {
      let allArr = []
      let ccc = item.split(',')
      let a, b
      let aIndex = 0
      let bIndex = 1
      let arrData = []
      ccc.forEach((str, index) => {
        if (index % 2 === 0) {
          a = str
          aIndex += 1
        } else {
          b = str
          bIndex += 1
        }
        if (a && b && bIndex - aIndex === 1) {
          let d = a + ',' + b
          arrData.push(d)
        }
      })
      let sortData = arrData.sort()
      allArr.push(...sortData)
      let strData = allArr.toString()
      return strData
    },
    // 处理路况的经纬度
    transTo4490(arr) {
      const length = arr.length / 2
      const pointArr = []
      for (let i = 1; i <= length; i++) {
        const index = i * 2
        let jwd1 = coordtransform.bd09togcj02(arr[index - 2], arr[index - 1])
        let jwd2 = coordtransform.gcj02towgs84(jwd1[0], jwd1[1])
        let str = [jwd2[0], jwd2[1]].concat(0)
        pointArr.push(str)
      }
      return pointArr.flat()
    },
    // 加载全部预警点位
    addAllPoint(data) {
      let that = this
      let pointData = []
      let types = null
      mapUtilZBZX.drawTool.clear()
      mapUtilZBZX.removeAllLayers(['风险预警', 'syr', 'syr1', '重点区域点位', 'video-point'])
      data.map((item) => {
        types = this.findOfType(item.collaborative_matters)
        let obj = {
          collaborative_matters: item.collaborative_matters,
          collaborative_matters_name: item.collaborative_matters_name,
          xgrys: item.xgrys,
          address: item.address,
          meta_label: item.meta_label,
          find_way: item.find_way,
          describe: item.describe,
          STATUS: types + '-' + item.STATUS,
          type: item.type,
          yjId: item.id,
          push_time: item.push_time,
          jwd: item.jwd,
          lon: item.jwd.split(',')[0],
          lng: item.jwd.split(',')[0],
          lat: item.jwd.split(',')[1],
          yjSTATUS: item.STATUS,
        }
        pointData.push(obj)
      })
      if (pointData.length > 0) {
        // mapUtilZBZX.flyTo({
        //   destination: [pointData[0].lng || pointData[1].lng, pointData[0].lat || pointData[1].lat],
        // })
        let imgsStr = {
          待确认: 'red',
          处理中: 'yellow',
          已完成: 'green',
          无效: 'grey',
          全部: 'all',
        }
        let imgsName = imgsStr[that.status]
        let imgs = '/map/img/${imgsName}.png'
        mapUtilZBZX.loadPointLayer({
          data: pointData,
          layerid: '风险预警', //图层id
          iconcfg: {
            image: imgs,
            // iconSize: 0.3,
            iconlist: {
              field: 'STATUS',
              list: [
                {
                  value: '裸土整治类-待确认',
                  size: '100',
                  src: '/map/img/red.png',
                },
                {
                  value: '裸土整治类-处理中',
                  size: '100',
                  src: '/map/img/yellow.png',
                },
                {
                  value: '裸土整治类-已完成',
                  size: '100',
                  src: '/map/img/green.png',
                },
                {
                  value: '裸土整治类-无效',
                  size: '100',
                  src: '/map/img/gray.png',
                },
                {
                  value: '人员异常聚集类-待确认',
                  size: '100',
                  src: '/map/img/fxyj/yrycjj/red.png',
                },
                {
                  value: '人员异常聚集类-处理中',
                  size: '100',
                  src: '/map/img/fxyj/yrycjj/yellow.png',
                },
                {
                  value: '人员异常聚集类-已完成',
                  size: '100',
                  src: '/map/img/fxyj/yrycjj/green.png',
                },
                {
                  value: '人员异常聚集类-无效',
                  size: '100',
                  src: '/map/img/fxyj/yrycjj/grey.png',
                },
                {
                  value: '山洪告警类-待确认',
                  size: '100',
                  src: '/map/img/fxyj/shgj/red.png',
                },
                {
                  value: '山洪告警类-处理中',
                  size: '100',
                  src: '/map/img/fxyj/shgj/yellow.png',
                },
                {
                  value: '山洪告警类-已完成',
                  size: '100',
                  src: '/map/img/fxyj/shgj/green.png',
                },
                {
                  value: '山洪告警类-无效',
                  size: '100',
                  src: '/map/img/fxyj/shgj/grey.png',
                },
                {
                  value: '扬尘分贝告警类-待确认',
                  size: '100',
                  src: '/map/img/fxyj/ycfbgj/red.png',
                },
                {
                  value: '扬尘分贝告警类-处理中',
                  size: '100',
                  src: '/map/img/fxyj/ycfbgj/yellow.png',
                },
                {
                  value: '扬尘分贝告警类-已完成',
                  size: '100',
                  src: '/map/img/fxyj/ycfbgj/green.png',
                },
                {
                  value: '扬尘分贝告警类-无效',
                  size: '100',
                  src: '/map/img/fxyj/ycfbgj/grey.png',
                },
                {
                  value: '用电监测告警类-待确认',
                  size: '100',
                  src: '/map/img/fxyj/ydjcgj/red.png',
                },
                {
                  value: '用电监测告警类-处理中',
                  size: '100',
                  src: '/map/img/fxyj/ydjcgj/yellow.png',
                },
                {
                  value: '用电监测告警类-已完成',
                  size: '100',
                  src: '/map/img/fxyj/ydjcgj/green.png',
                },
                {
                  value: '用电监测告警类-无效',
                  size: '100',
                  src: '/map/img/fxyj/ydjcgj/grey.png',
                },
                {
                  value: '火灾成灾类-待确认',
                  size: '100',
                  src: '/map/img/fxyj/hzcz/red.png',
                },
                {
                  value: '火灾成灾类-处理中',
                  size: '100',
                  src: '/map/img/fxyj/hzcz/yellow.png',
                },
                {
                  value: '火灾成灾类-已完成',
                  size: '100',
                  src: '/map/img/fxyj/hzcz/green.png',
                },
                {
                  value: '火灾成灾类-无效',
                  size: '100',
                  src: '/map/img/fxyj/hzcz/grey.png',
                },
                {
                  value: '其他-待确认',
                  size: '100',
                  src: '/map/img/fxyj/others/red.png',
                },
                {
                  value: '其他-处理中',
                  size: '100',
                  src: '/map/img/fxyj/others/yellow.png',
                },
                {
                  value: '其他-已完成',
                  size: '100',
                  src: '/map/img/fxyj/others/green.png',
                },
                {
                  value: '其他-无效',
                  size: '100',
                  src: '/map/img/fxyj/others/grey.png',
                },
              ],
            },
          }, //图标
          // cluster: false,//是否聚合
          // datacfg: datacfg,
          onclick: this.openMassage,
          // popcfg: popCfig,
        })
      }
    },
    findOfType(name) {
      let typeList = [
        '裸土整治类',
        '人员异常聚集类',
        '山洪告警类',
        '扬尘分贝告警类',
        '用电监测告警类',
        '火灾成灾',
        '其他',
      ]
      if (!name) {
        name = '其它'
      }
      if (name.indexOf('裸土') != -1) {
        return '裸土整治类'
      } else if (name.indexOf('人员异常') != -1) {
        return '人员异常聚集类'
      } else if (name.indexOf('山洪') != -1) {
        return '山洪告警类'
      } else if (name.indexOf('扬尘') != -1) {
        return '扬尘分贝告警类'
      } else if (name.indexOf('用电') != -1) {
        return '用电监测告警类'
      } else if (name.indexOf('火灾') != -1) {
        return '火灾成灾类'
      } else {
        return '其他'
      }
    },
    openMassage(e) {
      e.id = e.yjId
      e.STATUS = e.yjSTATUS
      this.openPoint(e)
    },
    openPoint(item, index) {
      mapUtilZBZX.removeLayer('sou_wg')
      // top.DHWsInstance.destroyCtrl(['ctrl1', 'ctrl2'])
      top.postMessage(
        JSON.stringify({
          type: 'changeRight',
          obj: item,
          topDateIndex: this.topDateIndex,
        }),
        '*'
      )
      let icon = 'fxyj'

      let data = [
        {
          collaborative_matters: item.collaborative_matters,
          meta_label: item.meta_label,
          find_way: item.find_way,
          describe: item.describe,
          STATUS: item.STATUS,
          push_time: item.push_time,
          lng: item.jwd.split(',')[0],
          lat: item.jwd.split(',')[1],
        },
      ]

      // this.addOnlyPoint(icon, data)
      //点击列表行 飞行到固定点，上点
      mapUtilZBZX.flyTo({
        destination: [Number(item.jwd.split(',')[0]), Number(item.jwd.split(',')[1])],
        zoom: 18,
        tilt: 40,
      })
      // mapUtilZBZX.flyTo({
      //   destination: [item.jwd.split(',')[0], item.jwd.split(',')[1]],
      //   offset: [1200, 0],
      // })
      if (item.find_way == '物联感知') {
        let point = item.jwd.replace(' ', '')
        this.getWlgzData('type=zbjk', point, 0.5)
        mapUtilZBZX.drawTool.draw('circle', {
          circles: [
            {
              center: [item.jwd.split(',')[0], item.jwd.split(',')[1]],
              radius: 500,
              fillColor: [0, 255, 255, 0.1],
              strokeColor: [110, 44, 65, 1],
            },
          ],
          layerid: 'drawcircles',
        })
      } else if (item.type == 'rkjjyj') {
        this.drawArea(item)
      }
      if (item.STATUS === '待确认') {
        let leftData = {
          type: 'closeIframeByNames',
          name: ['fxyj-sjxx', 'fxyj-sjDetail'],
        }
        top.postMessage(JSON.stringify(leftData), '*')
        let leftData2 = {
          type: 'openIframe',
          name: 'fxyj-jbxx',
          src: '/static/citybrain/djtl/commont/fxyj-jbxx.html',
          width: '1500px',
          height: '1580px',
          left: '3090px',
          top: '300px',
          zIndex: '900',
          argument: {
            type: 'changeJbxxMsg',
            obj: item,
          },
        }
        top.postMessage(JSON.stringify(leftData2), '*')
      } else if (item.STATUS === '处理中' || item.STATUS === '已完成') {
        let leftData = {
          type: 'closeIframeByNames',
          name: ['fxyj-jbxx', 'fxyj-sjDetail'],
        }
        top.postMessage(JSON.stringify(leftData), '*')
        this.eventCorrelation(item)
      }
    },
    eventCorrelation(item) {
      top.DHWsInstance.destroyCtrl(['ctrl1', 'ctrl2'])
      // top.commonObj.openMenuFun('sjrw_three')
      // sessionStorage.setItem('showSjrw', true)

      let leftData2 = {
        type: 'openIframe',
        name: 'fxyj-sjxx',
        src: '/static/citybrain/djtl/commont/fxyj-sjxx.html',
        width: '2040px',
        height: '1660px',
        left: '2820px',
        top: '250px',
        zIndex: '900',
        argument: {
          type: 'changeFxyjMsg',
          obj: item,
        },
      }
      top.postMessage(JSON.stringify(leftData2), '*')
    },
    // 人员异常聚集画面
    drawArea(item) {
      let point = item.jwd.replace(' ', '')
      this.getWlgzData('type=zbjk', point, 0.5)

      mapUtilZBZX.removeLayer('sou_wg')
      getCsdnInterface('fxyj_map_area', { id: item.id }).then((res) => {
        let geojsonData = {
          type: 'FeatureCollection',
          features: [res.data.data[0].feature],
        }
        mapUtilZBZX.loadPolygonLayer({
          layerid: 'sou_wg',
          data: geojsonData,
          style: {
            strokeColor: [255, 50, 40, 0.9], //多边形轮廓颜色透明度
            fillColor: [193, 210, 240, 0.2], //多边形填充色
          },
          onclick: function (e) {
            console.log('多边形点击事件')
          },
        })
      })
    },
    getWlgzData(label, point, distance) {
      // zbjk
      let that = this
      let type = label
      // distance: '12',
      // point:'119.6842722,29.0851881',
      axios({
        method: 'get',
        url: baseURL.url + '/jhyjzh-server/screen_api/home/<USER>',
        params: {
          type: type,
          distance: distance,
          point: point,
        },
      }).then(function (data) {
        let arr = data.data.data.zbjk.pointData
        let str = ''
        let dataArr = []
        arr.map((item, index) => {
          if (index < 3) {
            str += item.data.name + ','
          }
          let cameraType = item.isHighAltitude == 1 ? '未知' : item.cameraType
          let obj = {
            cameraType: cameraType,
            data: item,
            is_online: item.is_online,
            lat: item.point.split(',')[1],
            lng: item.point.split(',')[0],
            pointType: that.getPointType(item.is_online, cameraType),
            pointId: 'video',
          }
          dataArr.push(obj)
        })
        mapUtilZBZX.removeLayer('video-point')
        that.getManyPoint(dataArr)
      })
    },
    mouseenterEvent1() {
      clearInterval(this.time1)
    },
    mouseleaveEvent1() {
      this.time1 = setInterval(() => {
        this.dom1.scrollBy({
          top: 70,
          behavior: 'smooth',
        })
        if (this.dom1.scrollTop >= this.dom1.scrollHeight - this.dom1.offsetHeight) {
          this.dom1.scrollTop = 0
        }
      }, 1500)
    },
    //绘制异常指标类型分布
    getChart01(id, data) {
      let myEc = echarts.init(document.getElementById(id))
      let imgUrl = '/static/citybrain/djtl/img/wlgz/pie-bg.png'
      let color = ['#0061FF', '#0091FF', '#FADC6D', '#D53939']
      option = {
        title: {
          text: '异常指标类型(例)',
          textAlign: 'center',
          subtext: '3',
          x: '25%',
          y: '35%',
          textStyle: {
            color: '#fff',
            fontSize: 32,
            fontWeight: 'normal',
            align: 'center',
            lineHeight: 100,
            // width: "200px",
          },
          subtextStyle: {
            color: '#0BFFFE',
            fontSize: 88,
            fontWeight: 'normal',
            align: 'center',
            // lineHeight: 125
          },
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          textStyle: {
            color: 'white',
            fontSize: '30',
          },
        },
        legend: {
          orient: 'vertical',
          icon: 'circle',
          top: 'center',
          right: '10%',
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 50,
          align: 'left',
          textStyle: {
            color: '#DCEFFF',
            rich: {
              uname: {
                fontSize: 32,
              },
              unum: {
                color: '#DCEFFF',
                width: 100,
                align: 'left',
                fontSize: '32',
              },
            },
          },
          formatter: function (name) {
            // var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = ((tarValue / total) * 100).toFixed(1)
            return `{uname| ${name}   }{unum|${p}%}{unum|      ${tarValue}例}`
          },
        },
        series: [
          {
            name: '异常指标类型',
            type: 'pie',
            radius: ['45%', '53%'],
            center: ['26%', '50%'],
            color: color,
            label: {
              normal: {
                show: false,
              },
            },
            data: data,
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
      tools.loopShowTooltip(myEc, option, { loopSeries: true })
    },
  },
  watch: {},
  beforeDestroy() {
  // 清除定时器
  if(this.time1) {
    clearInterval(this.time1)
  }

  // 销毁地图实例
  if(mapUtilZBZX) {
    // 移除所有图层
    mapUtilZBZX.removeAllLayers()
    
    // 清除绘制工具
    if(mapUtilZBZX.drawTool) {
      mapUtilZBZX.drawTool.clear()
    }

    // 销毁地图
    if(viewZBZX) {
      viewZBZX.container = null
      viewZBZX = null
    }
  }

  // 销毁 echarts 实例
  if(this.myChartMap) {
    this.myChartMap.dispose()
    this.myChartMap = null
  }

  // 移除事件监听
  if(top.emiter) {
    top.emiter.off('qxData')
  }
},
}
</script>

<style scoped lang="less">
.container {
}
.con {
  width: 3204px;
  height: 1875px;
  background: rgb(13 19 39 / 60%);
  display: flex;
  justify-content: space-between;
  /* opacity: 0.12; */
}
.left-con {
  width: 1028px;
  height: 100%;
}
#chart01 {
  width: 100%;
  height: 750px;
}
/* 表格样式================================== */
.table_box {
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  height: 870px;
  overflow: hidden;
}
.table1 {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
}

.table1 .th {
  padding: 0 20px;
  box-sizing: border-box;
  width: 100%;
  height: 92px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-weight: 700;
  font-size: 30px;
  line-height: 92px;
  color: #fff;
  text-align: left;
}

.border-bottom {
  height: 20px;
  background: url("@/assets/home/<USER>/table-top.png") no-repeat;
  background-size: 100% 100%;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: left;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 130px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  padding: 0 20px;
  height: 92px;
  display: flex;
  background-color: #0f2b4d;
  /* justify-content: center; */
  align-items: center;
}

.table-cursor .tr:nth-child(1) {
  cursor: pointer;
}

.table1 .tr:nth-child(2n) {
  background: rgba(27, 130, 183, 0.22);
}

.table1 .tr:nth-child(2n + 1) {
  background: rgba(27, 130, 183, 0.12);
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

.table1 .tr_td {
  font-size: 30px;
  width: 100%;
  height: 92px;
  line-height: 92px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  text-align: left;
}

/* 表格自动滚动 */
/* @keyframes rowUp {
    0% {
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
    100% {
      transform: translate3d(0, -100%, 0);
      -webkit-transform: translate3d(0, -100%, 0);
      -moz-transform: translate3d(0, -100%, 0);
      -ms-transform: translate3d(0, -100%, 0);
      -o-transform: translate3d(0, -100%, 0);
    }
  } */
/* .tr {
    animation: 10s rowUp linear infinite normal;
    -webkit-animation: 10s rowUp linear infinite normal;
  } */
.tbody:hover .tr {
  animation-play-state: paused;
}

.tbody .tr:nth-child(2n) {
  background: rgba(50, 134, 248, 0.25);
}

.tbody .tr:nth-child(2n + 1) {
  background: rgba(50, 134, 248, 0.15);
}
.right-con {
  width: calc(100% - 1088px);
  height: 100%;
}
.map-con {
  width: 96%;
  height: 95%;
  padding-top: 40px;
  box-sizing: border-box;
  position: relative;
}
#viewDiv {
  /* width: 1614px; */
  width: 100%;
  height: 1380px;
  margin: 0px auto;
  position: absolute;
}
.map-echart {
  width: 1930px;
  background: #2d4a67;
  height: 1380px;
  position: absolute;
}
.map-btn {
  width: 1400px;
  height: 110px;
  margin: 60px auto 0px;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: 70px;
  left: 45%;
  z-index: 99;
}
.map-btn li {
  width: 350px;
  height: 112px;
  background-image: url("@/assets/home/<USER>/map-btn.png");
  background-size: 100% 100%;
  color: #00a3ff;
  font-size: 44px;
  line-height: 112px;
  list-style: none;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
}
.map-zs {
  width: 164px;
  height: 85px;
  background-image: url("@/assets/home/<USER>/bottom-zs.png");
  background-size: 100% 100%;
  position: absolute;
  left: 46%;
  bottom: -63px;
  /* left: 55%; */
}
.active {
  color: #fff !important;
}
.wg {
  width: 312px;
  height: 650px;
  position: absolute;
  bottom: 273px;
  right: -13px;
  /* display: none; */
}
/* 共用暂无数据 */
.empty-img {
  width: 500px;
  height: 500px;
  margin: 130px auto;
  font-size: 50px;
  color: #fff;
  text-align: center;
  line-height: 500px;
  /* background: url('/static/citybrain/csrk_3840/cshl/img/暂无数据.png');
  background-size: 100% 100%; */
}
.tootip-con {
  width: 700px;
  height: 400px;
  background-color: rgba(0, 0, 0, 0.8);
  border: 1px solid #0091ff;
  border-radius: 14px;
  position: relative;
  padding: 0px 40px;
  box-sizing: border-box;
}

.t-name {
  width: 100%;
  border-left: 20px solid #0091ff;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 40px;
  font-family: Source Han Sans CN-Bold, Source Han Sans CN;
  font-weight: 700;
  color: #ffffff;
  line-height: 40px;
  position: absolute;
  top: 25px;
}

.t-content {
  width: 91%;
  height: 455px;
  position: absolute;
  top: 70px;
}

.title-qy {
  color: #9bd2ff;
  line-height: 32px;
  font-size: 32px;
  margin: 30px;
}

.val-qy {
  font-size: 36px !important;
  font-weight: bold;
}
</style>
