<!--经济生态-市场主体-->
<template>
  <div style="margin-top: 30px">
    <pie3D :id="'pie2'" :options="values" :width-custom="939" :height-custom="287" :legend-custom="legendOptions" :internalDiameterRatio="0.7" :scaleCustom="1" :zHeight="0.01"></pie3D>
  </div>
</template>

<script>
import pie3D from "@/components/pie3D";
export default {
  name: "MarketSubject",
  data() {
    return {
      values:[
        {
          name:"企业",
          value:22530
        },
        {
          name:"个体户",
          value:42232
        },
        {
          name:"农村专业合作社",
          value:220
        },
        {
          name:"国有企业",
          value:200
        },
        {
          name:"集体企业",
          value:34
        }
      ],
      legendOptions:{
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 15,
        left: '50%',
        top: '20%',
      }
    }
  },
  components:{
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>