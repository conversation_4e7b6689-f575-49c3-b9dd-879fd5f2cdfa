<template>
  <div class="innerRight">
    <Wrapbox bg="right">
      <div class="rightWrapper animate__animated animate__fadeInRight">
        <jyfw />
        <shfw />
        <jzfw />
        <wtfw />
        <lyfw />
      </div>
    </Wrapbox>
  </div>
</template>

<script>
import Wrapbox from "@/components/wrapbox.vue"
import jyfw from "@/pages/PublicService/components/rightComponents/jyfw";
import shfw from "@/pages/PublicService/components/rightComponents/shfw";
import jzfw from "@/pages/PublicService/components/rightComponents/jzfw";
import lyfw from "@/pages/PublicService/components/rightComponents/lyfw";
import wtfw from "@/pages/PublicService/components/rightComponents/wtfw";

export default {
  components: {
    Wrapbox,
    jyfw,
    shfw,
    jzfw,
    lyfw,
    wtfw
  },
  data () {
    return {
    }
  }
}
</script>

<style scoped lang="less">
.innerRight {
  .rightWrapper {
    width: 100%;
    height: 1904px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }
}

</style>