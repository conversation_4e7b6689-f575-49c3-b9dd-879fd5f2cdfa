<template>
  <div>
    <pie3D id="cadreEcharts"  :scaleCustom="1"  :options="chartsData" :legendCustom="legend" :internalDiameterRatio="0.7" class="chart"></pie3D>
  </div>
</template>

<script>
import pie3D from "@/components/pie3D";
export default {
  name: "charts1",
  data() {
    return {
      chartsData:[
        {
          value: 55,
          name: "社区书记数"
        },
        {
          value: 103,
          name: "村书记数"
        }
      ],
      legend: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 60,
        left: '60%',
        top: '40%',
      }
    }
  },
  computed: {},
  components:{
    pie3D
  },
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>