<!--城市人口-->
<template>
  <div class="city">
    <titleTwo :title-text="'城市人口'" style="margin-top: 17px" :width="'1957'"></titleTwo>
    <div class="populationList">
      <div class="populationItem" v-for="(item,i) in cityPopulations" :key="i">
        <div class="info" :style="{background:'url(' + item.background + ')'}">
          <div class="name">{{item.name}}</div>
          <div class="value gold">{{item.value}}</div>
          <div class="unit gold">{{item.unit}}</div>
        </div>
        <div class="base" :style="{background:'url(' + item.baseImg + ')'}"></div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "city",
  data() {
    return {
      cityPopulations: [
        {
          name:"登记人口数",
          value:51.9,
          unit:"万人",
          background:require("@/assets/safe/城市人口.png"),
          baseImg: require("@/assets/safe/base1.png")
        },
        {
          name:"常住人口数",
          value:48.7,
          unit:"万人",
          background:require("@/assets/safe/城市人口1.png"),
          baseImg: require("@/assets/safe/base2.png")
        },
        {
          name:"常住人口家庭户数",
          value:11.2,
          unit:"万人",
          background:require("@/assets/safe/城市人口2.png"),
          baseImg: require("@/assets/safe/base3.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .city {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    .populationList {
      width: 1811px;
      height: 350px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .populationItem {
        height: 100%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        .info {
          width: 532px;
          height: 176px;
          background-size: cover;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-top: -10px;
          .name {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            margin-left: 160px;
            white-space: nowrap;
          }
          .value {
            font-size: 60px;
            font-family: BN;
            font-weight: 400;
            margin-left: 10px;
          }
          .unit {
            font-size: 25px;
            margin-top: 15px;
            white-space: nowrap;
            margin-left: 10px;
          }
        }
        .base {
          width: 513px;
          height: 167px;
          background-size: cover;
          margin-top: -15px;
        }
      }
    }
  }
</style>