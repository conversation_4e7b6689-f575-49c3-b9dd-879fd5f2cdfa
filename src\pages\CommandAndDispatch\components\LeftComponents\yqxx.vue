<template>
  <div class="container">
    <Ktitle :titleText="'舆情信息'" width="939.6" :show-date="false">
      <div class="titleText">
        <div class="icon"></div>
        <div class="text" style="cursor: pointer" @click="downloadFile">舆情报告</div>
      </div>
    </Ktitle>
    <div class="container-content">
      <div class="container-content-item" v-for="(item, i) in list" :key="i" @click="showDetail(item)">
        <div class="container-content-item-inner">
          <div class="container-content-item-inner-title" :title="item.title">{{ item.title }}</div>
          <div class="container-content-item-inner-bottom">
            <div class="container-content-item-inner-bottom-laiyuan">{{ item.laiyuan }}</div>
            <div class="container-content-item-inner-bottom-date">{{ item.date }}</div>
          </div>
        </div>
      </div>
    </div>
    <Commondialog :title="dialogTitle" :dialogFlag="showDialog" @close="showDialog = false" :dialogWidth="'1320px'">
      <CommonDialogDetail :detailData="itemObj" :keyDesc="keyList" :width="1260"></CommonDialogDetail>
    </Commondialog>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import Commondialog from '@/components/Commondialog'
import CommonDialogDetail from '@/components/CommonDialogDetail'
import { getCsdnInterface,getYqDetail } from "@/api/csdnIndexApi";
export default {
  name: 'yqxx',
  data() {
    return {
      fileUrl: 'KFQ-2024-05-27舆情报告.docx',
      list: [],
      dialogTitle: '',
      showDialog: false,
      itemObj: {},
      keyList: [
        {
          label: '标题',
          key: 'bt',
          isHalf: true,
        },
        {
          label: '时间',
          key: 'sj',
          isHalf: true,
        },
        {
          label: '部门',
          key: 'bm',
          isHalf: true,
        },
        {
          label: '风险等级',
          key: 'fxdj',
          isHalf: true,
        },
        {
          label: '标签',
          key: 'bq',
          isHalf: true,
        },
        {
          label: '内容',
          key: 'nr',
          isHtml: true
        },
      ],
    }
  },
  components: {
    Ktitle,
    Commondialog,
    CommonDialogDetail,
  },
  computed: {},
  mounted() {
    this.getYqList()
  },
  methods: {
    //获取舆情列表
    getYqList() {
      getCsdnInterface('xzzf_qylb',{qx:'开发区'}).then(res => {
        this.list = res.data.data.map(item => ({
          id: item.id,
          title: item.text,
          laiyuan: item.source,
          date: item.time,
          content: item.text,
        }))
      })
    },
    showDetail(item) {
      this.dialogTitle = '舆情详情'
      getYqDetail({id: item.id}).then(res => {
        this.itemObj = res.data.data
        this.showDialog = true
      })
    },
    downloadFile() {
      const link = document.createElement('a');
      link.href = this.fileUrl;
      link.download = '舆情详情';
      document.body.appendChild(link); // 需要将链接添加到DOM中才能触发下载
      link.click(); // 触发点击事件
      document.body.removeChild(link); // 下载完成后移除链接
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  .titleText {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .icon {
      width: 64px;
      height: 60px;
      background: url('~@/assets/Command/baogao.png') no-repeat;
      background-size: cover;
    }
    .text {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #7dbef1;
    }
  }
  ::-webkit-scrollbar {
    width: 0;
  }
  .container-content {
    width: 940px;
    height: 588px;
    overflow-y: scroll;
    margin-top: 30px;
    .container-content-item {
      width: 940px;
      height: 132px;
      background: url('~@/assets/Command/yqxxBg.png') no-repeat;
      background-size: cover;
      margin-bottom: 20px;
      overflow: hidden;
      cursor: pointer;
      .container-content-item-inner {
        margin: 26px 41px 0 28px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        .container-content-item-inner-title {
          width: 884px;
          height: 48px;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 36px;
          color: #ffffff;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .container-content-item-inner-bottom {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .container-content-item-inner-bottom-laiyuan {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 28px;
            color: #77b3f1;
          }
          .container-content-item-inner-bottom-laiyuan::before {
            width: 30px;
            height: 30px;
            content: url('~@/assets/Command/ziyuan.png');
            margin-right: 9px;
          }
          .container-content-item-inner-bottom-date {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 28px;
            color: #77b3f1;
          }
          .container-content-item-inner-bottom-date::before {
            width: 31px;
            height: 30px;
            content: url('~@/assets/Command/shijian.png');
            margin-right: 9px;
          }
        }
      }
    }
  }
}
</style>
