<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts2">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <div class="charts12" id="charts12"></div>
  </div>
</template>

<script>
import {getWarningFx} from "@/api/home";
export default {
  name: 'charts2',
  data() {
    return {
      value: 2, //预警总数分析默认
      options: [
        { label: '年度', value: 1 },
        { label: '季度', value: 4 },
        { label: '月度', value: 2 },
      ], //预警总数分析下拉列表
      charts2Data: [],
    }
  },
  computed: {},
  mounted() {
    this.initCharts2()
  },
  methods: {
    initCharts2() {
      getWarningFx({type: this.value}).then(res => {
        this.charts2Data = res.data.data.map(item => ({
          name: item.name,
          value: item.num
        }))
        let myChart = this.$echarts.init(document.getElementById('charts12'))
        let option = {
          tooltip: {
            trigger: 'axis',
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          grid: {
            left: '0%',
            right: '0%',
            top: '22%',
            bottom: '1%',
            containLabel: true,
          },
          xAxis: [
            {
              type: 'category',
              data: this.charts2Data.map((item) => item.name),
              axisLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)', // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: '#D6E7F9',
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: '预警总数/个',
              type: 'value',
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: [10, -100, 10, 10],
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
            {
              name: '',
              type: 'value',
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: '#D6E7F9',
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: 'rgb(119,179,241,.4)',
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: '#D6E7F9',
                },
              },
            },
          ],
          series: [
            {
              name: '预警个数',
              type: 'bar',
              barWidth: 30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#00C0FF',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,192,255,0)',
                    },
                  ]),
                  barBorderRadius: 4,
                },
              },
              data: this.charts2Data.map((item) => item.value),
            },
          ]
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 60px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #fefefe;
  }
}
.charts12 {
  width: 931px;
  height: 300px;
  margin-top: 26px;
}
</style>
