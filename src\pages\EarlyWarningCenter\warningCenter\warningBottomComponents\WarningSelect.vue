<template>
  <div class="selectBox">
    <div class="selectBox-title">预警管理</div>
    <div class="selectBox-container">
        <div class="selectBox-container-item" v-for="(item,i) in list" :key="i">
          <img :src="item.icon" alt="" class="icon">
          <div class="name">{{item.value}}</div>
          <img v-show="!item.isChecked" class="wkuangClass noSelect" :src="require(`@/assets/common/wkuang.png`)" @click="checked(item)" style="margin-left: 30px;cursor: pointer">
          <img v-show="item.isChecked" class="wkuangClass noSelect" :src="require(`@/assets/common/xkuang.png`)" @click="checked(item)" style="margin-left: 30px;cursor: pointer">
        </div>
      </div>
  </div>
</template>

<script>
import {mapGetters, mapMutations} from 'vuex'
import {getPeopleWarnList} from "@/api/warning";
import {MapInitTimer} from "@/utils";
export default {
  name: "PointSelect",
  data() {
    return {
      list: [
        {
          id:"1",
          icon:require("@/assets/common/jhdc.png"),
          value:"金华地磁",
          isChecked:true,
          source: 1
        },
        {
          id:"2",
          icon:require("@/assets/common/hjjc.png"),
          value:"环境监测设备",
          isChecked:true,
          source: 1
        },
        {
          id:"3",
          icon:require("@/assets/common/zhjg.png"),
          value:"智慧井盖",
          isChecked:true,
          source: 1
        },
        {
          id:"4",
          icon:require("@/assets/common/zhdg.png"),
          value:"智慧灯杆",
          isChecked:true,
          source: 1
        },
        {
          id:"5",
          icon:require("@/assets/common/zhljt.png"),
          value:"智慧垃圾桶",
          isChecked:true,
          source: 1
        },
        {
          id:"6",
          icon:require("@/assets/common/szsb.png"),
          value:"水质设备",
          isChecked:true,
          source: 1
        },
        {
          id:"7",
          icon:require("@/assets/common/kqzsb.png"),
          value:"空气站设备",
          isChecked:true,
          source: 1
        },
        {
          id:"8",
          icon:require("@/assets/common/sdlr.png"),
          value:"手动录入",
          isChecked:true,
          source: 0
        },
        {
          id:"9",
          icon:require("@/assets/common/other.png"),
          value:"其他预警",
          isChecked:true
        }
      ],
      initTimer: null,
      debounceTimer: null,
      levelChangeTimer: null
    }
  },
  computed: {
    ...mapGetters(['levelCheckList']),
    otherWarningItem() {
      return this.list[this.list.length - 1]
    }
  },
  mounted() {
    this.Warning_HISTORY(this.list.filter(item => item.isChecked && item.source !== undefined))

    //保证地图初始化完成后调用
    this.initTimer = MapInitTimer(this.EmitCheckChange.bind(this), this.triggerShowOtherWarning.bind(this,this.otherWarningItem));
  },
  methods: {
    ...mapMutations('mapStatus', ['Warning_HISTORY']),
    checked(item) {
      item.isChecked = !item.isChecked
      this.Warning_HISTORY(this.list.filter(item => item.isChecked && item.source !== undefined))
      if (item.source !== undefined) {
        this.$emit('warningCheckChange')
      } else {
        //其他预警单独处理
        this.triggerShowOtherWarning(item)
      }
    },
    //其它预警点位展示
    async showOtherWarning(item) {
      this.clearOtherWarning()
      if (item.isChecked) {
        try {
          const levelList = this.levelCheckList.map(item => item.value)
          //获取人员预警点位并上图
          const res = await getPeopleWarnList({level: levelList})
          if (res.data && res.data.data.length > 0) {
            const list = res.data.data.map(item => ({
                feature: JSON.parse(item.mapData),
                level: item.level,
                title: item.devId,
                name: item.pkName,
                time: item.warnTime,
                content: item.describe,
                lng: item.lon,
                lat: item.lat,
                levelDescribe: this.$parent.getLabel(this.$parent.levelList, item.level),
                type: "peopleWarn",
                icon: `/map/img/其它预警-${item.level}.png`,
                layerid: `OtherWarningList-其它预警-${item.level}`
            }));
            const groupArr = Object.entries(this.$parent.groupBySource(list, "layerid"));
            groupArr.forEach(([layerId, pointData]) => {
              const icon = pointData[0].icon;
              this.$EventBus.$emit('CommonDrawPoint', pointData, layerId, icon, true, true, true);
            });
          }
        } catch (error) {
          console.error('Failed to get OtherWarning map point layer:', error);
          // 可以在这里添加错误处理逻辑，比如用户提示等
        }
      }
    },
    //清除其它预警点位
    clearOtherWarning() {
      // 确保 top.mapUtil 和 top.mapUtil.layers 是可用的
      if (!top || !top.mapUtil || typeof top.mapUtil.layers !== 'object') {
        console.error('mapUtil or mapUtil.layers is not available');
        return;
      }

      // 使用常量定义正则表达式，以提高代码的可维护性
      const mapWarningLayerRegex = /OtherWarningList/;

      // 获取所有层的键，并筛选出匹配 MapWarningList 的层
      const layerKeys = Object.keys(top.mapUtil.layers).filter(item => mapWarningLayerRegex.test(item));

      // 检查是否有匹配的层
      if (layerKeys.length === 0) {
        console.log('No warning layers found to clean');
        return;
      }

      // 安全地移除匹配的层
      try {
        top.mapUtil.removeAllLayers(layerKeys);
      } catch (error) {
        console.error('Failed to remove warning layers:', error);
      }

      //清除人员预警点位点击产生的区块图层
      this.$EventBus.$emit('clearPolygon')
    },
    // 手动实现的防抖函数 避免频繁勾选时调用showOtherWarning重复上图
    triggerShowOtherWarning(item) {
      clearTimeout(this.debounceTimer); // 清除之前的计时器
      this.debounceTimer = setTimeout(() => {
        this.showOtherWarning(item);
      }, 1500); // 设置新的延迟调用
    },
    //发射勾选事件变化事件
    EmitCheckChange() {
      this.$emit('warningCheckChange')
    }
  },
  watch: {
    levelCheckList() {
      //由于第一次初始化时预警级别发射了更新levelCheckList的事件更新了值所以初始化时会走进这个watch 所以需要使用MapInitTimer保证地图初始化完成后执行triggerShowOtherWarning
      this.levelChangeTimer = MapInitTimer(this.triggerShowOtherWarning.bind(this,this.otherWarningItem));
    }
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.initTimer) {
      clearInterval(this.initTimer);
    }
    if (this.debounceTimer) {
      clearInterval(this.debounceTimer);
    }
    if (this.levelChangeTimer) {
      clearInterval(this.levelChangeTimer);
    }
  },
  destroyed() {
    this.clearOtherWarning()
  }
}
</script>

<style scoped lang="less">
  .selectBox {
    width: 452px;
    height: 756px;
    background: url("~@/assets/warnningCenter/box-bg.png") no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .selectBox-title {
      width: 432px;
      height: 65px;
      background: url("~@/assets/warnningCenter/box-title.png") no-repeat;
      background-size: cover;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 32px;
      color: #FFFFFF;
      line-height: 40px;
      letter-spacing: 1px;
      text-shadow: 0px 10px 20px rgba(0,0,0,0.4);
      text-align: left;
      font-style: normal;
      text-transform: none;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 20px;
    }
    .selectBox-container {
      width: fit-content;
      height: 691px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      overflow-y: scroll;
      .selectBox-container-item {
        width: 400px;
        height: 100px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin: 15px 0 15px 0;
        .icon {
          width: 67px;
          height: 67px;
        }
        .name {
          width: 200px;
          margin-left: 44px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32px;
          color: #FFFFFF;
          line-height: 40px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }
  }
</style>