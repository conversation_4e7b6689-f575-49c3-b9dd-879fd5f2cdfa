<!--案件趋势-->
<template>
  <div id="ajqsChart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: "1月",
          value1: 23,
          value2: 18,
        },
        {
          name: "2月",
          value1: 17,
          value2: 16,
        },
        {
          name: "3月",
          value1: 16,
          value2: 14,
        },
        {
          name: "4月",
          value1: 24,
          value2: 19,
        },
        {
          name: "5月",
          value1: 18,
          value2: 16,
        },
        {
          name: "6月",
          value1: 17,
          value2: 14,
        },
        {
          name: "7月",
          value1: 17,
          value2: 14,
        }
      ],
      seriesOne: '一般公共预算收入',
      seriesTwo: '一般公共预算支出',
      myChart:undefined
    }
  },
  mounted () {
    this.initChart()
  },
  methods: {
    initChart () {
      this.myChart = this.$echarts.init(document.getElementById("ajqsChart"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          icon: "circle",
          itemGap: 45,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
          right: 120,
          top: 30
        },
        grid: {
          left: "8%",
          right: "6%",
          top: "20%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => (item.name)),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
                padding: [8, 0, 0, 0],
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：亿元",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 30,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",

              },
            },
          },
        ],
        series: [
          {
            name: this.seriesOne,
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#CBF2FF",
                  },
                  {
                    offset: 0.5,
                    color: "#00C0FF",
                  },
                  {
                    offset: 1,
                    color: "#004F69",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value1),
          }, {
            name: this.seriesTwo,
            type: "bar",
            barWidth: "20",
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#FFFECB",
                  },
                  {
                    offset: 0.5,
                    color: "#FFFC00",
                  },
                  {
                    offset: 1,
                    color: "#696000",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value2),
          }
        ],
      }
      this.myChart.setOption(option)
      this.myChart.getZr().on('mousemove', param => {
        this.myChart.getZr().setCursorStyle('default')
      })
    }
  }
}
</script>

<style scoped lang="less">
#ajqsChart {
  width: 100%;
  height: 100%;
}
</style>