<template>
  <div class="container">
    <Ktitle titleText="应用分类" showDate :width="'3215'">
      <div class="setImg" @click="popShow = !popShow"></div>
    </Ktitle>

    <div class="appTabList">
      <div class="appTab-item" v-for="(item,i) in tabList" :key="i" :style="{background: 'url(' + item.img + ') no-repeat'}">
        <div class="appTab-item-info">
          <div class="text">{{item.name}}</div>
          <div class="value">{{item.value}} <div class="unit">{{item.unit}}</div> </div>
        </div>
      </div>
    </div>
    <div class="tabelWrap">
      <div class="header">
        <div class="txtClass xhClass">序号</div>
        <div class="txtClass aClass">系统名称</div>
        <div class="txtClass bClass">建设单位</div>
        <div class="txtClass cClass">系统类别</div>
        <div class="txtClass dClass">应用类型</div>
        <div class="txtClass eClass">项目类型</div>
        <div class="txtClass fClass">获奖情况</div>
      </div>
      <div class="tablebody">
        <div class="tableLine" v-for="(item,i) in tableContainer" :key="i" @click="openPage(item)">
          <div class="txtClass xhClass">{{i + 1}}</div>
          <div class="txtClass aClass">{{item.xtmc}}</div>
          <div class="txtClass bClass">{{item.jsdw}}</div>
          <div class="txtClass cClass">{{item.xtlb}}</div>
          <div class="txtClass dClass">{{item.yylx}}</div>
          <div class="txtClass eClass">{{item.xmlx}}</div>
          <div class="txtClass fClass">{{item.hjqk}}</div>
        </div>
      </div>
    </div>
    <div class="panel" v-show="popShow" >
      <resourceAllocation :list="tableContainer" @change="listChange" labelKey="xtmc" :max-check-size="4"/>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import resourceAllocation from "@/components/ResourceAllocation";

export default {
  data () {
    return {
      popShow: false,
      tableTitle: ['序号', '系统名称', '建设单位', '系统类别', '应用类型', '项目类型', '获奖情况'],
      tableContainer: [
        {
          xtmc: "物联平台",
          jsdw: "",
          xtlb: "",
          yylx: "",
          xmlx: "",
          hjqk: "",
          url: "http://**************:31234/dplus/view/1689515108750671873?token=login:token:cri4dbm0uvkaynjgx858j1xvdyrm29@admin",
          checked: true
        },
        {
          xtmc: "智慧应急监测预警系统",
          jsdw: "",
          xtlb: "",
          yylx: "应急管理",
          xmlx: "",
          hjqk: "",
          url: "http://zhyjgl.kfq.jinhua.gov.cn:9000/emergencyMap",
          checked: false
        },
        {
          xtmc: "金华开发区社会治理“微智治”",
          jsdw: "开发区",
          xtlb: "",
          yylx: "社会治理",
          xmlx: "",
          hjqk: "",
          url: "http://zh.kfq.jinhua.gov.cn:85/screen/#/overview",
          checked: true
        },
        {
          xtmc: "平安法治小区",
          jsdw: "开发区",
          xtlb: "",
          yylx: "社会治理",
          xmlx: "",
          hjqk: "",
          url: "https://szfz.kfq.jinhua.gov.cn:5443/dp/#/login",
          checked: false
        },
        {
          xtmc: "智慧城管项目",
          jsdw: "",
          xtlb: "",
          yylx: "城市管理",
          xmlx: "",
          hjqk: "",
          url: "https://zhcg.kfq.jinhua.gov.cn/jkq_screen/#/jurisdiction",
          checked: false
        },
        {
          xtmc: "金华开发区文物安全智治模块项目",
          jsdw: "开发区",
          xtlb: "",
          yylx: "文化旅游",
          xmlx: "",
          hjqk: "",
          url: "https://wbzz.kfq.jinhua.gov.cn/sso/login",
          checked: false
        },
        {
          xtmc: "金华开发区数字探访关爱系统",
          jsdw: "开发区",
          xtlb: "",
          yylx: "社会治理",
          xmlx: "",
          hjqk: "",
          url: "http://wsld.jinhua.gov.cn/szdp/",
          checked: false
        },
        {
          xtmc: "全生命周期项目管理",
          jsdw: "",
          xtlb: "",
          yylx: "/",
          xmlx: "",
          hjqk: "",
          url: "https://tzxm.kfq.jinhua.gov.cn/JSC/#/index",
          checked: true
        },
        {
          xtmc: "金华健康生物产业园数字化平台项目",
          jsdw: "",
          xtlb: "",
          yylx: "公共服务",
          xmlx: "",
          hjqk: "",
          url: "https://jkswcyy.kfq.jinhua.gov.cn/iip/free/ssoLogin?username=admin&password=Jkswcyy@4202",
          checked: false
        },
        {
          xtmc: "睦家园",
          jsdw: "",
          xtlb: "",
          yylx: "公共服务、社会治理",
          xmlx: "",
          hjqk: "",
          url: "http://mjy.kfq.jinhua.gov.cn/#/cockpitIndex",
          checked: true
        },
        {
          xtmc: "金华经济技术开发区智慧应急监测预警系统",
          jsdw: "",
          xtlb: "",
          yylx: "公共服务、社会治理",
          xmlx: "",
          hjqk: "",
          url: "http://zhyjgl.kfq.jinhua.gov.cn:9000/openView?u=15068062875&p=" + this.base64Encode('Jhyj@2024-'+ Date.now()),
          checked: false
        },
        {
          xtmc: "金华开发区小微企业园智慧管理服务平台",
          jsdw: "开发区",
          xtlb: "",
          yylx: "/",
          xmlx: "",
          hjqk: "",
          checked: false
        },
        {
          xtmc: "金华新能源及高端装备制造产业园",
          jsdw: "",
          xtlb: "",
          yylx: "",
          xmlx: "",
          hjqk: "",
          url: "http://124.221.90.194/",
          checked: false
        },
      ],
      tabList: [
        {
          img:require("@/assets/application/csgl.png"),
          name:"城市管理",
          value:"1",
          unit:"个"
        },
        {
          img:require("@/assets/application/shzl.png"),
          name:"社会治理",
          value:"4",
          unit:"个"
        },
        {
          img:require("@/assets/application/whly.png"),
          name:"文化旅游",
          value:"1",
          unit:"个"
        },
        {
          img:require("@/assets/application/ggfw.png"),
          name:"公共服务",
          value:"2",
          unit:"个"
        },
        {
          img:require("@/assets/application/yjgl.png"),
          name:"应急管理",
          value:"1",
          unit:"个"
        }
      ]
    }
  },
  components: {
    Ktitle,
    resourceAllocation
  },
  methods: {
    openPage(item) {
      item.url?window.open(item.url):false
    },
    listChange(res) {
      this.$nextTick(() => {
        this.$bus.$emit("appList",res)
      })
    },
    //Base64加密方法
    base64Encode(input) {
      return btoa(unescape(encodeURIComponent(input)));
    }
  }
}
</script>

<style scoped lang="less">
.txtClass {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.setImg {
  width: 68px;
  height: 67px;
  background: url("~@/assets/Command/peizhi.png");
  background-size: cover;
  cursor: pointer;
}
.panel {
  width: 280px;
  position: absolute;
  top: 1020px;
  left: 5060px;
}
.xhClass {
  width: 110px;
  text-align: center;
  // background-color: aqua;
}
.aClass {
  width: 730px;
  // background-color: chocolate;
}
.bClass {
  width: 480px;
  // background-color: chartreuse;
}
.cClass {
  width: 480px;
  // background-color: aqua;
}
.dClass {
  width: 480px;
  // background-color: chartreuse;
}
.eClass {
  width: 480px;
  // background-color: aqua;
}
.fClass {
  width: 360px;
  // background-color: chartreuse;
}
.container {
  width: 100%;
  .appTabList {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin: 16px 0 24px 0;
    .appTab-item {
      width: 434px;
      height: 161px;
      background-size: cover;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .appTab-item-info {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: flex-start;
        margin: 20px 0 0 171px;
        .text {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 32px;
          color: #FFFFFF;
        }
        .value {
          font-family: BN;
          font-weight: 400;
          font-size: 60px;
          color: #FFFFFF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .unit {
            font-size: 30px;
          }
        }
      }
    }
  }
  .tabelWrap {
    width: 100%;
    .header {
      width: 100%;
      height: 50px;
      background-color: #00396f;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 30px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #77b3f1;
    }
    .tablebody {
      width: 100%;
      height: 655px;
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      .tableLine {
        width: 100%;
        height: 72px;
        font-size: 34px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #d6e7f9;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
        cursor: pointer;
      }
      .tableLine:nth-child(odd) {
        background-color: #0c2441;
      }
      /* 偶数行 */
      .tableLine:nth-child(even) {
        background-color: #0f2b4d;
      }
    }
  }
}
</style>