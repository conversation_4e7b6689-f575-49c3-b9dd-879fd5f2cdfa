<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left">
        <eventCenterLeft class="animate__animated animate__fadeInLeft"></eventCenterLeft>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right">
        <eventCenterRight class="animate__animated animate__fadeInRight"></eventCenterRight>
      </wrapbox>
    </div>
    <div class="animate__animated animate__fadeInUp midBottom">
      <MidBottom />
    </div>
<!--    <div class="animate__animated animate__fadeInDown midTop">-->
<!--      <div class="txt_box" @click="changeTab(0)">-->
<!--        <div class="txt" :class="tabIndex == 0 ? 'txt_on' : ''">日</div>-->
<!--        <img class="on" v-if="tabIndex == 0" src="@/assets/eventTaskCenter/mid_top_icon_on.png" alt="" />-->
<!--      </div>-->
<!--      <div class="txt_box" @click="changeTab(1)">-->
<!--        <div class="txt" :class="tabIndex == 1 ? 'txt_on' : ''">月</div>-->
<!--        <img class="on" v-if="tabIndex == 1" src="@/assets/eventTaskCenter/mid_top_icon_on.png" alt="" />-->
<!--      </div>-->
<!--      <div class="txt_box" @click="changeTab(2)">-->
<!--        <div class="txt" :class="tabIndex == 2 ? 'txt_on' : ''">总</div>-->
<!--        <img class="on" v-if="tabIndex == 2" src="@/assets/eventTaskCenter/mid_top_icon_on.png" alt="" />-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import eventCenterLeft from '@/pages/EventTaskCenter/eventTaskCenter/eventTaskCenterLeft'
import eventCenterRight from '@/pages/EventTaskCenter/eventTaskCenter/eventTaskCenterRight'
import MidBottom from './eventTaskCenter/MidBottom.vue'
import {MapInitTimer} from "@/utils";
export default {
  name: 'index',
  data() {
    return {
      //tab
      tabIndex: 0,
      initTimer: null,
    }
  },
  components: {
    eventCenterLeft,
    eventCenterRight,
    wrapbox,
    MidBottom,
  },
  computed: {},
  mounted() {

  },
  methods: {
    changeTab(idx) {
      this.tabIndex = idx
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .left {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    .right {
      width: 2097px;
      height: 1927px;
      background: linear-gradient(180deg, #0e1a40, #064069);
    }
    .gzsbBtn {
      width: 60px;
      height: 260px;
      padding: 36px 0 24px 24px;
      margin: 158px 13px 0 0;
      cursor: pointer;
      font-size: 38px;
      font-family: FZZhengHeiS-DB-GB;
      font-weight: 400;
      color: #feffff;
      background: url('~@/assets/common/gzsbbtn.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .midBottom {
    position: absolute;
    left: 2210px;
    bottom: 66px;
    z-index: 2;
  }
  .eventTaskCenter-midTop {
    position: fixed;
    left: 3317px;
    top: 153px;
    z-index: 1000;
    width: 1047px;
    height: 82px;
    background: url('~@/assets/eventTaskCenter/mid_top_bkg.png') 0 0 no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-content: center;
    align-items: center;
    .txt_box {
      flex: 1;
      text-align: center;
      position: relative;
      cursor: pointer;
      .on {
        width: 110px;
        height: 42px;
        position: absolute;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: -1;
      }
      .txt {
        position: relative;
        z-index: 1;
        font-family: Source Han Sans CN;
        font-weight: bold;
        font-size: 32px;
        color: #d1d6df;
        line-height: 64px;
        background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
        opacity: 0.57;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        .txt_on {
          font-family: Source Han Sans CN;
          font-weight: bold;
          font-size: 32px;
          color: #ffffff;
          line-height: 64px;
        }
      }
    }
  }
}
</style>
