<template>
  <div id="scztzlChart"></div>
</template>

<script>
export default {
  data () {
    return {
      chartsData: [
        {
          name: '企业总量',
          value: 21022,
        },
        {
          name: '个体户总量',
          value: 37197,
        },
        {
          name: '农村专业\n合作社总量',
          value: 22500,
        }
      ],
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initChartsPie()
    })
  },
  methods: {
    initChartsPie () {
      let that = this
      let myChart = this.$echarts.init(document.getElementById('scztzlChart'))
      let imgUrl = require('@/assets/common/piebg.png')
      let option = {
        color: [
          '#00C0FF',
          '#22E8E8',
          '#FFC460',
          '#CAD55D',
          '#B76FD8',
          '#FD852E',
          '#0594C3',
          '#009D9D',
          '#A47905',
        ],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '50%',
          top: '24%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 22,
          textStyle: {
            rich: {
              name: {
                fontSize: 36,
                color: '#D6E7F9',
                padding: [0, 20, 0, 15],
                lineHeight: 50,
              },
              value: {
                fontSize: 36,
                color: '#FFC769'
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total
            var p = ((tarValue / total) * 100).toFixed(2)
            // return '{name|' + name + '} \n \n {value|' + p + '%}'
            return '{name|' + name + '}  {value|' + tarValue + '户}'
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'logo',
            left: '3%',
            top: '5%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50,50], //中心点
            scale: [1.4, 1.4], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['66%', '93%'],
            center: ['23%', '50.5%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: false,
            },
            data: this.chartsData,
          },
        ],
      }
      myChart.setOption(option)
    },
  }
}
</script>

<style scoped lang="less">
#scztzlChart {
  width: 100%;
  height: 100%;
  // background-color: blueviolet;
}
</style>