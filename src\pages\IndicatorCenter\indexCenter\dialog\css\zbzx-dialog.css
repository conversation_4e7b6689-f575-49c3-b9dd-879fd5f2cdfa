body,
html,
p {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 效果 */

.blue-color {
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

* {
  margin: 0;
  padding: 0;
}

.content {
  position: relative;
  left: 155px;
  top: 52.5px;
  width: 7370px;
  height: 1900px;
}

.l-pic-index {
  position: absolute;
  left: -2223px;
  z-index: 2;
  width: 177px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/jz.png') no-repeat;
}

.r-pic-index {
  position: absolute;
  right: -1300px;
  z-index: 2;
  width: 177px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/jz.png') no-repeat;
}

.l-pic-shadow {
  position: absolute;
  left: -2040px;
  z-index: 2;
  width: 120px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/right-shadow.png') no-repeat;
}

.r-pic-shadow {
  position: absolute;
  right: -1130px;
  z-index: 2;
  width: 120px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/left-shadow.png') no-repeat;
}

.l-bg-index {
  position: absolute;
  left: -2223px;
  z-index: 1;
  width: 3685px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/bg.png') left no-repeat;
}

.r-bg-index {
  position: absolute;
  right: -1300px;
  z-index: 1;
  width: 3685px;
  height: 1900px;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/bg.png') right no-repeat;
}

.main-index {
  /* display: none; */
  overflow: hidden;
  zoom: 1;
  position: absolute;
  left: -2215px;
  z-index: 5;
  color: #fff;
  width: 7370px;
  height: 1900px;
  font-size: 30px;
  padding: 90px 220px 90px 220px;
  box-sizing: border-box;
  /* position: relative; */
}

/* 内容样式 */

.content-box {
  width: 6545px;
  /* width: 6931px; */
  height: 1430px;
  overflow: hidden;
  margin: 0 auto;
  /* overflow: hidden; */
  /* overflow-y: auto; */
}

.move-box {
  width: auto;
  height: 100%;
  overflow: hidden;
  overflow-x: scroll;
}

.move-box::-webkit-scrollbar {
  width: 0;
  height: 0;
  transform: translateX();
}

.zbzx-dialog-left {

  height: 1430px;
  display: flex;
  justify-content: space-evenly;

}

.zbzx-dialog-box {
  /* flex: 0.2; */
  height: 100%;
  display: flex;
  /* margin: 0 150px; */
}

.part {
  height: 100%;
  padding: 20px 20px 0 30px;
  box-sizing: border-box;
}

.title-dialog {
  width: 100%;
  height: 70px;
  margin: 100px 0 100px 0px;
  font-size: 100px;
  font-weight: bold;
  line-height: 23px;
  letter-spacing: 10px;
  color: #ffffff;
  text-align: center;
  position: relative;
}

.fanhui-icon {
  display: inline-block;
  width: 71px;
  height: 68px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/fanhui.png');
  background-repeat: no-repeat;
  cursor: pointer;
  position: absolute;
  right: 66px;
}

.left-title {
  width: 130px;
  height: 100%;
  font-size: 72px;
  font-weight: bold;
  font-stretch: normal;
  line-height: 90px;
  text-align: center;
}

.left-title>i {
  display: inline-block;
  width: 130px;
  height: 115px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/qz.png');
  background-repeat: no-repeat;
}

.top-con {
  max-height: 100%;
  display: flex;
  /* overflow-y: auto; */
}

.top-con>div {
  width: 730px;
  max-height: 1400px;
  overflow: hidden;
  margin-right: 50px;
  margin-bottom: 50px;
}

.top-con>div:last-child {
  margin-bottom: 0;
}

.top-con::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: transparent !important;
}

.con-title {
  width: 100%;
  height: 80px;
  text-align: left;
  background: url('@/pages/IndicatorCenter/indexCenter/dialog/img/title-bg.png') no-repeat;
  color: white;
  line-height: 80px;
  font-weight: bold;
  margin-bottom: 20px;
  font-size: 48px;
  box-shadow: 2px 4px 0px 0px #071638;
}

.con-title-icon {
  display: inline-block;
  width: 32px;
  height: 33px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/icon.png');
  background-size: 100% 100%;
  margin: 0 10px 0 20px;
}

.con-list {
  width: 100%;
  max-height: 1180px;
  overflow-y: auto;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.con-list-item {
  font-size: 32px;
  letter-spacing: 2px;
  color: #ffffff;
  line-height: 50px;
  position: relative;
  padding: 10px 30px;
  box-sizing: border-box;
  cursor: pointer;
}

.name-div {
  flex: 1;
  padding-right: 10px;
  box-sizing: border-box;
}

.con-icon {
  width: 13px;
  height: 20px;
  background-image: url('@/pages/IndicatorCenter/indexCenter/dialog/img/jiantou1.png');
  background-size: 100% 100%;
  position: absolute;
  right: -40px;
  top: 8px;
}

/* .flex-8 {
  flex: 0.8;
}

.flex-5 {
  flex: 0.5;
} */

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.3);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(27, 146, 215, 0.5);
}

.el-carousel {}

.middle-lb-right {
  position: absolute;
  width: 180px;
  z-index: 999;
  right: 185px;
  top: 45%;
  cursor: pointer;
}

.middle-lb-left {
  position: absolute;
  width: 180px;
  z-index: 999;
  left: 180px;
  top: 45%;
  cursor: pointer;
}

.cursor {
  cursor: not-allowed;
}

/* .el-carousel__container {
  height: 1430px !important;
  width: 95%;
  margin: 0 auto;
} */

.el-carousel__item {
  display: flex !important;
  justify-content: space-around !important;
}

.el-carousel__indicators--outside {
  display: none !important;
}

/* .el-carousel__item .is-animating {
  transform: none !important
} */