<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2023-02-28 11:47:26
 * @LastEditors: wjb
 * @LastEditTime: 2025-04-14 11:35:12
-->
<template>
  <div class="container">
    <div class="inneLeft">
      <wrapbox bg="left" :width="3204">
        <indexCenterLeft class="animate__animated animate__fadeInLeft"></indexCenterLeft>
      </wrapbox>
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <wrapbox :width="1028">
        <indexCenterBottom @openDialog="openDialog"></indexCenterBottom>
      </wrapbox>
    </div>
    <div class="innerRight">
      <wrapbox bg="right" :width="3204">
        <indexCenterRight class="animate__animated animate__fadeInRight"></indexCenterRight>
      </wrapbox>
    </div>

    <!-- 弹窗 -->
    <div v-if="visible">
      <indicatorDialog :id="id" :name="name" :visible="visible" @close="visible = false"></indicatorDialog>
    </div>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import indexCenterLeft from '@/pages/IndicatorCenter/indexCenter/indexCenterLeft'
import indexCenterBottom from '@/pages/IndicatorCenter/indexCenter/indexCenterBottom'
import indexCenterRight from '@/pages/IndicatorCenter/indexCenter/indexCenterRight'
import indicatorDialog from '@/pages/IndicatorCenter/indexCenter/dialog/indicatorDialog.vue'

export default {
  name: 'index',
  data() {
    return {
      visible: false,
      id: '',
      name: '',
    }
  },
  components: {
    indexCenterLeft,
    indexCenterBottom,
    indexCenterRight,
    wrapbox,
    indicatorDialog,
  },
  computed: {},
  mounted() {},
  methods: {
    openDialog(e) {
      this.visible = true
      this.id = e.id
      this.name = e.name
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: url('~@/assets/application/dtBg.png') no-repeat center center;
  background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    left: 64px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 64px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .midBottom {
    position: absolute;
    left: 3328px;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
}
</style>