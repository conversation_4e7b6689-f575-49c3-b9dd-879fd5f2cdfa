<!--最多跑一次-->
<template>
  <div class="container">
    <TitleTwo :title-text="'最多跑一次'" :width="'3210'" />
    <div class="partOneContent">
      <div class="boxOne">
        <div class="content bgOne">
          <div class="descTxt">
            <ToText :lineOneType="3" :numType="6" :unitType="4" :text="'“一网通办”办件率'" :num="'99.92'" :unit="'%'" />
          </div>
          <div class="progressWrap">
            <el-progress :percentage="99" :stroke-width="13" :show-text="false"></el-progress>
          </div>
        </div>

      </div>
      <div class="boxOne">
        <div class="content bgTwo">
          <div class="descTxt">
            <ToText :lineOneType="3" :numType="6" :unitType="4" :text="'“一网通办”占比达标率'" :num="'97.18'" :unit="'%'" />
          </div>
          <div class="progressWrap">
            <el-progress :percentage="97" :stroke-width="13" :show-text="false"></el-progress>
          </div>
        </div>
      </div>
      <div class="boxOne">
        <div class="content bgThree">
          <div class="descTxt">
            <ToText :lineOneType="3" :numType="6" :unitType="4" :text="'政务2.0收件率'" :num="'99.94'" :unit="'%'" />
          </div>
          <div class="progressWrap">
            <el-progress :percentage="99" :stroke-width="13" :show-text="false"></el-progress>
          </div>
        </div>
      </div>
      <div class="boxOne">
        <div class="content bgFour">
          <div class="descTxt">
            <ToText :lineOneType="3" :numType="6" :unitType="4" :text="'政务2.0超期办件数占比'" :num="'0.14'" :unit="'%'" />
          </div>
          <div class="progressWrap">
            <el-progress :percentage="1" :stroke-width="13" :show-text="false"></el-progress>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TitleTwo from "@/components/titleTwo.vue"
import ToText from "@/components/ToText.vue"
export default {
  name: "zdpyc",
  components:{
    TitleTwo,
    ToText
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  .partOneContent {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-template-rows: 1fr;
    position: relative;
    top: 100px;

    // background-color: aquamarine;
    .boxOne {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .bgOne {
        background: url('~@/assets/publicService/bjl.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .bgTwo {
        background: url('~@/assets/publicService/dbl.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .bgThree {
        background: url('~@/assets/publicService/sjl.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .bgFour {
        background: url('~@/assets/publicService/zb.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .content {
        width: 542px;
        height: 231px;

        .descTxt {
          margin-top: 42px;
          margin-left: 180px;
        }

        .progressWrap {
          margin-top: 25px;
          box-sizing: border-box;
          padding: 0 34px 0 42px;
        }
      }
    }
  }
}
</style>