<!--调解成功率-->
<template>
  <div style="height: 621px">
    <titleTwo :title-text="'调解成功率'"></titleTwo>
    <div class="chart">
      <div class="info">
        <div class="value gold">{{successPercent.value}}</div>
        <div class="name">{{successPercent.name}}</div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "success",
  data() {
    return {
      successPercent:{
        name:"调解成功率",
        value:"99.93%"
      }
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .chart {
    width: 498px;
    height: 498px;
    background: url("@/assets/safe/chart.png");
    background-size: cover;
    margin: 60px 0 0 220px;
    overflow: hidden;
    .info {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      margin-top: 142px;
      .name {
        width: 252px;
        height: 48px;
        font-size: 51px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        white-space: nowrap;
      }
      .value {
        font-size: 105px;
        font-family: BN;
        font-weight: 400;
      }
    }
  }
</style>