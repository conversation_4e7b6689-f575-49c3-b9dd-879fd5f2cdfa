<template>
  <div :id="id" class="cityGreenLand-charts">
  </div>
</template>



<script>
import * as echarts from 'echarts'
import 'echarts-gl'

export default {
  components: {},
  props: {
    type: { //默认是半侧居左，true--居中
      type: Boolean,
      default: false,
    },
    options: {  // 传入的图表数据     格式： [{name: 'I类',value: 50}]
      type: Array,
      default: () => [],
      required: true
    },
    id: {  // 定义饼图id
      type: String,
      default: "id" + new Date().getTime(),
      required: true
    },
    colors: { // 定义扇形的颜色
      type: Array,
      default: () => ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#D2D2D2']
    },
    internalDiameterRatio: { //定义饼环的空心大小 0~1 0为实心
      type: Number,
      default: 0
    },
    zHeight: { //定义饼环的高度
      type: Number,
      default: 3
    },
    scaleCustom: { // 定义饼图缩放比例 值越大图越小
      type: Number,
      default: 2
    },
    legendCustom: { // 定义legend位置，间距
      type: Object,
      default: function () {
        return {
          itemWidth: 15,
          itemHeight: 15,
          itemGap: 14,
          left: '55%',
          top: '12%',
        }
      }
    }
  },
  data () {
    return {
      optionData: [],
      graphicCunstom: {
        left: "4.2%",
        top: "45%",
        z: -10,
        rotation: 0, //旋转
        origin: [50, 50], //中心点
        scale: [1.2, 1.2], //缩放
      },
      grid3DLeft: '-18%',// 饼图左偏移量
      total: null
    }
  },
  created () {
    if (this.type) {
      this.graphicCunstom = {
        left: "27.8%",
        top: "45%",
        z: -10,
        rotation: 0, //旋转
        origin: [50, 50], //中心点
        scale: [1, 1], //缩放
      },
        this.grid3DLeft = '-12%'
    }
    this.optionData = this.options


    this.total = this.options.map(item => { return item.value }).reduce(function (prev, cur, index, arr) {

      return prev + cur
    })

    this.optionData.forEach((x, y) => {
      this.optionData[y].itemStyle = {
        color: this.colors[y]
      }
    })
  },
  mounted () {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    init () {
      // 构建3d饼状图
      const myChart = echarts.init(document.getElementById(this.id))
      // 传入数据生成 option
      this.option = this.getPie3D(this.optionData, this.internalDiameterRatio)


      // 是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption
      this.option.series.push({
        name: 'pie2d',
        // type: 'pie',
        labelLine: {
          length: 10,
          length2: 10,
        },
        startAngle: 0, // 起始角度，支持范围[0, 360]。
        clockwise: false, // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
        // radius: ['100%', '100%'],
        // radius: '50%',
        // center: ['50%', '30%'],
        data: this.optionData,
        itemStyle: {
          opacity: 1,
        },
      })
      myChart.setOption(this.option)
      this.bindListen(myChart)
    },
    // 生成模拟 3D 饼图的配置项
    getPie3D (pieData, internalDiameterRatio) {
      const imgUrl = require('@/assets/common/dizuo3d.png')
      const that = this
      const series = []
      // 总和
      let sumValue = 0
      let startValue = 0
      let endValue = 0
      const legendData = []
      const k = typeof internalDiameterRatio !== 'undefined'
        ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
        : 1 / 3

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i += 1) {
        sumValue += pieData[i].value
        const seriesItem = {
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k,
          },
        }

        if (typeof pieData[i].itemStyle !== 'undefined') {
          const { itemStyle } = pieData[i]

          // eslint-disable-next-line no-unused-expressions
          typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
          // eslint-disable-next-line no-unused-expressions
          typeof pieData[i].itemStyle.opacity !== 'undefined'
            ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
            : null

          seriesItem.itemStyle = itemStyle
        }
        series.push(seriesItem)
      }
      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i += 1) {
        // console.log("aaaaaaaaaaaaaaaaaaaaaa",series[i].pieData.value)
        endValue = startValue + series[i].pieData.value

        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = this.getParametricEquation(
          series[i].pieData.startRatio,
          series[i].pieData.endRatio,
          false,
          false,
          k,
          // 扇形的高度  可以动态设置不同扇形的不同高度
          series[i].pieData.value,
        )

        startValue = endValue
        // console.log("bbbbbbbbbbbbbbbbbbbbbb",series[i])
        // legendData.push(series[i]);
      }

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      const option = {
        // animation: false,
        legend: {
          orient: 'vertical',
          // data: [{name:legendData[0]}],
          formatter: function (name) {
            let nameValue = ""
            that.optionData.forEach((item) => {
              if (item.name == name) {
                nameValue = item.value
              }
            })
            return '{a|' + name + '}{b|' + nameValue + '}{b|户}'
          },
          textStyle: {
            rich: {
              a: {
                align: 'left',
                color: '#D6E7F9',
                padding: [0, 15, 0, 5],
                fontSize: 30,
              },
              b: {
                color: '#FFC460',
                fontSize: 30,
              }
            },

          },

          icon: 'circle',
          ...that.legendCustom,

        },
        tooltip: {
          trigger: 'item',
          backgroundColor: '#1d2323',
          borderColor: '#ffffff00',
          // position: ['37%', '25%'], // tooltip固定位置
          formatter: (params) => {
            if (params.seriesName !== 'mouseoutSeries') {
              return `<div>
                  <p style="font-size:30px;color:#fff;font-weight:bold;margin:0;">${((option.series[params.seriesIndex].pieData.value / this.total)*100).toFixed(0)}%</p>
                  <p style="color:#fff;text-align:center;margin:10px 0;font-size:30px;">${params.seriesName
                }</p></div>`
            }
            return ''
          },
        },
        // x,y,z调整大小的
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1,
          max: 1,
        },
        grid3D: {
          show: false,
          boxHeight: that.zHeight, // 饼环的高度
          // top: '-8%',
          left: this.grid3DLeft,
          viewControl: {
            // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 28,
            // beta: 30,
            rotateSensitivity: 1, // 旋转
            zoomSensitivity: 0, // 缩放
            panSensitivity: 0, // 平移
            autoRotate: true, // 旋转
            distance: 150, // 整视角到主体的距离，类似调整zoom
          },
          // 后处理特效可以为画面添加高光、景深、环境光遮蔽（SSAO）、调色等效果。可以让整个画面更富有质感。
          postEffect: {
            // 配置这项会出现锯齿，请自己去查看官方配置有办法解决
            enable: false,
            bloom: {
              enable: true,
              bloomIntensity: 0.1,
            },
            SSAO: {
              enable: true,
              quality: 'medium',
              radius: 2,
            },
            // temporalSuperSampling: {
            //   enable: true,
            // },
          },
        },
        series,
        graphic: [
          {
            type: "image",
            id: "logo",
            bounding: "raw",
            ...this.graphicCunstom,
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
      }
      return option
    },

    // 获取3d丙图的最高扇区的高度
    getHeight3D (series, height) {
      series.sort((a, b) => (b.pieData.value - a.pieData.value))

      return height * 25 / series[0].pieData.value
    },

    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation (startRatio, endRatio, isSelected, isHovered, k, h) {
      let that = this
      // 计算
      const midRatio = (startRatio + endRatio) / 2
      const startRadian = startRatio * Math.PI * 2
      const endRadian = endRatio * Math.PI * 2
      const midRadian = midRatio * Math.PI * 2
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        // eslint-disable-next-line no-param-reassign
        isSelected = false
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      // eslint-disable-next-line no-param-reassign
      k = typeof k !== 'undefined' ? k : 1 / 3
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      const hoverRate = isHovered ? 1.05 : 1
      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
        },
        y (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate / that.scaleCustom
        },
        z (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u)
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
        },
      }
    },

    fomatFloat (num, n) {
      let f = parseFloat(num)
      // eslint-disable-next-line no-restricted-globals
      if (isNaN(f)) {
        return false
      }
      // eslint-disable-next-line no-restricted-properties
      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂
      let s = f.toString()
      let rs = s.indexOf('.')
      // 判定如果是整数，增加小数点再补0
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + n) {
        s += '0'
      }
      return s
    },

    bindListen (myChart) {

      // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
      const that = this
      let selectedIndex = ''
      let hoveredIndex = ''
      // 监听点击事件，实现选中效果（单选）
      myChart.on('click', (params) => {
        console.log(params)
        this.$emit("myChartClick", params)
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
        if (that.option.series[params.seriesIndex]) {
          const isSelected = !that.option.series[params.seriesIndex].pieStatus.selected
          const isHovered = that.option.series[params.seriesIndex].pieStatus.hovered
          const { k } = that.option.series[params.seriesIndex].pieStatus
          const { startRatio } = that.option.series[params.seriesIndex].pieData
          const { endRatio } = that.option.series[params.seriesIndex].pieData
          // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
          if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
            that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(that.option.series[
              selectedIndex].pieData
              .startRatio, that.option.series[selectedIndex].pieData.endRatio, false, false, k, that.option.series[
                selectedIndex].pieData
              .value)
            that.option.series[selectedIndex].pieStatus.selected = false
          }
          // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
          that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
            isSelected,
            isHovered, k, 80) // 高亮时扇形的高度
          that.option.series[params.seriesIndex].pieStatus.selected = isSelected
          // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
          // eslint-disable-next-line no-unused-expressions
          isSelected ? selectedIndex = params.seriesIndex : null
          // 使用更新后的 option，渲染图表
          myChart.setOption(that.option)
        }
      })
      // 监听 mouseover，近似实现高亮（放大）效果
      myChart.on('mouseover', (params) => {
        // 准备重新渲染扇形所需的参数
        let isSelected
        let isHovered
        let startRatio
        let endRatio
        let k
        // 如果触发 mouseover 的扇形当前已高亮，则不做操作
        if (hoveredIndex === params.seriesIndex) {

          // 否则进行高亮及必要的取消高亮操作
        } else {
          // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
          if (hoveredIndex !== '') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
            isSelected = that.option.series[hoveredIndex].pieStatus.selected
            isHovered = false
            // eslint-disable-next-line prefer-destructuring
            startRatio = that.option.series[hoveredIndex].pieData.startRatio
            // eslint-disable-next-line prefer-destructuring
            endRatio = that.option.series[hoveredIndex].pieData.endRatio
            // eslint-disable-next-line prefer-destructuring
            k = that.option.series[hoveredIndex].pieStatus.k
            // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
            that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
              isSelected,
              isHovered, k, that.option.series[hoveredIndex].pieData.value) // 35 为取消高亮时扇形的高度
            that.option.series[hoveredIndex].pieStatus.hovered = isHovered
            // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
            hoveredIndex = ''
          }
          // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
          if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
            if (that.option.series[params.seriesIndex]) {

              isSelected = that.option.series[params.seriesIndex].pieStatus.selected
              isHovered = true
              // eslint-disable-next-line prefer-destructuring
              startRatio = that.option.series[params.seriesIndex].pieData.startRatio
              // eslint-disable-next-line prefer-destructuring
              endRatio = that.option.series[params.seriesIndex].pieData.endRatio
              // eslint-disable-next-line prefer-destructuring
              k = that.option.series[params.seriesIndex].pieStatus.k
              // 对当前点击的扇形，执行高亮操作（对 option 更新）

              that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
                isSelected, isHovered, k, 220) // 高亮时扇形的高度
              that.option.series[params.seriesIndex].pieStatus.hovered = isHovered
              // 记录上次高亮的扇形对应的系列号 seriesIndex
              hoveredIndex = params.seriesIndex
            }
          }
          // 使用更新后的 option，渲染图表
          myChart.setOption(that.option)
        }
      })
      // 修正取消高亮失败的 bug
      myChart.on('globalout', () => {
        // 准备重新渲染扇形所需的参数
        let isSelected
        let isHovered
        let startRatio
        let endRatio
        let k
        if (hoveredIndex !== '') {
          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
          isSelected = that.option.series[hoveredIndex].pieStatus.selected
          isHovered = false
          // eslint-disable-next-line prefer-destructuring
          k = that.option.series[hoveredIndex].pieStatus.k
          // eslint-disable-next-line prefer-destructuring
          startRatio = that.option.series[hoveredIndex].pieData.startRatio
          // eslint-disable-next-line prefer-destructuring
          endRatio = that.option.series[hoveredIndex].pieData.endRatio
          // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
          // console.log("xxxxxxxxxxxxxxxxxxxxxxxxxxx",that.option.series[hoveredIndex].pieData.value)
          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
            isSelected,
            isHovered, k, that.option.series[hoveredIndex].pieData.value) // 取消高亮时扇形的高度
          that.option.series[hoveredIndex].pieStatus.hovered = isHovered
          // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
          hoveredIndex = ''
        }
        // 使用更新后的 option，渲染图表
        myChart.setOption(that.option)
      })
    },
  },
}
</script>
<style lang="less" scoped>
.cityGreenLand-charts {
  width: 100%;
  height: 100%;
}
</style>


