<template>
  <div class="warnningRight">
    <ktitle :titleText="'预警分析'"></ktitle>
    <VariousWarning></VariousWarning>
    <div class="topAndAnalyse">
      <departmentTop></departmentTop>
      <Analyse></Analyse>
    </div>
    <airAndWater></airAndWater>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import VariousWarning from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/VariousWarning";
import departmentTop from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/departmentTop";
import Analyse from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/Analyse";
import airAndWater from "@/pages/EarlyWarningCenter/warningCenter/warningRightComponents/airAndWater";
export default {
  name:"warningCenterRight",
  components:{
    ktitle,
    VariousWarning,
    departmentTop,
    Analyse,
    airAndWater
  }
}
</script>

<style scoped lang="less">
.warnningRight {
  margin: 80px 68px 0 68px;
  overflow: hidden;
  .topAndAnalyse {
    width: 1935.6px;
    height: 448px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 80px;
  }
}
</style>