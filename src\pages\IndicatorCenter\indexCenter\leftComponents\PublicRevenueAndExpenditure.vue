<!--经济生态-公共收支-->
<template>
  <div class="container">
      <div class="item" v-for="(item,i) in values" :key="i">
        <div class="icon" :style="{backgroundImage:'url('+ item.icon +')'}"></div>
        <div class="right">
          <div class="name">{{item.name}}</div>
          <div class="value">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
        </div>
      </div>
  </div>
</template>

<script>
export default {
  name: "PublicRevenueAndExpenditure",
  data() {
    return {
      values:[
        {
          name:"一般公共预算 收入(累计)",
          value:355786,
          icon:require("@/assets/home/<USER>/Public1.png"),
          unit:"万元"
        },
        {
          name:"一般公共预算 支出(累计)",
          value:247559,
          icon:require("@/assets/home/<USER>/Public2.png"),
          unit:"万元"
        },
        {
          name:"财政总收入(总计)",
          value:452939,
          icon:require("@/assets/home/<USER>/Public3.png"),
          unit:"万元"
        },
        {
          name:"财政总支出(总计)",
          value:370148,
          icon:require("@/assets/home/<USER>/Public2.png"),
          unit:"万元"
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 60px;
    .item {
      width: 421px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .icon {
        width: 108px;
        height: 107px;
        background-size: cover;
      }
      .right {
        margin-left: 9px;
        .name {
          width: 197px;
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #FFFFFF;
          word-break: break-all;
        }
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .unit {
          font-size: 30px;
          margin: 0 0 0 -10px;
        }
      }
    }
  }
</style>