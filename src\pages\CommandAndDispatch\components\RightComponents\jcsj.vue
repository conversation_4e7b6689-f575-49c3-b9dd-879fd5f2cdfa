<template>
  <div class="container">
    <Ktitle width="939.6" :titleText="'监测数据'" :show-date="false">
      <div class="setImg" @click="popShow = !popShow"></div>
    </Ktitle>
    <div class="container-content">
      <div class="container-content-scroll">
        <div class="container-content-child" v-for="(item,i) in jcsjList" :key="i">
          <div class="container-content-child-top">
            <div class="number">{{ item.value }}</div>
            <div class="unit">条</div>
          </div>
          <div class="container-content-child-bottom">{{item.label}}</div>
        </div>
      </div>
    </div>
    <div class="panel">
      <resourceAllocation :list="List" @change="listChange" v-show="popShow" labelKey="label" />
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import resourceAllocation from "@/components/ResourceAllocation";
export default {
  name: "jcsj",
  data() {
    return {
      List: [
        {
          label:"泵站",
          value:"98",
          checked:true
        },
        {
          label:"车次数",
          value:"1200",
          checked:true
        },
        {
          label:"地下水站",
          value:"40",
          checked:true
        },
        {
          label:"分洪水位",
          value:"50",
          checked:true
        }
      ],
      jcsjList: [],
      popShow: false
    }
  },
  components: {
    Ktitle,
    resourceAllocation
  },
  computed: {},
  mounted() {

  },
  methods: {
    listChange(res) {
      this.jcsjList = res
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  ::-webkit-scrollbar-thumb {
    background-color: #1C92D9;
    -webkit-border-radius: 6px;
    border-radius: 6px;
  }
  ::-webkit-scrollbar {
    width: 14px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    border-radius: 6px;
    background-color: #10213E;
  }
  .setImg {
    width: 68px;
    height: 67px;
    background: url("~@/assets/Command/peizhi.png");
    background-size: cover;
    cursor: pointer;
  }
  .panel {
    position: fixed;
    top: 135px;
    left: 1749px;
  }
  .container-content {
    width: 939.6px;
    height: 370px;
    overflow-x: scroll;
    .container-content-scroll {
      width: fit-content;
      height: 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .container-content-child {
        flex-shrink: 0;
        width: 262px;
        height: fit-content;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: column;
        .container-content-child-top {
          width: 100%;
          height: 263px;
          background: url("~@/assets/Command/jcsjBg.png") no-repeat;
          background-size: cover;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          flex-direction: column;
          .number {
            font-family: BN;
            font-weight: 400;
            font-size: 54px;
            color: #FFFFFF;
            background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-top: 80px;
            margin-left: 20px;
          }
          .unit {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 30px;
            color: #FFFFFF;
            margin-top: 2px;
            margin-left: 20px;
          }
        }
        .container-content-child-bottom {
          font-family: Source Han Sans CN;
          font-weight: 500;
          font-size: 36px;
          color: #FFFFFF;
          margin-left: 20px;
          margin-bottom: 38px;
        }
      }
    }

  }
}
</style>