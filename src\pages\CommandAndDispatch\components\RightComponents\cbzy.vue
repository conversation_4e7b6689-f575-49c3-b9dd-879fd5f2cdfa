<template>
  <div class="container">
    <Ktitle :titleText="'储备资源'" :show-date="false">
      <div class="setImg" @click="popShow = !popShow"></div>
    </Ktitle>
    <div class="container-content">
      <div class="container-content-child" v-for="(item,i) in cbzyList" :key="i" :style="{marginRight:i != cbzyList.length - 1? '56px':'0px'}">
        <div class="container-content-child-top">
          <div class="container-content-child-top-left" @click="pointOnMap(item)" :style="{backgroundImage:'url('+item.icon+')'}"></div>
          <div class="container-content-child-top-right" @click="showDetail(item)">
            <div class="text">{{item.name}}</div>
            <div class="value">{{item.value}} <div class="unit">{{item.unit}}</div></div>
          </div>
        </div>
        <div class="container-content-child-list">
          <div class="container-content-child-list-child" v-for="(child,j) in item.childList" :key="j">
            <div class="left" :title="child.name">{{child.name}}</div>
            <div class="right"> {{child.value}} <div class="unit">{{child.unit}}</div> </div>
          </div>
        </div>
      </div>
    </div>
    <div class="panel">
      <resourceAllocation :list="List" @change="listChange" v-show="popShow" labelKey="name" />
    </div>
    <Commondialog :title="dialogTitle" :dialogFlag="showDialog" @close="showDialog = false" dialogWidth="1700px">
      <CommonDialogTable :show-index="true" :title-with-key="titleList" :table-data="tableData" :width="1600" :height="730"></CommonDialogTable>
    </Commondialog>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
import resourceAllocation from "@/components/ResourceAllocation";
import Commondialog from "@/components/Commondialog";
import CommonDialogTable from "@/components/CommonDialogTable";
import {getCbzyList, getCbzyTj} from "@/api/command";
export default {
  name: "cbzy",
  data() {
    return {
      List: [],
      cbzyList: [],
      popShow: false,
      tableData: [],
      titleList:[],
      dialogTitle:"",
      showDialog:false
    }
  },
  components: {
    Ktitle,
    resourceAllocation,
    Commondialog,
    CommonDialogTable
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getCbzyTj().then(res => {
        this.List = res.data.data.map(item => ({
          name:item.name,
          value:item.num,
          unit:item.unit,
          icon:this.getIcon(item.name),
          childList: item.list.map(obj => ({
            name:obj.name?obj.name:"-",
            value:obj.num?obj.num:"-",
            unit:obj.unit?obj.unit:"-"
          })),
          checked:true
        }))
      })
    },
    getIcon(type) {
      switch (type) {
        case "应急救援队伍":
          return require("@/assets/Command/ryllBg.png")
        case "应急救援物资":
          return require("@/assets/Command/wz.png")
        case "应急专家":
          return require("@/assets/Command/zj.png")
        case "应急装备":
          return require("@/assets/Command/zb.png")
        case "避难场所":
          return require("@/assets/Command/cs.png")
      }
    },
    listChange(res) {
      this.cbzyList = res
    },
    showDetail(item) {
      this.dialogTitle = item.name + "详情"
      this.getTableKeyList(item.name)
      getCbzyList({pageSize: 1000,pageNum: 1,type: item.name}).then(res => {
        this.tableData = res.data.rows
        this.tableData.forEach((obj,i) => {obj.index = i + 1})
        this.showDialog = true
      })
    },
    //点位上图
    pointOnMap(item) {
      const that = this;
      if (item.name == "应急专家" || item.name == "应急救援队伍" || item.name == "避难场所") {
        if (top.mapUtil.layers[item.name + "MarkerArray"]) {
          top.mapUtil.removeLayer(item.name + "MarkerArray")
        } else {
          getCbzyList({pageSize: 1000,pageNum: 1,type: item.name}).then(res => {
            let pointData = res.data.rows.map(obj => ({
              type: "cbzy",
              lng:obj.lon.trim(),
              lat:obj.lat.trim(),
              level: obj.level?obj.level:"-",
              town: obj.town?obj.town:"-"
            }))
            this.$EventBus.$emit('CommonDrawPoint', pointData, item.name + "MarkerArray", that.getPointIcon(item.name), true, false, true);
          })
        }
      }
    },
    //获取不同类型点位图标
    getPointIcon(type) {
      switch (type) {
        case "避难场所":
          return "/map/img/避难场所.png"
        case "应急专家":
          return "/map/img/应急专家.png"
        case "应急救援队伍":
          return "/map/img/应急救援.png"
      }
    },
    getTableKeyList(type) {
      switch (type) {
        case "应急救援队伍":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"名称",
              key:"name",
              flex: 3
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 2
            },
            {
              label:"地址",
              key:"address",
              flex: 3
            },
            {
              label:"队长姓名",
              key:"dzName",
              flex: 1.5
            },
            {
              label:"队长联系方式",
              key:"dzPhone",
              flex: 2
            },
            {
              label:"队员人数",
              key:"dyNum",
              flex: 1.5
            }
          ]
          break;
        case "应急救援物资":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"物资名称",
              key:"name",
              flex: 2
            },
            {
              label:"所属分类",
              key:"fl",
              flex: 2
            },
            {
              label:"所属区划",
              key:"qh",
              flex: 2
            },
            {
              label:"类型",
              key:"lx",
              flex: 3
            },
            {
              label:"负责人",
              key:"dzName",
              flex: 1.5
            },
            {
              label:"负责人电话",
              key:"dzPhone",
              flex: 2.5
            },
            {
              label:"地址",
              key:"address",
              flex: 2
            },
            {
              label:"数量",
              key:"num",
              flex: 2
            }
          ]
          break;
        case "应急专家":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"姓名",
              key:"name",
              flex: 1
            },
            {
              label:"所在单位",
              key:"unit",
              flex: 3
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 1.8
            },
            {
              label:"单位地址",
              key:"address",
              flex: 4
            },
            {
              label:"联系方式",
              key:"phone",
              flex: 2
            }
          ]
          break;
        case "应急装备":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"名称",
              key:"name",
              flex: 2
            },
            {
              label:"所属分类",
              key:"fl",
              flex: 2
            },
            {
              label:"所属区划",
              key:"qh",
              flex: 2
            },
            {
              label:"类型",
              key:"lx",
              flex: 3
            },
            {
              label:"负责人",
              key:"dzName",
              flex: 1.8
            },
            {
              label:"负责人电话",
              key:"dzPhone",
              flex: 3
            },
            {
              label:"地址",
              key:"address",
              flex: 2
            },
            {
              label:"数量",
              key:"num",
              flex: 1
            }
          ]
          break;
        case "避难场所":
          this.titleList = [
            {
              label:"序号",
              key:"index",
              flex: 1
            },
            {
              label:"镇（街道、乡）",
              key:"town",
              flex: 2
            },
            {
              label:"级别",
              key:"level",
              flex: 1
            },
            {
              label:"场所名称",
              key:"name",
              flex: 2.5
            },
            {
              label:"场所地址",
              key:"address",
              flex: 3
            },
            {
              label:"容纳人数(人）",
              key:"accommodateNum",
              flex: 1.8
            },
            {
              label:"联系人",
              key:"lxr",
              flex: 1
            },
            {
              label:"联系电话",
              key:"phone",
              flex: 2
            }
          ]
          break;
      }
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    ::-webkit-scrollbar-thumb {
      background-color: #1C92D9;
      -webkit-border-radius: 6px;
      border-radius: 6px;
    }
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 6px;
      background-color: #10213E;
    }
    .setImg {
      width: 68px;
      height: 67px;
      background: url("~@/assets/Command/peizhi.png");
      background-size: cover;
      cursor: pointer;
    }
    .panel {
      position: fixed;
      top: 625px;
      left: 1749px;
    }
    .container-content {
      width: 1935.6px;
      height: 520px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      overflow-x: scroll;
      .container-content-child {
        flex-shrink: 0;
        width: 338px;
        height: 470px;
        cursor: pointer;
        .container-content-child-top {
          width: 100%;
          height: 101px;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .container-content-child-top-left {
            width: 120px;
            height: 101px;
            background-size: cover;
            margin-right: 10px;
            flex-shrink: 0;
          }
          .container-content-child-top-right {
            width: 359px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            .text {
              font-family: Source Han Sans CN;
              font-weight: 500;
              font-size: 32px;
              color: #FFFFFF;
            }
            .value {
              font-family: BN;
              font-weight: 400;
              font-size: 60px;
              color: #9AA9BF;
              background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              display: flex;
              justify-content: flex-start;
              align-items: center;
              .unit {
                font-size: 30px;
                margin: 7px 0 0 10px;
              }
            }

          }
        }
        .container-content-child-list {
          height: 340px;
          margin-top: 20px;
          overflow-y: scroll;
          .container-content-child-list-child {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .left {
              width: 200px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              font-size: 30px;
              color: #FEFEFE;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .right {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              font-family: BN;
              font-weight: 400;
              font-size: 48px;
              color: #9AA9BF;
              background: linear-gradient(180deg, #FFFFFF 0%, #00C0FF 50.244140625%, #FFFFFF 53.0029296875%, #CBF2FF 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              margin-right: 27px;
              .unit {
                font-size: 25px;
                margin: 10px 0 0 5px;
              }
            }
          }
        }
      }
    }
  }
</style>