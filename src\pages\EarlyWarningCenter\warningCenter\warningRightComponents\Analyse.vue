<!--监测点分析-->
<template>
  <div>
    <titleTwo :title-text="'监测点分析'"></titleTwo>
    <div class="warningCharts6" id="warningCharts6"></div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import imgUrl from "@/assets/common/piebg.png";
export default {
  name: "Analyse",
  data() {
    return {
      charts2Data:[
        {name:"安全生产危险源数量",value:118},
        {name:"基础设置监测点数量",value:128},
        {name:"城市部件监测点数量",value:228},
        {name:"物联网监测点数量",value:38}
      ],
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let that = this
      let myChart = this.$echarts.init(document.getElementById("warningCharts6"));
      let imgUrl = require('@/assets/common/piebg.png')
      let option = {
        color: ['#FFC460', '#FF4949', '#FD852E', '#B76FD8', '#A9DB52', '#22E8E8', '#00C0FF'],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '48%',
          top: '25%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 30,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15]
              },
              value: {
                fontSize: 25,
                color: '#FFC460',
                // padding: [10, 0, 0, 15]
              },
            }
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total;
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '}{value|' + tarValue + '个}'
          },
        },
        graphic: [
          {
            type: "image",
            id: "logo",
            left: "15.3%",
            top: "27.4%",
            z: -10,
            bounding: "raw",
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.4, 0.4], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: 'Radius Mode',
            type: 'pie',
            radius: [50, 160],
            center: ['25%', '50%'],
            roseType: 'radius',
            itemStyle: {
              borderRadius: 5
            },
            label: {
              show: false
            },
            emphasis: {
              label: {}
            },
            data: this.charts2Data
          }
        ]
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  watch: {}
}
</script>

<style scoped>
.warningCharts6 {
  width: 935px;
  height: 390px;
}
</style>