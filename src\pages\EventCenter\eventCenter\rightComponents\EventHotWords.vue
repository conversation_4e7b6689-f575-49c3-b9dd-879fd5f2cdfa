<template>
  <div>
    <CiYun :id="'1'" :charts-data="chartsData" :height="'960'" :width="'939.6'"></CiYun>
  </div>
</template>

<script>
import CiYun from "@/components/CiYun";
export default {
  name: "EventHotWords",
  data() {
    return {
      chartsData:[
        {
          name:"垃圾占道",
          value:30,
        },
        {
          name:"违法停车",
          value:20,
        },
        {
          name:"投诉",
          value:10,
        },
        {
          name:"政务",
          value:40,
        },
        {
          name:"城市",
          value:50,
        },
        {
          name:"交通",
          value:15,
        },
        {
          name:"公共安全",
          value:30,
        },
        {
          name:"环境保护",
          value:20,
        },
        {
          name:"园区",
          value:10,
        },
        {
          name:"消费",
          value:40,
        },
        {
          name:"健康",
          value:50,
        },
        {
          name:"金融",
          value:15,
        },
        {
          name:"物流",
          value:30,
        },
        {
          name:"旅游",
          value:20,
        },
        {
          name:"教育",
          value:10,
        },
        {
          name:"社区",
          value:40,
        },
        {
          name:"社会治理",
          value:50,
        },
        {
          name:"数据分析",
          value:15,
        },
        {
          name:"全息化管理",
          value:30,
        },
        {
          name:"互联网应用",
          value:20,
        }
      ]
    }
  },
  components:{
    CiYun
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped>

</style>