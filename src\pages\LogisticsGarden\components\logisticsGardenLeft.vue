<template>
  <div class="eventCenterLeft">
    <ktitle :titleText="'物联设备概览'"></ktitle>
    <titleTwo :title-text="'各类物联设备数量'" width="1942"></titleTwo>
    <facilityCount class="eventSituation"></facilityCount>
    <ktitle :titleText="'园区运行概览'"></ktitle>
    <div class="titlebox">
      <titleTwo class="title" :titleText="'园区物流收入分析图'" width="938"></titleTwo>
      <titleTwo :titleText="'物流车流量分析'" width="938"></titleTwo>
    </div>
    <div class="titlebox">
      <logisticsIncome></logisticsIncome>
      <logisticsFlow></logisticsFlow>
    </div>
  </div>
</template>

<script>
import ktitle from '@/components/title'
import titleTwo from '@/components/titleTwo'
import facilityCount from './leftComponents/facilityCount.vue' //各类物联设备数量
import logisticsIncome from './leftComponents/logisticsIncome.vue' // 园区物流收入分析图
import logisticsFlow from './leftComponents/logisticsFlow.vue' //物流车流量分析
export default {
  name: 'eventCenterLeft',
  components: {
    ktitle,
    titleTwo,
    facilityCount,
    logisticsIncome,
    logisticsFlow,
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style lang="less" scoped>
.eventCenterLeft {
  margin: 80px 68px 0 68px;
  overflow: hidden;
  .eventSituation {
    height: 760px;
  }
  .eventTrend {
    height: 680px;
  }
  .titlebox {
    display: flex;
    align-items: center;
    .title {
      margin-right: 58px;
    }
  }
}
</style>
