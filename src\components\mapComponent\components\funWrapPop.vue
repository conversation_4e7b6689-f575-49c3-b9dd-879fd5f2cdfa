<template>
  <div class="popContainer">
    <div class="popContainer-title"> <span style="margin-left: 10px;">{{name}}</span> </div>
    <div class="popContainer-content">
      <!--测量工具-->
      <measure v-show="name == '测量工具'"/>
      <!--空间分析-->
      <SpatialAnalysis v-show="name == '空间分析'"/>
      <!--坐标转换-->
      <coordinate v-show="name == '坐标转换'"/>
      <!--标绘-->
      <Plot v-show="name == '标绘'"/>
      <!--路径规划-->
      <PathPlanning v-show="name == '路径规划'"/>
    </div>
  </div>
</template>

<script>
import measure from "@/components/mapComponent/MapFunComponents/measure";
import SpatialAnalysis from "@/components/mapComponent/MapFunComponents/SpatialAnalysis";
import coordinate from "@/components/mapComponent/MapFunComponents/coordinate";
import Plot from "@/components/mapComponent/MapFunComponents/Plot";
import PathPlanning from "@/components/mapComponent/MapFunComponents/PathPlanning";
export default {
  name: "funWrapPop",
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  components: {
    measure,
    SpatialAnalysis,
    coordinate,
    Plot,
    PathPlanning
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .popContainer {
    width: 500px;
    height: 1200px;
    background: linear-gradient(180deg, rgba(2, 51, 110, 0.85), rgba(8, 37, 71, 0.85));
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    .popContainer-title {
      width: 380px;
      height: 50px;
      line-height: 50px;
      font-size: 30px;
      font-weight: bolder;
      padding-left: 25px;
      background: url("~@/assets/map/header.svg") no-repeat;
      background-size: cover;
      color: white;
      margin-top: 30px;
    }
    .popContainer-content {
      width: 400px;
      height: 90%;
      margin-top: 20px;
    }
  }
</style>