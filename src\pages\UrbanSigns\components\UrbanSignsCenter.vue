<template>
  <div class="container">
    <img :src="img" alt="" class="centerPage">
    <div class="number1" @click="showDialog('安全韧性')"></div>
    <div class="number2" @click="showDialog('风貌特征')"></div>
    <div class="number3" @click="showDialog('交通便捷')"></div>
  </div>
</template>

<script>
export default {
  name: "UrbanSignsCenter",
  data() {
    return {
      img: require("@/assets/cstz/中间.png")
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    showDialog(str) {
      this.$emit('showDialog',str)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.container {
  .centerPage {
    width: 3411px;
    height: 1930px;
  }
  .number1 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 114px;
    left: 3225px;
    cursor: pointer;
  }
  .number2 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 925px;
    left: 3200px;
    cursor: pointer;
  }
  .number3 {
    width: 55px;
    height: 54px;
    position: absolute;
    top: 920px;
    left: 1520px;
    cursor: pointer;
  }
}
</style>