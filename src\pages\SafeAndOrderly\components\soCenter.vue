<template>
  <div class="SafeAndOrderlyCenter">
    <ktitle :titleText="'城市安全'" :width="'3057.5'"></ktitle>
    <div class="hazard">
      <titleTwo :title-text="'城市危险源'" :width="'3057.5'" style="margin-top: 28px;"></titleTwo>
      <hazard></hazard>
    </div>
    <div class="otherSafe">
      <SafeProduction></SafeProduction>
      <EmergencyResources></EmergencyResources>
      <Rain></Rain>
    </div>
    <ktitle :titleText="'城市消防'" :width="'3057.5'"></ktitle>
    <div class="firefighting">
      <Brigade></Brigade>
      <car></car>
      <warning></warning>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import titleTwo from "@/components/titleTwo";
import hazard from "@/pages/SafeAndOrderly/components/centerComponents/hazard";
import SafeProduction from "@/pages/SafeAndOrderly/components/centerComponents/SafeProduction";
import EmergencyResources from "@/pages/SafeAndOrderly/components/centerComponents/EmergencyResources";
import Rain from "@/pages/SafeAndOrderly/components/centerComponents/Rain";
import Brigade from "@/pages/SafeAndOrderly/components/centerComponents/Brigade";
import car from "@/pages/SafeAndOrderly/components/centerComponents/car";
import warning from "@/pages/SafeAndOrderly/components/centerComponents/warning";
export default {
  name: "soCenter",
  data() {
    return {}
  },
  components:{
    ktitle,
    titleTwo,
    hazard,
    SafeProduction,
    EmergencyResources,
    Rain,
    Brigade,
    car,
    warning
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .SafeAndOrderlyCenter {
    margin: 26px 68px 0 68px;
    overflow: hidden;
    .otherSafe {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin: 55px 0 78px 0;
    }
    .firefighting {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin: 55px 0 78px 0;
    }
  }
</style>