<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2023-02-28 11:47:26
 * @LastEditors: wjb
 * @LastEditTime: 2025-04-14 11:32:31
-->
<!--指标中心右侧页面-->
<template>
  <div class="indexCenterLeft">
    <titleTwo :title-text="'2025年度指标'" width="3000" :showDate="false"></titleTwo>
    <div class="indicators-container" v-if="listArr && listArr.length > 0">
      <!-- 第一行指标 -->
      <div class="indicator-row">
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[0].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[0].indicatorValue }}{{ listArr[0].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[0].percent }"></div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[1].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[1].indicatorValue }}{{ listArr[1].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar red-bar" :style="{ width: listArr[1].percent }"></div>
          </div>
        </div>
      </div>

      <!-- 第二行指标 -->
      <div class="indicator-row">
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[2].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[2].indicatorValue }}{{ listArr[2].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[2].percent }"></div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[3].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[3].indicatorValue }}{{ listArr[3].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[3].percent }"></div>
          </div>
        </div>
      </div>

      <!-- 第三行指标 -->
      <div class="indicator-row">
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[4].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[4].indicatorValue }}{{ listArr[4].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[4].percent }"></div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[5].indicatorName }}</div>
            <div class="indicator-value">增速{{ listArr[5].indicatorValue }}{{ listArr[5].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[5].percent }"></div>
          </div>
        </div>
      </div>

      <!-- 第四行指标 -->
      <div class="indicator-row">
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[6].indicatorName }}</div>
            <div class="indicator-value">占比{{ listArr[6].indicatorValue }}{{ listArr[6].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar yellow-bar small-bar" :style="{ width: listArr[6].percent }"></div>
          </div>
        </div>
        <div class="indicator-item">
          <div class="indicator-header">
            <div class="indicator-title">{{ listArr[7].indicatorName }}</div>
            <div class="indicator-value">{{ listArr[7].indicatorValue }}{{ listArr[7].indicatorUnit }}</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar blue-bar" :style="{ width: listArr[7].percent }"></div>
          </div>
        </div>
      </div>
    </div>
    <titleTwo :title-text="'关键指标'" width="3000" :showDate="false"></titleTwo>
    <div class="flex-end">
      <div class="fs64" @click="showCheckFlag = true" v-if="showCheckFlag == false">固定指标</div>
      <div class="btn_box" v-else>
        <el-button type="primary" @click="submitSelect">确定</el-button>
        <el-button type="info" @click="showCheckFlag = false">取消</el-button>
      </div>
    </div>
    <div class="key-indicators">
      <div class="indicator-card" v-for="(item, index) in listData" :key="index" @click="choseFourFun(item, index)">
        <div class="indicator-icon" v-if="showCheckFlag">
          <div class="icon" v-if="!item.checked">
            <img src="@/assets/img/uchecked.png" alt="" />
          </div>
          <div class="icon" v-else>
            <img src="@/assets/img/checked.png" alt="" />
          </div>
        </div>
        <div class="card-title cut_text">{{ item.indicatorName }}</div>
        <div class="card-value" :class="item.valueStatus ? 'warning' : ''">
          {{ item.indicatorValue }}
          <span class="unit">{{ item.indicatorUnit }}</span>
        </div>
        <div class="update-time">更新时间: {{ item.zbTime }}</div>
      </div>
    </div>
    <!-- 弹窗 -->
    <div v-if="visible1">
      <indicatorDetail :visible="visible1" :id="detailid" @close="visible1 = false"></indicatorDetail>
    </div>
  </div>
</template>

<script>
import titleTwo from '@/components/titleCommon.vue'
import indicatorDetail from './dialog/indicatorDetail.vue'
import { getJschqzdzb, getJscgjzb } from '@/api/IndicatorCenter'
export default {
  name: 'indexCenterLeft',
  components: {
    titleTwo,
    indicatorDetail,
  },
  data() {
    return {
      listArr: [],
      listData: [],
      showCheckFlag: false,
      //指标点击
      detailid: null,
      visible1: false,
    }
  },
  computed: {},
  mounted() {
    this.getData()
    this.getData1()
  },
  methods: {
    // 指标点击
    choseFourFun(item, index) {
      if (this.showCheckFlag) {
        this.listData[index].checked = !this.listData[index].checked
        this.$forceUpdate()
      } else {
        this.detailid = item.indicatorId
        this.visible1 = true
      }
    },
    submitSelect() {
      let arr = []
      this.listData.forEach((item) => {
        if (item.checked) {
          arr.push(item.indicatorId)
        }
      })
    },
    getData() {
      getJschqzdzb().then((res) => {
        const divisors = [7, 15, 5.5, 3, 6, 5, 2.2, 5.7]
        const resdata = res.data.data.map((item, index) => {
          const divisor = divisors[index] || 1
          return {
            ...item,
            percent: !item.indicatorValue ? 0 : `${(Number(item.indicatorValue) / divisor) * 100}%`,
          }
        })
        this.listArr = resdata
        console.log('1111', resdata)
      })
    },
    getData1() {
      getJscgjzb().then((res) => {
        this.listData = res.data.data.splice(0, 24)
        this.listData.map((item, index) => {
          return {
            ...item,
            checked: false,
          }
        })
      })
    },
  },
  watch: {},
}
</script>

<style lang="less" scoped>
.indexCenterLeft {
  margin: 80px 0 0 28px;
  overflow: hidden;
}

.indicators-container {
  margin: 60px 180px 70px;
}

.indicator-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 118px;
}

.indicator-item {
  width: 48%;
  display: flex;
  flex-direction: column;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.indicator-title {
  color: #d6e7f9;
  font-size: 40px;
  text-align: left;
}

.indicator-value {
  color: #fff;
  font-size: 40px;
  text-align: right;
}

.progress-container {
  width: 100%;
  height: 30px;
  background-color: rgba(3, 43, 91, 0.5);
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 8px;
  position: absolute;
  left: 0;
  top: 0;
}

.blue-bar {
  background: linear-gradient(to right, rgba(0, 192, 255, 0.3), rgba(0, 192, 255, 0.8));
}

.yellow-bar {
  background: linear-gradient(to right, rgba(255, 212, 132, 0.3), rgba(255, 212, 132, 0.8));
}

.red-bar {
  background: linear-gradient(to right, rgba(255, 50, 50, 0.3), rgba(255, 50, 50, 0.8));
}

.small-bar {
  width: 20%;
}

.key-indicators {
  margin: 24px 0 70px;
  display: flex;
  flex-wrap: wrap;
}

.indicator-card {
  width: 314px;
  height: 199px;
  background: url('@/assets/common/zb_bg.png') 0 0 no-repeat;
  background-size: cover;
  margin: 0 40px 44px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.indicator-icon {
  position: absolute;
  top: 0;
  right: 0;
  .icon {
    width: 30px;
    height: 30px;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
    }
  }
}

.card-title {
  color: rgba(220, 239, 255, 0.8);
  font-size: 32px;
  margin-bottom: 15px;
  width: 100%;
  text-align: center;
}

.card-value {
  color: #fff;
  font-size: 46px;
  font-weight: bold;
  margin-bottom: 15px;
}
.warning {
  color: #fd852e;
  .unit {
    color: #fd852e !important;
  }
}

.unit {
  font-size: 32px;
  font-weight: normal;
}

.update-time {
  color: rgba(220, 239, 255, 0.8);
  font-size: 28px;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  padding-right: 84px;
  .fs64 {
    font-size: 32px;
    color: #fff;
    cursor: pointer;
  }
}
</style>
