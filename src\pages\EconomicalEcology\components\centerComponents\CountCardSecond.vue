<template>
  <div class="cardWrap">
    <div class="cardInner">
      <div class="cardOne">
        <div class="titClass">规上工业总产值</div>
        <CyyxCounTo :val="'284.50'" />
        <div class="unitClass">亿元</div>
      </div>
      <div class="cardTwo">
        <div class="twoInner">
          <div class="lineOne">规上工业</div>
          <div class="lineOne">增加值</div>
          <div class="lineTwo">
            <div class="twoNum">109.6</div>
            <div class="twoUnit">亿元</div>
          </div>
        </div>
      </div>
      <div class="cardThree">
        <div class="twoInner">
          <div class="lineOne">规上工业</div>
          <div class="lineOne">销售产值</div>
          <div class="lineTwo">
            <div class="twoNum">599.6</div>
            <div class="twoUnit">亿元</div>
          </div>
        </div>
      </div>
      <div class="cardFour">
        <div class="twoInner">
          <div class="lineOne">规上工业</div>
          <div class="lineOne">销率</div>
          <div class="lineTwo">
            <div class="twoNum">98.52</div>
            <div class="twoUnit">%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CyyxCounTo from "@/pages/EconomicalEcology/components/centerComponents/CyyxCounTo.vue"

export default {
  components: {
    CyyxCounTo
  },
  data () {
    return {

    }
  },
  methods: {

  }
}
</script>

<style scoped lang="less">
.cardWrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .cardInner {
    width: 100%;
    height: 362px;
    background: url('~@/assets/economical/cardBg.png') no-repeat center center;
    background-size: 100% 100%;
    box-sizing: border-box;
    // padding: 45px 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);

    .cardOne {
      // height: 150px;
      grid-column-start: 1;
      grid-column-end: 4;
      box-sizing: border-box;
      border-bottom: 1px solid #CDD8E2;
      // border-image: linear-gradient(#0A2D54, #CDD8E2, #0A2D54) 0 10 0;
      border-image: url('~@/assets/economical/bdbottom.png') 27;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;

      .titClass {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        margin-right: 18px;
      }

      .unitClass {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        margin-left: 24px;
      }
    }

    .cardTwo {
      grid-column-start: 1;
      grid-column-end: 2;
      grid-row-start: 2;
      grid-row-end: 3;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      // align-items: center;



    }

    .cardThree {
      grid-column-start: 2;
      grid-column-end: 3;
      grid-row-start: 2;
      grid-row-end: 3;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      // align-items: center;

      // background-color: cadetblue;
    }

    .cardFour {
      grid-column-start: 3;
      grid-column-end: 4;
      grid-row-start: 2;
      grid-row-end: 3;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      // align-items: center;

      // background-color: chartreuse;
    }
  }

}

.twoInner {
  margin-top: 15px;
  width: 100%;
  display: flex;
  // justify-content: center;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  border-right: 1px solid #CDD8E2;
  border-image: url('~@/assets/economical/bdright.png') 27;

  .lineOne {
    font-size: 28px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #FFFFFF;
  }

  .lineTwo {
    font-weight: bolder;
    display: flex;
    align-items: baseline;
    background: linear-gradient(180deg, #FFFFFF 0%, #CBF2FF 50.244140625%, #00C0FF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    .twoNum {
      font-size: 50px;
    }

    .twoUnit {
      font-size: 32px;
    }
  }
}
</style>