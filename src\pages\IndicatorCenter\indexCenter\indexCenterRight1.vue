<!--指标中心右侧页面-->
<template>
  <div class="indexCenterLeft">
    <ktitle :titleText="'安全有序'" @titleClick="$router.push('/SafeAndOrderly')"></ktitle>
    <titleTwo class="csaqtitle2" :title-text="'城市安全'"></titleTwo>
    <right1 class="right1"></right1>
    <div class="yqzd">
      <titleTwo :title-text="'园区安全'"></titleTwo>
      <titleTwo :title-text="'重大危险源'"></titleTwo>
    </div>
    <div class="yqzd">
      <right2 class="right2"></right2>
      <right3 class="right3"></right3>
    </div>
    <ktitle :titleText="'公共服务'" class="ktitleCustom" @titleClick="$router.push('/PublicService')"></ktitle>
    <div class="yqzd yqzd1">
      <titleTwo :title-text="'民生服务'"></titleTwo>
      <titleTwo :title-text="'政务服务'"></titleTwo>
    </div>
    <div class="yqzd">
      <right4 class="right2"></right4>
      <right5 class="right3"></right5>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import titleTwo from "@/components/titleTwo";
import right1 from "./rightComponents/right1.vue" //城市安全
import right2 from "./rightComponents/right2.vue" //园区安全
import right3 from "./rightComponents/right3.vue" //重大危险源
import right4 from "./rightComponents/right4.vue" //民生服务
import right5 from "./rightComponents/right5.vue" //政务服务
export default {
  name: "indexCenterLeft",
  components: {
    ktitle,
    titleTwo,
    right1,
    right2,
    right3,
    right4,
    right5,
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style lang="less" scoped>
.indexCenterLeft {
  margin: 80px 0 0 68px;
  overflow: hidden;

  .csaqtitle2 {
    width: 1936px !important;
  }

  .right1 {
    width: 2082px;
    height: 397px;
  }

  .yqzd {
    display: flex;
    justify-content: space-between;

    .right2 {
      flex: 1;
    }

    .right3 {
      flex: 1;
    }
  }
  .yqzd1{
    margin-top: 10px;
  }
  .ktitleCustom{
    margin-top: 10px;
  }
}
</style>