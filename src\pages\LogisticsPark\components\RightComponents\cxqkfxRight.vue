<template>
  <div>
    <div class="cxqkfxRight2" id="cxqkfxRight2"></div>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
export default {
  name: "cxqkfxRight",
  components: {
    Ktitle
  },
  data() {
    return {
      charts2Data: []
    }
  },
  computed: {},
  mounted() {
    this.initCharts3()
  },
  methods: {
    initCharts3() {
      this.charts2Data = [
        {
          name: "5月",
          value1: 140,
          value2: 20,
        },
        {
          name: "6月",
          value1: 40,
          value2: 120,
        },
        {
          name: "7月",
          value1: 38,
          value2: 100,
        },
        {
          name: "8月",
          value1: 77,
          value2: 77,
        },
        {
          name: "9月",
          value1: 130,
          value2: 50,
        },
        {
          name: "10月",
          value1: 38,
          value2: 90,
        },
        {
          name: "11月",
          value1: 110,
          value2: 60,
        },
      ]
      let myChart = this.$echarts.init(document.getElementById("cxqkfxRight2"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
          itemWidth: 14, // 设置宽度
          itemHeight: 14, // 设置高度
          itemGap: 16, // 设置间距
          textStyle: {
            color: "#D6E7F9",
            fontSize: 25,
          },
          right:"12%",
          top:"13%"
        },
        grid: {
          left: "0%",
          right: "0%",
          top: "34%",
          bottom: "-2%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.charts2Data.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "金额",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: [10,30,20,10],
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
          {
            name: "",
            type: "value",
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "销售总额",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#00C0FF",
              },
            },
            data: this.charts2Data.map(item => item.value1),
          },
          {
            name: "总产值",
            type: "line",
            barWidth:30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#FFC460",
              },
            },
            data: this.charts2Data.map(item => item.value2),
          }
        ]
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
  right: 48px;
  top: 870px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}
.cxqkfxRight2 {
  width: 935px;
  height: 307px;
}
</style>