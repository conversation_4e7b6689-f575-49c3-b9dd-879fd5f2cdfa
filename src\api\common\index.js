import { request } from '@/utils/request'
import { requestLogin1 } from '@/utils/request1'

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: '/ywtg-api/system/dict/data/type/' + dictType,
    method: 'get',
  })
}

// 登陆
export function login(zzdCode) {
  return requestLogin1({
    url: '/ywtg-api/login',
    method: 'post',
    data: {
      zzdCode: zzdCode,
    },
  })
}
