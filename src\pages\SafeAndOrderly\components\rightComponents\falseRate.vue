<!--虚警率-->
<template>
  <div style="height: 621px">
    <titleTwo :title-text="'虚警率'"></titleTwo>
    <pie3D id="rateEcharts"  :scaleCustom="1"  :options="chartsData" :legendCustom="legend" :internalDiameterRatio="0.7" class="chart" style="margin-top: 120px"></pie3D>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import pie3D from "@/components/pie3D";
export default {
  name: "falseRate",
  data() {
    return {
      chartsData:[
        {
          value: 50,
          name: "当日报警数量"
        },
        {
          value: 1,
          name: "无效警情数量"
        }
      ],
      legend: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 100,
        left: '55%',
        top: '30%',
      }
    }
  },
  components:{
    titleTwo,
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.chart {
  width: 585px;
  height: 398px;
}
</style>