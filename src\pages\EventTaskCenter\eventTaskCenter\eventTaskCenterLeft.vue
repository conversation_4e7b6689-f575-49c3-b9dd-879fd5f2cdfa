<template>
  <div class="eventCenterLeft">
    <ktitle :titleText="'事件趋势分析'"></ktitle>
    <trend-analyse />
    <div class="box">
      <ktitle :titleText="'责任部门事件处置分析'"></ktitle>
      <div class="box-content">
        <div class="left">
          <DisposeAnalyseLeft />
        </div>
        <div class="right">
          <DisposePie />
        </div>
      </div>
    </div>
    <div class="box-type">
      <ktitle :titleText="'事件类型分析'"></ktitle>
      <div class="box-content">
        <div class="left">
          <TypeAnalyseLeft />
        </div>
        <div class="right">
          <TypeAnalyseRight />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from '@/components/title'
import titleTwo from '@/components/titleTwo'
import TrendAnalyse from './leftComponents/TrendAnalyse.vue'
import DisposeAnalyseLeft from './leftComponents/DisposeAnalyseLeft.vue'
import DisposePie from './leftComponents/DisposePie.vue'
import TypeAnalyseLeft from './leftComponents/TypeAnalyseLeft.vue'
import TypeAnalyseRight from './leftComponents/TypeAnalyseRight.vue'
export default {
    name: 'eventCenterLeft',
  components: {
    ktitle,
    titleTwo,
    TrendAnalyse,
    DisposeAnalyseLeft,
    DisposePie,
    TypeAnalyseLeft,
    TypeAnalyseRight,
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style lang="less" scoped>
.eventCenterLeft {
  margin: 80px 68px 0 68px;
  overflow: hidden;
  .eventSituation {
    height: 587px;
  }
  .eventTrend {
    height: 486px;
  }
  .titlebox {
    display: flex;
    align-items: center;
    .title {
      margin-right: 58px;
    }
  }
  .box {
    margin: 60px 0;
    .box-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        width: 1100px;
      }
      .right {
        width: 850px;
      }
    }
  }
  .box-type {
    margin: 60px 0;
    .box-content {
      display: flex;
      justify-content: space-between;
      // align-items: center;
      .left {
        width: 860px;
      }
      .right {
        // width: 900px;
      }
    }
  }
}
</style>
