<!--路径规划-->
<template>
  <div>
    <div class="ljgh-body">
      <!-- 起点/终点  -->
      <div class="ljgh-start">
        <div class="lj-icon">
          <div class="icon-nav-start" id="routeOrig" @click="startPoint"></div>
          <div class="icon-nav-end" id="routeDest" @click="endPoint"></div>
        </div>
        <div class="lj-input-box" id="ljInputBox" style="width: 574px">
          <input
            autocomplete="off"
            class="lj-input"
            id="origInput"
            placeholder="起点"
            type="text"
            @change="origChange"
          />
          <div class="point s-m-b-10">
            <input
              autocomplete="off"
              class="latlng"
              placeholder="起点经度坐标"
              type="number"
              v-model="orig[0]"
              disabled
            />
            <span class="s-font-32">,</span>
            <input
              autocomplete="off"
              class="latlng"
              placeholder="起点纬度坐标"
              type="number"
              v-model="orig[1]"
              disabled
            />
          </div>
          <input
            autocomplete="off"
            class="lj-input"
            id="destInput"
            placeholder="终点"
            type="text"
            @change="destChange"
          />
          <div class="point">
            <input
              autocomplete="off"
              class="latlng"
              placeholder="终点经度坐标"
              type="number"
              v-model="dest[0]"
              disabled
            />
            <span class="s-font-32">,</span>
            <input
              autocomplete="off"
              class="latlng"
              placeholder="终点纬度坐标"
              type="number"
              v-model="dest[1]"
              disabled
            />
          </div>
        </div>
        <!--上下切换箭头-->
        <div class="icon-updown" title="调换位置" id="ljUpdown" @click="changeStartEnd"></div>
      </div>
      <!-- 导航路线类型 -->
      <div class="ljgh-type flex-center s-font-30">
        <ul class="lj-tab s-flex s-row-between" id="routeTab">
          <li class="dw-li dwActive" data-name="常规路线" data-val="0">常规路线</li>
          <li class="dw-li" data-name="最短距离" data-val="1">最短距离</li>
          <li class="dw-li" data-name="不走高速" data-val="2">不走高速</li>
        </ul>
      </div>
      <!--  导航信息 -->
      <div class="ljgh-nav">
        <!-- 导航结果 -->
        <div class="nav-info">
          <div class="s-font-30" style="color: white">
            总里程：
            <span class="nav-tip" id="routeNavDist">0</span>
            km
          </div>
          <div class="s-font-30" style="color: white">
            所需时间：
            <span class="nav-tip" id="routeNavTime">0</span>
            分钟
          </div>
        </div>
        <div class="nav-step scrollbar-no" id="routeNavStep" style="overflow-y: scroll;height: 620px"></div>
        <div class="container-btn" @click="closeRoute">移除路径规划内容</div>
      </div>
    </div>
  </div>
</template>

<script>
import {MapInitTimer} from "@/utils";
export default {
  name: "PathPlanning",
  data() {
    return {
      isljUpdown: false,
      orig: [], // 路径规划起点/终点
      dest: [],
      routeInfoObj: null,
      routeStyle: 0,
      useInternetApi: true,
      parseOrigLonLat: true,
      timer: null
    }
  },
  computed: {},
  mounted() {
    let _this = this
    this.funTabClick({
      ele: '#routeTab>li',
      className: 'dwActive',
      callback: function (res) {
        _this.routeStyle = res.data('val')
        if (!_this.orig || !_this.dest) return
        _this.funDrivingRouteInit()
      },
    })

    //保证地图初始化完成后调用
    this.timer = MapInitTimer(this.init.bind(this));

  },
  methods: {
    init() {
      let _this = this
      // 起点地址解析
      if (_this.orig && _this.parseOrigLonLat) {
        _this.getAddressByGD({
          lon: _this.orig[0],
          lat: _this.orig[1],
          crs: 'wgs84',
          callback(res) {
            $('#origInput').val(res.formatted_address)
            // 起点图标上图
            // if (showOrigIcon) {
            _this.addOrigIcon({ lng: _this.orig[0], lat: _this.orig[1] })
            // }
          },
        })
      }
      // 终点地址
      if (_this.dest && _this.parseDestLonLat) {
        _this.getAddressByGD({
          lon: _this.dest[0],
          lat: _this.dest[1],
          callback(res) {
            $('#destInput').val(res.formatted_address)
            funDrivingRouteInit()
            // 终点图标上图
            // if (showDestIcon) {
            _this.addDestIcon({ lng: _this.dest[0], lat: _this.dest[1] })
            // }
          },
        })
      }
    },
    funTabClick(params) {
      let { ele, className = 'whiteBorderActive' } = params
      $(ele).click(function () {
        $(this).addClass(className).siblings().removeClass(className)
        params.callback($(this))
      })
    },

    closeRoute() {
      // $('#mapTool>div').removeClass('activeTool')
      $('#routeNavDist').html(0) // 总里程
      $('#routeNavTime').html(0) // 行驶时间
      $('#routeNavStep').html('')
      top.mapUtil.tool.stopPick() // 停止拾取坐标
      this.orig = []
      this.dest = [] // 终点重置
      $('#origInput').val('')
      $('#destInput').val('')
      if (this.useInternetApi) {
        this.rmDrivingRoute()
      } else {
        this.funRmRoutePlanLayer()
      }
      // this.rmDrivingRoute()
      this.isljUpdown = false // 默认false, 起点input 在上， 终点input 在下
      $('#destInput').attr('placeholder', '终点')
      $('#origInput').attr('placeholder', '起点')
    },
    startPoint() {
      top.mapUtil.tool.stopPick() // 停止获取鼠标点位
      top.mapUtil.tool.pickPoint(this.origIConClick)
    },
    endPoint() {
      top.mapUtil.tool.stopPick() // 停止获取鼠标点位
      top.mapUtil.tool.pickPoint(this.destIConClick)
    },
    // 起点/终点位置切换
    changeStartEnd() {
      if ($('#origInput').val() === '' || $('#destInput').val() === '') return
      this.isljUpdown = !this.isljUpdown
      let origLnglat = null,
        destLnglat = null
      let changeArr = ([this.orig, this.dest] = [this.dest, this.orig])
      let orig = changeArr[0]
      let dest = changeArr[1]
      let origInp = $('#origInput').val()
      let destInp = $('#destInput').val()
      if (this.isljUpdown) {
        // true
        $('#origInput').val(destInp)
        $('#destInput').val(origInp)
        $('#destInput').attr('placeholder', '起点')
        $('#origInput').attr('placeholder', '终点')
        // this.orig = dest
        // this.dest = orig
        // origLnglat = { lng: dest[0], lat: dest[1] }
        // destLnglat = { lng: orig[0], lat: orig[1] }
      } else {
        $('#origInput').val(origInp)
        $('#destInput').val(destInp)
        $('#destInput').attr('placeholder', '终点')
        $('#origInput').attr('placeholder', '起点')
        // this.orig = orig
        // this.dest = dest
        // origLnglat = { lng: orig[0], lat: orig[1] }
        // destLnglat = { lng: dest[0], lat: dest[1] }
      }
      this.orig = orig
      this.dest = dest
      origLnglat = { lng: orig[0], lat: orig[1] }
      destLnglat = { lng: dest[0], lat: dest[1] }
      this.addOrigIcon(origLnglat)
      this.addDestIcon(destLnglat)
      this.funDrivingRouteInit()
    },
    // 起点图标上图
    addOrigIcon(lnglat) {
      this.CommonRemoveLayer('zhdd_routeplan_start')
      this.loadLjghPoint_start(lnglat)
    },
    // 路径规划起点上图
    loadLjghPoint_start(lngLat = {}) {
      const config_start = {
        layerid: 'zhdd_routeplan_start',
        data: [
          {
            lng: lngLat.lng,
            lat: lngLat.lat,
          },
        ],
        iconcfg: {
          image: '/map/img/nav_start.png',
          iconSize: 0.5,
        }, //图标
        datacfg: {
          lngfield: 'lng',
          latfield: 'lat',
        },
        onclick: null,
        popcfg: {
          offset: [0, 0],
          show: false,
          dict: null,
        },
      }
      this.CommonRemoveLayer('zhdd_routeplan_start')
      top.mapUtil.loadPointLayer(config_start)
    },
    // 起点图标点击回调
    origIConClick(e) {
      this.addOrigIcon(e)
      const lngLat = [e.lng, e.lat]
      if (this.isljUpdown) {
        this.dest = lngLat
      } else {
        this.orig = lngLat
      }
      this.getAddressByGD({
        lon: lngLat[0],
        lat: lngLat[1],
        crs: 'wgs84',
        callback(res) {
          if (this.isljUpdown) {
            $('#destInput').val(res.formatted_address)
          } else {
            $('#origInput').val(res.formatted_address)
          }
        },
      })
      this.funDrivingRouteInit()
    },
    // 终点图标上图
    addDestIcon(lnglat) {
      this.CommonRemoveLayer('zhdd_routeplan_end')
      this.loadLjghPoint_end(lnglat)
    },
    // 终点路径规划上图
    loadLjghPoint_end(lngLat = {}) {
      const config_end = {
        layerid: 'zhdd_routeplan_end',
        data: [
          {
            lng: lngLat.lng,
            lat: lngLat.lat,
          },
        ],
        iconcfg: {
          image: '/map/img/nav_end.png',
          iconSize: 0.5,
        }, //图标
        datacfg: {
          lngfield: 'lng',
          latfield: 'lat',
        },
        onclick: null,
        popcfg: {
          offset: [0, 0],
          show: false,
          dict: null,
        },
      }
      this.CommonRemoveLayer('zhdd_routeplan_end')
      top.mapUtil.loadPointLayer(config_end)
    },
    // 终点图标点击回调
    destIConClick(e) {
      this.addDestIcon(e)
      const lngLat = [e.lng, e.lat]
      if (this.isljUpdown) {
        this.orig = lngLat
      } else {
        this.dest = lngLat
      }
      this.getAddressByGD({
        lon: lngLat[0],
        lat: lngLat[1],
        crs: 'wgs84',
        callback(res) {
          if (this.isljUpdown) {
            $('#origInput').val(res.formatted_address)
          } else {
            $('#destInput').val(res.formatted_address)
          }
        },
      })
      this.funDrivingRouteInit()
    },
    // 路径规划路线上图
    loadLjghLine(opts = {}) {
      const data = opts.path ?? []
      if (data.length == 0) return
      this.CommonRemoveLayer('zhdd_routeplan_line')
      top.mapUtil.loadPolylineLayer({
        layerid: 'zhdd_routeplan_line',
        lines: data,
        style: {
          width: opts.lineWidth ?? 12,
          color: opts.lineColor ?? [255, 255, 255, 1],
          isAddArrow: opts.isAddArrow ?? true, //是否展示箭头
          isArrowReverse: opts.isArrowReverse ?? false, //是否反向显示箭头
        },
      })
    },
    // 起/终点地址input反查
    origChange() {
      let this_ = this
      if ($('#origInput').val() == '') {
        // this.CommonRemoveLayer('zhdd_routeplan_line')
        this.CommonRemoveLayer('zhdd_routeplan_start') // 起点图标
        this_.orig = []
        this_.resetRoute()
        return
      }
      this_.getLocationByJh({
        keyWord: $('#origInput').val(),
        callback(res) {
          // 默认取第一个
          if (res.data.length > 0) {
            const poi = res.data[0]
            this_.orig = [Number(poi.x), Number(poi.y)]
            console.log('起点坐标' + poi.x + ',' + Number(poi.y))
            this_.addOrigIcon({ lng: Number(poi.x), lat: Number(poi.y) })
            this_.funFlyToPt(poi.x + ',' + poi.y)
            this_.funDrivingRouteInit()
          }
        },
      })
    },
    destChange() {
      let this_ = this
      if ($('#destInput').val() == '') {
        // this.CommonRemoveLayer('zhdd_routeplan_line')
        this.CommonRemoveLayer('zhdd_routeplan_end') // 终点图标
        this_.dest = []
        this_.resetRoute()
        return
      }
      this_.getLocationByJh({
        keyWord: $('#destInput').val(),
        callback(res) {
          // 默认取第一个
          if (res.data.length > 0) {
            const poi = res.data[0]
            this_.dest = [Number(poi.x), Number(poi.y)]
            this_.addDestIcon({ lng: Number(poi.x), lat: Number(poi.y) })
            this_.funFlyToPt(poi.x + ',' + poi.y)
            this_.funDrivingRouteInit()
          }
        },
      })
    },
    resetRoute() {
      if ($('#destInput').val() == '' && $('#origInput').val() == '') {
        $('#routeNavDist').html(0) // 总里程
        $('#routeNavTime').html(0) // 行驶时间
        $('#routeNavStep').html('')
        this.CommonRemoveLayer('zhdd_routeplan_line')
        top.mapUtil.tool.stopPick() // 停止拾取坐标
        return
      }
    },
    // 金华现场地址查询
    getLocationByJh(params = {}) {
      let { keyWord = '' } = params
      const _url = `${process.env.VUE_APP_BASE_API_MAP}/api2.0/solr-provider/api/data-sources/solr-search`
      const _argument = {
        geoType: 'geojson',
        operator: 'or',
        order: '',
        pageInfo: {
          current: 1,
          size: 10,
          totalSize: 0,
        },
        returnGeo: true,
        sortField: '',
        tableNames: 'dmdz',
        text: keyWord, //地址关键字
      }

      this.funSearchInfoByUrl({ url: _url, argument: _argument, type: 'post' }, function (res) {
        if (params.callback) {
          console.log('金华地址查询结果：' + res.data.length)
          params.callback(res)
        }
      })
    },
    funSearchInfoByUrl: function (params, callback) {
      let { url, argument, flag = true, type = 'get', opts } = params
      $.ajax({
        url: url,
        // headers:{"token":common.token,"ptid":common.ptid},
        data: type === 'get' ? argument : JSON.stringify(argument),
        contentType: type === 'get' ? 'application/x-www-form-urlencoded' : 'application/json; charset=UTF-8',
        dataType: 'json',
        type: type,
        async: flag,
        success: function (data) {
          callback(data)
        },
        error: function (e) {
          // console.log("funSearchInfo Ajax Error：");
          // console.log(e);
        },
      })
    },
    funFlyToPt(point, zoom = 13) {
      const points = point.split(',')
      top.mapUtil.flyTo({
        destination: points,
        zoom,
      })
    },
    //清除点位
    CommonRemoveLayer(id) {
      if (top.mapUtil.layers[id]) {
        top.mapUtil.removeLayer(id)
      }
    },
    // 清除路径规划路线+起终点
    rmDrivingRoute() {
      this.CommonRemoveLayer('zhdd_routeplan_line')
      this.CommonRemoveLayer('zhdd_routeplan_start') // 起点图标
      this.CommonRemoveLayer('zhdd_routeplan_end') // 终点图标
    }, // 移除路径规划
    funRmRoutePlanLayer(view = window.view) {
      window.ArcGisUtils.removeTiandituRoutePlanLayer(view)
    },
    // 导航路线上图
    funDrivingRouteInit() {
      let this_ = this
      if (!this.orig || !this.dest) return
      this.CommonRemoveLayer('address_default')
      // 使用互联网接口查询
      if (this_.useInternetApi) {
        this_.drivingRouteByGD({
          orig: this_.orig, //this_.isljUpdown ? this_.dest :
          dest: this_.dest, //this_.isljUpdown ? this_.orig :
          style: this_.routeStyle,
          crs: 'wgs84',
          toCrs: 'wgs84',
          callback(res) {
            this_.routeInfoObj = res
            // $('#routeNavDist').html(res.distance); // 总里程天
            $('#routeNavDist').html(Number(res.distance) / 1000) // 总里程-高德km
            $('#routeNavTime').html(Math.ceil(Number(res.duration) / 60)) // 行驶时间
            this_.funNavstepCon(res.navInfo || res.steps)
          },
        })
      } else {
        let _locationList = []
        if (this_.isljUpdown) {
          _locationList = [
            { type: 'start', x: this_.dest[0], y: this_.dest[1] },
            { type: 'end', x: this_.orig[0], y: this_.orig[1] },
          ]
        } else {
          _locationList = [
            { type: 'start', x: this_.orig[0], y: this_.orig[1] },
            { type: 'end', x: this_.dest[0], y: this_.dest[1] },
          ]
        }
        this_.funAddRoutePlanLayer({
          locationList: _locationList,
          style: this_.routeStyle,
          callback(res) {
            $('#origInput').val(res.startPOI)
            $('#destInput').val(res.endPOI)
            $('#routeNavDist').html(Number(res.pathLength) / 1000) // 总里程
            $('#routeNavTime').html(Math.ceil(Number(res.durationAll) / 60)) // 行驶时间
            this_.funNavstepCon(res.data)
          },
        })
      }
    },
    // 导航信息
    funNavstepCon(data) {
      let lis = []
      data.forEach((item) => {
        let html
        if (this.useInternetApi) {
          html = `
                              <div style="height: 77px;
                                          padding: 10px 0;
                                          background: #00407c8c;
                                          margin: 14px 0;display: flex;justify-content: flex-start;align-items: center">
                                  <div style="background: url('map/img/icon_arrowUp.png') no-repeat;
                                        background-size: 100% 100%;
                                        width: 32px;
                                        height: 32px;"></div>
                                  <div style="width: 90%;
                                              white-space: normal;
                                              word-break: break-all;
                                              overflow: hidden;
                                              background-image: linear-gradient(180deg, #fff, #80e0ff, #00c0ff);
                                              -webkit-background-clip: text;
                                              -webkit-text-fill-color: transparent;font-size: 22px">${item.strguide || item.instruction}</div>
                                                                          </div>
                          `
        } else {
          html = `
                              <div style="height: 77px;
                                          padding: 10px 0;
                                          background: #00407c8c;
                                          margin: 14px 0;display: flex;justify-content: flex-start;align-items: center">
                                  <div style="background: url('map/img/icon_arrowUp.png') no-repeat;
                                                                              background-size: 100% 100%;
                                                                              width: 32px;
                                                                              height: 32px;"></div>
                                  <div style="width: 90%;
                                              white-space: normal;
                                              word-break: break-all;
                                              overflow: hidden;
                                              background-image: linear-gradient(180deg, #fff, #80e0ff, #00c0ff);
                                              -webkit-background-clip: text;
                                              -webkit-text-fill-color: transparent;font-size: 22px">${item.strguide + ' ' + item.distance + '  米'}</div>
                              </div>
                          `
        }

        lis.push(html)
      })
      $('#routeNavStep').html(lis.join(''))
    },
    /**
     * 高德驾车路线规划
     * @description: 借助高德驾车导航，实现不同驾车策略的导航路线规划
     * @param params
     */
    drivingRouteByGD(params = {}) {
      let {
        orig = [],
        dest = [],
        mid = [],
        style = 0,
        flyTo = true,
        routeWidth = 12,
        crs = 'wgs84',
        toCrs = 'wgs84',
      } = params
      let strategy = 1 // 驾驶策略
      let origGcj // 起点
      let destGcj // 终点
      if (crs === 'wgs84') {
        origGcj = coordtransform.wgs84togcj02(Number(orig[0]), Number(orig[1]))
        destGcj = coordtransform.wgs84togcj02(Number(dest[0]), Number(dest[1]))
      } else if (crs == 'bd09') {
        origGcj = coordtransform.bd09togcj02(Number(orig[0]), Number(orig[1]))
        destGcj = coordtransform.bd09togcj02(Number(dest[1]), Number(dest[1]))
      } else {
        origGcj = orig
        destGcj = dest
      }

      switch (style) {
        case 0:
          strategy = 10
          break
        case 1:
          strategy = 2
          break
        case 2:
          strategy = 13
          break
      }
      this.routePlanByGD({
        orig: origGcj.join(),
        dest: destGcj.join(),
        strategy: strategy, // 驾驶策略
        cartype: 0, // 车辆类型
      }).then((res) => {
        const { count, route } = res
        let newPath = [] // 路线字符串
        route.paths[0].steps.forEach((item) => {
          const lonlats = item.polyline.split(';')
          lonlats.forEach((ite) => {
            const it = ite.split(',')
            let wgs
            if (toCrs === 'wgs84') {
              wgs = coordtransform.gcj02towgs84(Number(it[0]), Number(it[1]))
            } else if (toCrs === 'bd09') {
              wgs = coordtransform.gcj02tobd09(Number(it[0]), Number(it[1]))
            } else {
              wgs = it
            }
            newPath.push([Number(wgs[0]), Number(wgs[1])])
          })
        })
        this.loadLjghLine({
          path: [newPath],
          lineWidth: routeWidth,
          lineColor: [170, 255, 85, 1],
        })
        if (params.callback) {
          params.callback(route.paths[0])
        }
      })
    },
    /**
     * arcgis地图厂商-路径规划
     * @description: 规划完成， 默认 起点、终点、路线上图
     * @param{Array} params.locationList: 路线起终点信息，格式：[{
     *             type: 'start',
     *             x: 119.63836400243707,
     *             y: 29.043415155379428,
     *           },
     *           {
     *             type: 'end',
     *             x: 119.64275559776982,
     *             y: 29.08174851685754,
     *           }]
     * @param{} params.style:    出行方式，默认：0 (0: 最快路线，1： 最短路线，2：避开高速， 3： 步行)
     * @param{Number} params.queryRadius: 查询点偏差范围， 默认：100米
     * @param{String} params.lineColor: 规划路线颜色
     */
    funAddRoutePlanLayer(params = {}) {
      const { locationList = [], style = 0, queryRadius = 100, lineColor = '' } = params
      const layer = window.ArcGisUtils.addTiandituRoutePlanLayer({
        locationList: locationList,
        style: Number(style),
        queryRadius: queryRadius,
        lineColor: lineColor,
      }).then((res) => {
        if (params.callback) {
          // 异步返回字段说明
          // {
          //     "startPOI": "金华市体育中心体育场",//起始POI
          //   "endPOI": "金华市人大常委会办公室",//终点POI
          //
          //   "data": [
          //     {
          //         "distance": 378,//当前道路长
          //         "streetName": "当前道路",
          //         "strguide": "从出发向东南沿当前道路行驶",
          //     },
          //
          // ],
          //   "pathLength": 5263,//总长 米
          //   "durationAll": 947//总耗时 秒
          // }
          params.callback(res)
        }
      })
    },
    /**
     * 借助高德路径规划
     * @param params
     * @return {Promise<unknown>}
     */
    routePlanByGD(params = {}) {
      const config = {
        key: params.key ?? '2cb6cc26ff58be0210b21ddece79f0b8', // webApiKEY
        origin: params.orig, // 起点，经纬度字符串，'119.25,29.0989'
        destination: params.dest,
        waypoints: params.waypoints ?? '',
        cartype: params.cartype ?? 0,
        ferry: params.ferry ?? 0,
        strategy: params.strategy ?? 32,
        extensions: params.extensions ?? 'base',
        output: 'json',
      }

      const url = `https://restapi.amap.com/v3/direction/driving`
      return new Promise((resolve, reject) => {
        $.ajax({
          url: url,
          type: 'get',
          dataType: 'json',
          data: config,
          async: false,
          success(res) {
            if (res.status == '1') {
              resolve({ count: res.count, route: res.route })
            }
          },
          error(e) {
            reject(e)
          },
        })
      })
    },
    /**
     * 高德逆地理编码
     * @description: 借助高度webapi, 实现根据经纬度转化为结构化地址信息
     */
    getAddressByGD(param = { crs: 'gcj02' }) {
      // 判断传入坐标系统
      let location = []
      if (param.crs === 'wgs84') {
        location = coordtransform.wgs84togcj02(Number(param.lon), Number(param.lat))
      } else if (param.crs == 'bd09') {
        location = coordtransform.bd09togcj02(Number(param.lon), Number(param.lat))
      } else {
        location = [Number(param.lon), Number(param.lat)]
      }
      const config = {
        key: param.key ?? '2cb6cc26ff58be0210b21ddece79f0b8',
        location: location.join(','),
        radius: param.radius ?? 1000,
        extensions: param.extensions ?? 'base',
        output: 'json',
      }
      const url = `https://restapi.amap.com/v3/geocode/regeo`
      $.ajax({
        url: url,
        type: 'get',
        data: config,
        async: false,
        dataType: 'json',
        success: (res) => {
          if (res.status == '1') {
            if (param.callback) {
              param.callback(res.regeocode)
            }
          }
        },
        error: (err) => {},
      })
    },
  },
  watch: {},
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
}
</script>

<style scoped>
.container-btn {
  width: 400px;
  height: 35px;
  cursor: pointer;
  border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
  box-sizing: border-box;
  transition: 0.3s;
  line-height: 30px;
  font-size: 25px;
  background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
  color: white;
  text-align: center;
  position: absolute;
  bottom: 30px;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.s-flex {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.s-font-30 {
  font-size: 22px;
}
.head > span[data-v-4bed0a19] {
  font-size: 42px !important;
}
.lj-tab {
  list-style: none;
  padding: 0 20px;
}
.cursor {
  cursor: pointer;
}
body {
  position: relative;
  color: #fff;
}
.zylj-pop {
  position: absolute;
  top: 112px;
  left: 43px;
  z-index: 10;
  width: 700px;
  height: 820px;
}
.lj-title {
  border-radius: 0;
  background-image: linear-gradient(0deg, rgba(0, 89, 147, 0.9) 0%, rgba(0, 32, 52, 0.9) 100%),
  linear-gradient(0deg, rgba(103, 200, 255, 0.2) 0%, rgba(110, 176, 231, 0.1) 100%);
  width: 100%;
  height: 80px;
  line-height: 100px;
  padding: 0 3%;
  box-sizing: border-box;
}
.ljgh-body {
  margin: 2% auto 0;
  height: 90%;
}
.ljgh-start {
  display: flex;
  align-items: center;
  padding: 0 3%;
  box-sizing: border-box;
}
.lj-start-item {
  margin: 10px 0;
}
.lj-icon {
  display: flex;
  height: 224px;
  flex-direction: column;
  justify-content: space-around;
  margin-right: 1%;
}
.icon-updown {
  position: absolute;
  right: 0;
  top: 115px;
  background: url('@/assets/map/icon_arrow_updown.png') no-repeat;
  width: 48px;
  height: 48px;
  background-size: 100% 100%;
  cursor: pointer;
}
.latlng {
  max-width: 272px;
  font-size: 25px;
  border-bottom: 1px solid #61deff;
  border-right: 0;
  border-bottom: 1px solid #61deff;
  background: rgba(97, 222, 255, 0);
  color: #e7e9f6;
  outline: none;
}
.lj-input {
  width: 100%;
  height: 60px;
  line-height: 60px;
  font-size: 32px;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-bottom: 1px solid #61deff;
  border-radius: 5px;
  outline: none;
  background: rgba(97, 222, 255, 0);
  color: #e7e9f6;
  box-sizing: border-box;
  padding: 0 3%;
}
.icon-nav-start {
  background: url('@/assets/map/nav_start.png') no-repeat;
  width: 30px;
  height: 40px;
  transform: scale(1.8);
  /*position: absolute;*/
  /*left: 2%;*/
  cursor: pointer;
}
.icon-nav-end {
  background: url('@/assets/map/nav_end.png') no-repeat;
  width: 30px;
  height: 40px;
  transform: scale(1.8);
  /*position: absolute;*/
  /*left: 2%;*/
  cursor: pointer;
}
.ljgh-type {
  margin: 15px 0;
}
.dw-li {
  background: url('@/assets/map/bg_dw.png') no-repeat;
  background-size: 100% 100%;
  width: 144px;
  height: 57px;
  line-height: 57px;
  text-align: center;
  float: left;
  color: #d8f2f9;
  opacity: 0.7;
  margin-right: 10px;
  cursor: pointer;
}
.dwActive {
  opacity: 1;
}
.ljgh-nav {
  height: 70%;
  width: 100%;
  background-color: rgba(28, 77, 101, 0);
  padding: 0 20px;
  box-sizing: border-box;
}
.nav-info {
  height: 16%;
}
.nav-tip {
  color: #cf3765;
  padding: 0 8px;
}
.nav-step {
  height: 90%;
}
.nav-step-item {
  height: 77px;
  padding: 10px 0;
  background: #00407c8c;
  margin: 14px 0;
}
.icon-arrowUp {
  background: url('@/assets/map/icon_arrowUp.png') no-repeat;
  background-size: 100% 100%;
  width: 32px;
  height: 32px;
}
.nav-arrow {
  width: 90px;
  height: 60px;
}
.nav-text {
  width: 90%;
  white-space: normal;
  word-break: break-all;
  overflow: hidden;
  background-image: linear-gradient(180deg, #fff, #80e0ff, #00c0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.lj-btn {
  width: 120px;
  text-align: center;
  border: 2px solid #359cf8;
  margin: 10px;
  border-radius: 15px;
  cursor: pointer;
  line-height: 50px;
}
.scrollbar-no {
  overflow: hidden;
  overflow-y: scroll;
}
.scrollbar-no::-webkit-scrollbar {
  width: 0;
  height: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
</style>