<template>
  <div>
    <div class="warningRightCharts8" id="warningRightCharts8"></div>
  </div>
</template>

<script>
export default {
  name: "water",
  data() {
    return {
      chartsData:[
        {
          name:"COD",
          value1:11,
          value2:7,
          value3:2,
        },
        {
          name:"PH",
          value1:11,
          value2:7,
          value3:7,
        },
        {
          name:"总磷",
          value1:11,
          value2:7,
          value3:7,
        },
        {
          name:"水温",
          value1:11,
          value2:7,
          value3:11,
        },
        {
          name:"浊度",
          value1:11,
          value2:7,
          value3:10,
        },
        {
          name:"溶解氧",
          value1:11,
          value2:7,
          value3:7,
        },
        {
          name:"电导率",
          value1:11,
          value2:7,
          value3:6,
        },
        {
          name:"高锰酸钾",
          value1:11,
          value2:7,
          value3:10,
        }
      ]
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let myChart = this.$echarts.init(document.getElementById("warningRightCharts8"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          itemGap: 45,
          top:30,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
        },
        grid: {
          left: "3%",
          right: "3%",
          top: "30%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：个",
            type: "value",
            nameTextStyle: {
              fontSize: 28,
              color: "#D6E7F9",
              padding: [5,-55,25,5],
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
          {
            name: "单位：%",
            type: "value",
            nameTextStyle: {
              fontSize: 28,
              color: "#D6E7F9",
              padding: [5,5,25,-50],
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "本月",
            type: "bar",
            barWidth: "20%",
            yAxisIndex: 0,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#00C0FF",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,192,255,0)",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value1),
          }, {
            name: "上月",
            type: "bar",
            barWidth: "20%",
            yAxisIndex: 0,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: "#A9DB52",
                  },
                  {
                    offset: 1,
                    color: "rgba(0,192,255,0)",
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map(item => item.value2),
          }, {
            name: "同比趋势",
            type: "line", // 直线ss
            yAxisIndex: 1,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#FF4949",
              },
            },
            data: this.chartsData.map(item => item.value3),
          }
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped>
.warningRightCharts8 {
  width: 1935.6px;
  height: 500px;
  margin-top: 40px;
}
</style>