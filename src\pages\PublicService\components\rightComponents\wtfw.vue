<!--文体服务-->
<template>
  <div class="rightFour">
    <Ktitle titleText="文体服务" :width="'950'" />
    <div class="wtfwWrap">
      <div class="tyWrap" v-for="(item,i) in list" :key="i">
        <div class="gdzkBoxInner">
          <img :src="item.icon" class="imgClass">
          <ToText :lineOneType="4" :numType="3" :unitType="3" :text="item.name" :num="item.value" :unit="item.unit" />
        </div>
      </div>
    </div>
    <wtfwChart />
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
import ToText from "@/components/ToText.vue"
import wtfwChart from "@/pages/PublicService/components/chartsComponents/WtfwChart";
export default {
  name: "wtfw",
  components:{
    Ktitle,
    ToText,
    wtfwChart
  },
  data() {
    return {
      list: [
        {
          icon:require("@/assets/publicService/tsg.png"),
          name:"图书馆藏书量",
          value:"6932",
          unit:"本"
        },
        {
          icon:require("@/assets/publicService/whg.png"),
          name:"文化场馆接待人数",
          value:"13612",
          unit:"人"
        },
        {
          icon:require("@/assets/publicService/cyz.png"),
          name:"文化产业增加值",
          value:"1232",
          unit:"亿元"
        },
        {
          icon:require("@/assets/publicService/ydl.png"),
          name:"居民综合阅读率",
          value:"82.34",
          unit:"%"
        },
        {
          icon:require("@/assets/publicService/xfzc.png"),
          name:"人均文化娱乐消费支出占比",
          value:"73.11",
          unit:"%"
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.rightFour {
  width: 940px;
  height: fit-content;
  box-sizing: border-box;
  padding: 50px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  // background-color: bisque;
  .wtfwWrap {
    flex: 1;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;

    .tyWrap {
      width: 100%;
      height: 100%;
      // background-color: antiquewhite;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .gdzkBoxInner {
      display: flex;
      align-items: center;

      .imgClass {
        width: 177px;
        height: 137px;
      }
    }
  }
}
</style>