<template>
  <div class="CommandRightContainer">
    <div class="CommandRightContainer-top">
      <!--人员力量-->
      <ryll></ryll>
      <!--监测数据-->
      <jcsj></jcsj>
    </div>
    <!--储备资源-->
    <cbzy></cbzy>
    <!--跟踪督办-->
    <gzdb style="margin-top: 40px;"></gzdb>
  </div>
</template>

<script>
import ryll from "@/pages/CommandAndDispatch/components/RightComponents/ryll";
import jcsj from "@/pages/CommandAndDispatch/components/RightComponents/jcsj";
import cbzy from "@/pages/CommandAndDispatch/components/RightComponents/cbzy";
import gzdb from "@/pages/CommandAndDispatch/components/RightComponents/gzdb";
export default {
  name: "CommandLeft",
  data() {
    return {}
  },
  components: {
    ryll,
    jcsj,
    cbzy,
    gzdb
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.CommandRightContainer {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  .CommandRightContainer-top {
    width: 1935.6px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 67px;
    margin-bottom: 26px;
  }
}
</style>