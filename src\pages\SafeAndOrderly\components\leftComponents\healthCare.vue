<!--卫生医疗-->
<template>
  <div class="health">
    <div class="healthBox">
      <div class="trendList">
        <div class="trendItem" v-for="(item,i) in indexData" :key="i">
          <img :src="item.img" alt="">
          <div class="info">
            <div class="value gold">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
            <div class="name">{{item.name}}</div>
          </div>
        </div>
      </div>
      <div class="healthChart" id="healthChart"></div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "healthCare",
  data() {
    return {
      chartsData:[
        {
          name:"0410",
          value:50
        },
        {
          name:"0411",
          value:30
        },
        {
          name:"0412",
          value:28
        },
        {
          name:"0413",
          value:75
        },
        {
          name:"0414",
          value:60
        },
        {
          name:"0415",
          value:63
        },
        {
          name:"0416",
          value:50
        },
        {
          name:"0417",
          value:23
        }
      ],
      indexData:[
        {
          name:"学前儿童完成疫苗接种",
          value:1.5,
          unit:"万人",
          img:require("@/assets/safe/medical1.png")
        },
        {
          name:"完成全程疫苗接种",
          value:37.78,
          unit:"万人",
          img:require("@/assets/safe/medical2.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("healthChart"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          itemGap: 45,
          right: 75,
          top:15,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
        },
        grid: {
          left: "8%",
          right: "6%",
          top: "22%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：人",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 40,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "当日就诊人次",
            type: "line", // 直线ss
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#FFC460",
              },
            },
            data: this.chartsData.map(item => item.value),
          }
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .health {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    margin-top: 37px;
    .healthBox {
    width: 1811px;
    height: 525px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .healthChart {
      width: 1259px;
      height: 436px;
    }
    .trendList {
      width: fit-content;
      height: 390px;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: column;
      .trendItem {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        img {
          width: 260px;
          height: 201px;
        }
        .info {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
          margin-left: 36px;
          .name {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            white-space: nowrap;
          }
          .value {
            font-size: 60px;
            font-family: BN;
            font-weight: 400;
          }
          .unit {
            font-size: 25px;
            margin-top: 15px;
            white-space: nowrap;
            margin-left: 5px;
          }
        }
      }
    }
  }
}
</style>