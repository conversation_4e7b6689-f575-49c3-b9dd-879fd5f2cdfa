<template>
  <div class="mid-main">
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="getAjList">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="total-sta">
      <div class="num">{{ajSum}}件</div>
      <div class="view-det-entry" @click="rwClick('sj')">详情</div>
    </div>
    <div class="legend">
      <div class="legend-item">
        <div class="block" style="background: #ff4a48"></div>
        <div class="text">上报</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #fdc260"></div>
        <div class="text">受理</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #00befc"></div>
        <div class="text">办理</div>
      </div>
      <div class="legend-item">
        <div class="block" style="background: #22e4e5"></div>
        <div class="text">结案</div>
      </div>
    </div>
    <div class="navs">
      <div
        class="nav-item"
        :class="item.on ? 'nav-item-select' : ''"
        v-for="item of navs"
        :key="item.id"
        @click="navItemClick(item)"
      >
        <img :src="item.icon" alt="" />
        <div class="text">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import {getEventNumber,getEventPointList} from "@/api/EventTask";
export default {
  data() {
    return {
      navs: [
        {
          id: '1',
          name: '公共服务',
          on: false,
          icon: require('@/assets/eventTaskCenter/midBottom/event/ggaq.png'),
          markerType: '公共安全MarkerArray',
          markerIcon: '/map/img/eventTaskCenter/ggaq.png',
          key:"ggfw",
          markerList: [
            {
              lx: '公共安全',
              jd: '处置',
              lng: 119.650638,
              lat: 29.09257,
              type: 'ggaq',
              jdindex: 2,
              sjly: '市民报告',
              sjnr: '2024年5月1日上午10点，位于市中心广场附近的步行街，市民陈武兴先生报告发现一个被遗弃的可疑包裹，引起了周围人群的关注和担忧。',
              sjbh: '2024-EVT-0456',
              sjsj: '2024年5月1日 10:00',
              sjdd: '双溪西路',
              sjlx: '公共安全',
              xm: '陈武兴',
              lxdh: '15021781092',
              cldw: '金华市综合行政执法局金华经济技术开发区分局',
              jasj: '暂无',
              jzxx: '暂无',
              tpxx: '暂无',
            },
          ],
        },
        {
          id: '2',
          name: '监管执法',
          on: false,
          icon: require('@/assets/eventTaskCenter/midBottom/event/scjg.png'),
          markerType: '监管执法MarkerArray',
          key:"jgzf",
          markerIcon: '/map/img/eventTaskCenter/scjg.png',
        },
        {
          id: '3',
          name: '经济生态',
          on: false,
          icon: require('@/assets/eventTaskCenter/midBottom/event/hjjc.png'),
          markerType: '经济生态MarkerArray',
          key:"jjst",
          markerIcon: '/map/img/eventTaskCenter/hjjc.png',
        },
        {
          id: '4',
          name: '应急管理',
          on: false,
          icon: require('@/assets/eventTaskCenter/midBottom/event/yjxy.png'),
          markerType: '应急管理MarkerArray',
          key:"yjgl",
          markerIcon: '/map/img/eventTaskCenter/yjxy.png',
        },
        {
          id: '5',
          name: '综治工作',
          on: false,
          icon: require('@/assets/eventTaskCenter/midBottom/event/cszl.png'),
          markerType: '综治工作MarkerArray',
          key:"zzgz",
          markerIcon: '/map/img/eventTaskCenter/cszl.png',
        }
      ],
      value:"日",
      options:[
        {label:"日",value:"日"},
        {label:"月",value:"月"},
        {label:"年",value:"年"},
      ],
      ajList: [
        {
          name: '公共服务',
          icon: require('@/assets/eventTaskCenter/midBottom/event/ggaq.png'),
          count: 0,
        },
        {
          name: '监管执法',
          icon: require('@/assets/eventTaskCenter/midBottom/event/scjg.png'),
          count: 0,
        },
        {
          name: '经济生态',
          icon: require('@/assets/eventTaskCenter/midBottom/event/hjjc.png'),
          count: 0,
        },
        {
          name: '应急管理',
          icon: require('@/assets/eventTaskCenter/midBottom/event/yjxy.png'),
          count: 0,
        },
        {
          name: '综治工作',
          icon: require('@/assets/eventTaskCenter/midBottom/event/cszl.png'),
          count: 0,
        }
      ]
    }
  },
  computed: {
    ajSum() {
      let sum = 0
      this.ajList.forEach((item,i) => {
        sum += item.count;
      })
      return sum
    }
  },
  mounted() {
    this.getAjList()
  },
  methods: {
    getAjList() {
      getEventNumber({type: this.value}).then(res => {
        const typeCountMap = res.data.data.reduce((map, obj) => {
          map[obj.label] = (map[obj.label] || 0) + obj.cnt;
          return map;
        }, {});
        this.ajList.forEach(item => {
          item.count = typeCountMap[item.name] || 0;
        });
      })

      //下拉修改时清除所有点位以及选中状态
      this.navs.forEach(item => item.on = false);
      this.cleanAllPoint()
    },
    navItemClick(item) {
      item.on = !item.on
      if (item.on) {
        getEventPointList({label: item.name,type: this.value}).then(res => {
          let markerList = res.data.data.map(obj => ({
            id: obj.id?obj.id:null,
            lx: obj.label,
            jd: obj.processStatus,
            lng: obj.longitude,
            lat: obj.latitude,
            type: item.key
          })).filter(obj2 => obj2.id !== null && obj2.id !== undefined);
          this.$EventBus.$emit('CommonDrawPoint', markerList, item.markerType, item.markerIcon, false, true, true)
        })
      } else {
        top.mapUtil.removeLayer(String(item.markerType))
      }
    },
    //任务详情
    rwClick(v) {
      this.$emit('tabClick', {name:v, value:this.ajList})
    },
    //清除点位
    cleanAllPoint() {
      this.navs.forEach((item,i) => {
        if (top.mapUtil.layers[String(item.markerType)]) {
          top.mapUtil.removeLayer(String(item.markerType))
        }
      })
    }
  },
  destroyed() {
    this.cleanAllPoint()
  }
}
</script>

<style lang="less" scoped>
.mid-main {
  position: relative;
  /deep/ .select {
    position: absolute;
    z-index: 999;
  }
  .total-sta {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    .num {
      margin-right: 40px;
      background: linear-gradient(to bottom, #ffeccb, #ffc460); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
      font-size: 54px;
      font-weight: bold;
    }
    .view-det-entry {
      color: #00f0ff;
      font-size: 32px;
      cursor: pointer;
    }
  }
  .navs {
    display: flex;
    justify-content: space-between;
    padding: 15px 260px;
    .nav-item {
      display: flex;
      flex-flow: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      width: 239px;
      height: 218px;
      .text {
        margin-top: 20px;
        color: #00f0ff;
        font-size: 32px;
      }
    }
    .nav-item-select {
      background: url('@/assets/eventTaskCenter/midBottom/navSelBg.png') no-repeat;
    }
  }
  .legend {
    position: absolute;
    z-index: 30;
    top: 10px;
    right: 200px;
    display: flex;
    .legend-item {
      display: flex;
      align-items: center;
      margin: 0 80px;
      .block {
        width: 20px;
        height: 20px;
        border-radius: 3px;
      }
      .text {
        margin-left: 12px;
        color: #d6e7f9;
        font-size: 28px;
      }
    }
  }
}
</style>
