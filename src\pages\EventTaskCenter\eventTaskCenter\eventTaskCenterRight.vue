<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-05-07 09:23:45
 * @LastEditors: wjb
 * @LastEditTime: 2024-05-08 08:38:11
-->
<template>
  <div class="rightContainer">
    <div class="eventTaskCenterRight-box1">
      <ktitle :titleText="'任务接入情况'"></ktitle>
      <putStatus></putStatus>
    </div>
    <div class="box2">
      <ktitle :titleText="'任务完成情况'"></ktitle>
      <finishStatus></finishStatus>
    </div>
    <div class="box3">
      <div class="box3-left">
        <titleTwo :titleText="'任务排名'" width="939.6" :showDate='false'></titleTwo>
        <ranking></ranking>
      </div>
      <div class="box3-right">
        <titleTwo :titleText="'任务趋势'" width="939.6" :showDate='false'></titleTwo>
        <trend></trend>
      </div>
    </div>
  </div>
</template>

<script>
import ktitle from '@/components/title'
import titleTwo from "@/components/titleCommon.vue"
import putStatus from '@/pages/EventTaskCenter/eventTaskCenter/rightComponents/putStatus'//任务接入情况
import finishStatus from '@/pages/EventTaskCenter/eventTaskCenter/rightComponents/finishStatus'//任务完成情况
import ranking from '@/pages/EventTaskCenter/eventTaskCenter/rightComponents/ranking'//任务排名
import trend from '@/pages/EventTaskCenter/eventTaskCenter/rightComponents/trend'//任务趋势
export default {
  name: 'eventCenterLeft',
  data() {
    return {}
  },
  components: {
    ktitle,
    titleTwo,
    putStatus,
    finishStatus,
    ranking,
    trend
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.rightContainer {
  margin: 80px 68px 0 68px;
  .box2 {
    margin-top: 30px;
  }
  .box3 {
    display: flex;
    align-items: center;
    margin-top: 30px;
    .box3-left,
    .box3-right {
      flex: 1;
      width: 939.6px;
      height: 500px;
    }
  }
}
</style>
