<!--预警级别-->
<template>
  <div>
    <titleTwo :titleText="'预警级别'"></titleTwo>
    <div class="warningCharts3" id="warningCharts3"></div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import imgUrl from "@/assets/common/piebg.png";
import {getWarningLevel} from "@/api/warning";
export default {
  name: "level",
  data() {
    return {
      charts2Data:[],
      levelList: [
        {
          label:"紧急",
          value:1
        },
        {
          label:"重要",
          value:2
        },
        {
          label:"普通",
          value:3
        },
        {
          label:"提示",
          value:4
        },
        {
          label:"未确认",
          value:5
        },
      ],
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let that = this
      getWarningLevel().then(res => {
        this.charts2Data = res.data.data.map(item => ({
          name: this.getLabel(this.levelList,Number(item.name)),
          value: item.num
        }))
        let myChart = this.$echarts.init(document.getElementById("warningCharts3"));
        let imgUrl = require('@/assets/common/piebg.png')
        let option = {
          color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

          tooltip: {
            trigger: 'item',
            // formatter: '{b}: <br/> {d}%',
            formatter: '{b}: <br/> {c}个<br/> {d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          legend: {
            orient: 'vertical',
            left: '48%',
            top: '20%',
            bottom: '0%',
            icon: 'circle',
            itemGap: 20,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: '#ffffff',
                  padding: [0, 20, 0, 15]
                },
                value: {
                  fontSize: 25,
                  color: '#FFC460',
                  // padding: [10, 0, 0, 15]
                },
              }
            },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              that.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2)
              return '{name|' + name + '}{value|' + p + '%}'
            },
          },
          graphic: [
            {
              type: "image",
              id: "logo",
              left: "10.8%",
              top: "10.4%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['55%', '80%'],
              center: ['25%', '54%'],
              roseType: '',
              itemStyle: {
                borderRadius: 0,
              },
              label: {
                show: false,
              },
              data: this.charts2Data,
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
    //字典翻译
    getLabel(arr, value) {
      const item = arr.find(item => item.value === value);
      if (item === undefined) {
        // 处理未找到匹配项的情况，可能是抛出错误或返回默认值
        // throw new Error(`Value "${value}" not found in array.`);
        // 或者 return "默认标签"; 依赖具体业务需求
        return ""
      }
      return item.label;
    },
  },
  watch: {}
}
</script>

<style scoped>
.warningCharts3 {
  width: 935px;
  height: 307px;
}
</style>