<template>
  <div class="facilityCount">
    <div class="left_box">
      <div class="left_box1">
        <div class="item_box" v-for="(item, index) in leftdata1" :key="index">
          <div class="count_box">
            <div class="count">{{ item.count }}</div>
          </div>
          <div class="name">{{ item.name }}</div>
        </div>
      </div>
      <div class="left_box2">
        <div class="title_box">
          <div class="title_item">
            <div class="block bg1"></div>
            <div class="name">已处理</div>
          </div>
          <div class="title_item">
            <div class="block bg2"></div>
            <div class="name">未处理</div>
          </div>
        </div>
        <div class="line_box">
          <div class="line_item" v-for="(item, index) in leftdata2" :key="index">
            <div class="item_count">
              <div class="count_left" :style="{ width: (item.count / (item.count + item.count2)) * 100 + '%' }"></div>
              <div class="count_right" :style="{ width: (item.count2 / (item.count + item.count2)) * 100 + '%' }"></div>
            </div>
            <div class="item_name">
              <div class="name_left">{{ item.name }}：已处理预警 {{ item.count }}</div>
              <div class="name_right">未处理预警</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="right_box">
      <div class="item_box" :class="'bg' + index" v-for="(item, index) in rightdata" :key="index">
        <div class="name">{{ item.name }}</div>
        <div class="count_box">
          <div class="count">{{ item.count }}</div>
          <div class="unit">{{ item.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'facilityCount',
  data() {
    return {
      leftdata1: [
        { count: 689, name: '设备数量' },
        { count: 131, name: '设备异常' },
        { count: 32, name: '设备预警' },
      ],
      leftdata2: [
        { count: 457, count2: 135, name: '智慧安防' },
        { count: 546, count2: 114, name: '智慧车辆' },
        { count: 423, count2: 127, name: '智慧安防' },
      ],
      rightdata: [
        { count: 46302, name: '智慧车辆', unit: '辆' },
        { count: 460893, name: '智慧访客', unit: '个' },
        { count: 43463, name: '智慧能耗', unit: '个' },
        { count: 46453, name: '智慧考勤', unit: '次' },
      ],
    }
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {},
}
</script>

<style scoped lang="less">
.facilityCount {
  margin-top: 98px;
  display: flex;
  .left_box {
    flex: 1;
    margin-right: 176px;
    .left_box1 {
      display: flex;
      align-content: center;
      align-items: center;
      justify-content: space-between;
      margin-left: 44px;

      .item_box {
        .count_box {
          width: 158px;
          height: 140px;
          text-align: center;
          padding-top: 40px;
          box-sizing: border-box;
          background: url('@/assets/common/logistics_count_bkg.png') 0 0 no-repeat;
          background-size: cover;
          .count {
            font-size: 60px;
            font-family: BN;
            font-weight: 400;
            color: #9aa9bf;
            background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50%, #ffffff 53%, #cbf2ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        .name {
          margin-top: 12px;
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #ffffff;
          text-align: center;
        }
      }
    }
    .left_box2 {
      padding: 88px 0 44px 44px;
      .title_box {
        display: flex;
        justify-content: space-between;
        .title_item {
          display: flex;
          align-content: center;
          align-items: center;
          .block {
            width: 21px;
            height: 21px;
          }
          .bg1 {
            background: #00c0ff;
          }
          .bg2 {
            background: #fcdf1d;
          }
          .name {
            margin-left: 4px;
            font-size: 28px;
            font-family: Source Han Sans SC;
            font-weight: 400;
            color: #d6e7f9;
          }
        }
      }
      .line_box {
        .line_item {
          margin-top: 48px;
          .item_count {
            display: flex;
            align-content: center;
            align-items: center;
            justify-content: space-between;
            .count_left {
              height: 18px;
              background: linear-gradient(270deg, #cbf2ff, #85e6ff, #00c0ff);
              border-radius: 9px;
              margin-right: 16px;
            }
            .count_right {
              height: 18px;
              background: linear-gradient(270deg, #ffeccb, #ffe4a8, #ffc460);
              border-radius: 9px;
            }
          }
          .item_name {
            display: flex;
            align-content: center;
            align-items: center;
            justify-content: space-between;
            margin-top: 24px;
            .name_left {
              font-size: 32px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #ffffff;
            }
            .name_right {
              font-size: 32px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .right_box {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .bg0 {
      background: url('@/assets/common/logistics_count_icon1.png') 0 0 no-repeat;
      background-size: cover;
    }
    .bg1 {
      background: url('@/assets/common/logistics_count_icon2.png') 0 0 no-repeat;
      background-size: cover;
    }
    .bg2 {
      background: url('@/assets/common/logistics_count_icon3.png') 0 0 no-repeat;
      background-size: cover;
    }
    .bg3 {
      background: url('@/assets/common/logistics_count_icon4.png') 0 0 no-repeat;
      background-size: cover;
    }
    .item_box {
      width: 380px;
      height: 172px;
      margin-right: 52px;
      .name {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #ffffff;
        margin-left: 210px;
        margin-top: 56px;
      }
      .count_box {
        display: flex;
        justify-content: flex-end;
        margin-right: 20px;
        margin-top: 26px;
        color: #9aa9bf;
        background: linear-gradient(180deg, #ffffff 0%, #00c0ff 50.244140625%, #ffffff 53.0029296875%, #cbf2ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-family: BN;
        .count {
          font-size: 60px;
          font-weight: 400;
        }
        .unit {
          font-size: 32px;
          margin-top: 24px;
        }
      }
    }
  }
}
</style>
