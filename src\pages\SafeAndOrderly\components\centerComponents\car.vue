<!--消防车辆-->
<template>
  <div>
    <titleTwo :title-text="'消防车辆'"></titleTwo>
    <pie3D id="fireCarEcharts"  :scaleCustom="1"  :options="chartsData" :legendCustom="legend" :internalDiameterRatio="0.7" class="chart"></pie3D>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import pie3D from "@/components/pie3D";
export default {
  name: "car",
  data() {
    return {
      chartsData:[
        {
          value: 2,
          name: "城市主战车"
        },
        {
          value: 3,
          name: "重型水罐车"
        },
        {
          value: 3,
          name: "抢险救援车"
        },
        {
          value: 2,
          name: "高喷车"
        },
        {
          value: 1,
          name: "登高平台车"
        }
      ],
      legend: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 30,
        left: '55%',
        top: '12%',
      }
    }
  },
  components:{
    titleTwo,
    pie3D
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.chart {
  width: 935px;
  height: 460px;
  margin-top: 30px;
}
</style>