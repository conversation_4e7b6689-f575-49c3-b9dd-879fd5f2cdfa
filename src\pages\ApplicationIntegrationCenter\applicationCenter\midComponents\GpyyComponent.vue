<template>
  <div class="yyglContainer">
    <Ktitle :titleText="'高频应用TOP10'" width="937"></Ktitle>
    <div class="pngWrap">
      <div class="pngInnerWrap">
        <div class="header">
          <div class="pmClass">排名</div>
          <div class="yymcClass">应用名称</div>
          <div class="fwsClass">访问数</div>
        </div>
        <div class="warningCharts2" id="warningCharts2"></div>

      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/titleCommon.vue"
export default {
  components: {
    Ktitle
  },
  data () {
    return {
      chartsData: [
        {
          name: "智慧城管项目",
          value: 21420
        },
        {
          name: "金华开发区社会治理“微智治”",
          value: 1865
        },
         {
          name: "全生命周期项目管理",
          value: 587
        },
        {
          name: "智慧应急监测预警系统",
          value: 511
        },
        {
          name: "睦家园",
          value: 499
        },
        {
          name: "平安法治小区",
          value: 146
        }
      ]
    }
  },
  mounted () {
    this.initCharts()
  },
  methods: {
    initCharts () {
      const that = this
      let myChart = this.$echarts.init(document.getElementById("warningCharts2"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
          formatter: '{b}: <br/> {c}次',
        },
        grid: {
          top: "5%",
          left: "0%",
          right: "0%",
          bottom: "0",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          name: "",
          type: "category",
          triggerEvent: false,
          inverse: true,
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            inside: true,
            textStyle: {
              color: "#D6E7F9",
              verticalAlign: "bottom",
              align: "left",
              padding: [0, 0, 9, 0],
              fontSize: 32
            },
            // formatter: function (params) {
            //   console.log('这是排名index',params)
            //   return ('TOP' + (that.chartsData.findIndex((item => item.name == params)) + 1)).toString() + '  ' + params
            // }
            formatter: function (value, index) {
              if (index == 0) {
                return '{titleOne|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 1) {
                return '{titleTwo|TOP' + (index + 1) + '}' + '  ' + value
              } else if (index == 2) {
                return '{titleThree|TOP' + (index + 1) + '}' + '  ' + value
              } else {
                return '{title|TOP' + (index + 1) + '}' + '  ' + value
              }
            },
            rich: {
              titleOne: {
                width: 120,
                align: 'left',
                color: '#FF4949',
                fontSize: 32,
                fontWeight: '500'
              },
              titleTwo: {
                width: 120,
                align: 'left',
                color: '#DF8F30',
                fontSize: 32,
                fontWeight: '500'
              },
              titleThree: {
                width: 120,
                align: 'left',
                color: '#B76FD8',
                fontSize: 32,
                fontWeight: '500'
              },
              title: {
                width: 120,
                align: 'left',
                color: '#30B8D5',
                fontSize: 32,
                fontWeight: '500'
              }
            }
          },
          data: this.chartsData.map(item => item.name),
        },
        series: [{
          // cursor:"auto",
          type: "bar",
          name: "",
          showBackground: true,
          backgroundStyle: {
            color: '#2E3F54'
          },
          itemStyle: {
            barBorderRadius: [0, 10, 10, 0],
            color: function (params) {
              var colors = [
                "#4587E7",
                "#35AB33",
                "#F5AD1D",
                "#ff7f50",
                "#da70d6",
                "#32cd32",
                "#6495ed",
              ]
              // 通过返回值的下标一一对应将颜色赋给柱子上，return出去什么颜色就是什么颜色，这里可以写判断
              if (params.dataIndex == 0) {
                return new echarts.graphic.LinearGradient(
                  1,
                  0,
                  0,
                  0,
                  [{
                    offset: 0,
                    color: "#FF9B78", //指0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "#FF4949", //指100%处的颜色
                  },
                  ],
                  false
                )
              } else if (params.dataIndex == 1) {
                return new echarts.graphic.LinearGradient(
                  1,
                  0,
                  0,
                  0,
                  [{
                    offset: 0,
                    color: "#FAFF78", //指0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "#DF8F30", //指100%处的颜色
                  },
                  ],
                  false
                )
              } else if (params.dataIndex == 2) {
                return new echarts.graphic.LinearGradient(
                  1,
                  0,
                  0,
                  0,
                  [{
                    offset: 0,
                    color: "#E2B8F6", //指0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "#B76FD8", //指100%处的颜色
                  },
                  ],
                  false
                )
              } else {
                return new echarts.graphic.LinearGradient(
                  1,
                  0,
                  0,
                  0,
                  [{
                    offset: 0,
                    color: "#2BDAFF", //指0%处的颜色
                  },
                  {
                    offset: 1,
                    color: "#078FF7", //指100%处的颜色
                  },
                  ],
                  false
                )
              }
              // return colors[params.dataIndex];
            },
          },
          label: {
            show: true,
            position: [835, -40],
            color: "#fff",
            formatter: function (params) {
              return params.value
            },
            fontSize: 32,
            textStyle: {
              color: '#B5DAFF',
              fontFamily: 'Source Han Sans CN'
            }
          },
          barWidth: 8,
          color: "#539FF7",
          data: this.chartsData.map(item => item.value),
        }, {
          name: "",
          type: "bar",
          barWidth: 8,
          barGap: "-100%",
          data: this.chartsData.map(item => 150),
          itemStyle: {
            normal: {
              color: "#2E5171",
            },
          },
          z: 0,
        }]
      }

      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
.yyglContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .pngWrap {
    flex: 1;
    width: 100%;
    box-sizing: border-box;
    padding: 80px 0px 0 0px;
    .pngInnerWrap {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // background-color: aqua;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 30px;
        font-family: Source Han Sans SC;
        font-weight: 400;
        color: #ddeeff;
        margin-bottom: 40px;
        box-sizing: border-box;
        padding: 0 0 0 10px;
        .pmClass {
          width: 11%;
        }
        .yymcClass {
          flex: 1;
        }
        .fwsClass {
        }
      }
      .warningCharts2 {
        flex: 1;
      }
    }
  }
}
</style>