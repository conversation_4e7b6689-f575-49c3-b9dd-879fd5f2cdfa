<template>
  <div class="eventSituation">
    <div class="box1">
      <div class="item" v-for="(item, i) in data0" :key="i">
        <div>{{ item.label }}</div>
        <div>
          <div>{{ item.val }}</div>
          <div>{{ item.dw }}</div>
        </div>
      </div>
    </div>
    <div class="box2">
      <div class="item" v-for="(item, i) in data1" :key="i">
        <div>{{ item.label }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "eventSituation",
  data() {
    return {
      data0: [
        { label: "本月事件数", val: 324, dw: "件" },
        { label: "本年事件数", val: 452, dw: "件" },
        { label: "累计事件数", val: 622, dw: "件" },
      ],
      data1:[
        {label:"重大紧急",value:1056},
        {label:"风险隐患",value:1056},
        {label:"矛盾纠纷",value:1056},
        {label:"复杂问题",value:1056},
        {label:"事务处理",value:1056},
        {label:"其他事件",value:1056},
      ]
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
  },
  watch: {}
}
</script>

<style scoped lang="less">
.eventSituation {
  margin-top: 22px;

  .box1 {
    margin-top: 54px;
    margin-bottom: 78px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 161px;

    .item {
      background-image: url(@/assets/home/<USER>/left/icon1.png);
      background-size: cover;
      width: 354px;
      height: 161px;
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;
      box-sizing: border-box;

      >div:first-child {
        margin-left: 172px;
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
        margin-top: 50px;
      }

      >div:last-child {
        margin-left: 172px;
        display: flex;
        align-items: baseline;

        >div:first-child {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        >div:last-child {
          font-size: 32px;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  .box2{
    width: 1931px;
    height: 182px;
    background-image: url(@/assets/home/<USER>/left/icon2.png);
    background-size: cover;
    margin-top: 128px;
    display: flex;
    align-items: center;
    // padding: 0 90px;
    box-sizing: border-box;
    position: relative;
    >div{
      width:187px;
      height: 240px;
      margin-top: 5px;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      >div:first-child{
        font-size: 32px;
font-family: Source Han Sans CN;
font-weight: 400;
color: #FFFFFF;
text-align: center;
// line-height: 29px;
      }
      >div:nth-child(2){
        font-size: 60px;
font-family: BN;
font-weight: 400;
color: #FFFFFF;
text-align: center;

background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;
      }
    }
    >div:nth-child(1){
      left: 145px;
    }
    >div:nth-child(2){
      left: 430px;
    }
    >div:nth-child(3){
      left: 720px;
    }
    >div:nth-child(4){
      left: 1005px;
    }
    >div:nth-child(5){
      left: 1292px;
    }
    >div:nth-child(6){
      left: 1580px;
    }
  }

}</style>