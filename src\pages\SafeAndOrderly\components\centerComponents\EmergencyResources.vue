<!--应急资源-->
<template>
  <div>
    <titleTwo :title-text="'应急资源'"></titleTwo>
    <div class="box">
      <div class="line1">
        <div class="item" v-for="(item,i) in indexs" :key="i" :style="{background:'url(' + item.background + ')'}">
          <div class="info">
            <div class="name">{{item.name}}</div>
            <div class="value gold"> {{item.value}} <span class="unit">{{item.unit}}</span> </div>
          </div>
        </div>
      </div>
      <div class="line2">
        <div class="item" v-for="(item,i) in indexs2" :key="i">
          <div class="name">{{item.name}}</div>
          <div class="value gold">{{item.value}}{{ item.unit }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "SafeProduction",
  data() {
    return {
      indexs:[
        {
          name:"避难安置场所数",
          value:46,
          unit:"个",
          background:require("@/assets/safe/应急资源1.png")
        },
        {
          name:"政府应急预案数",
          value:10,
          unit:"项",
          background:require("@/assets/safe/应急资源2.png")
        }
      ],
      indexs2:[
        {
          name:"应急队伍",
          unit:"个",
          value:17
        },
        {
          name:"应急专家",
          unit:"人",
          value:53
        },
        {
          name:"应急组织",
          unit:"个",
          value:48
        },
        {
          name:"应急救援装备",
          unit:"个",
          value:126030
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .box {
    width: 935px;
    height: 440px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    .line1 {
      width: 100%;
      height: 161px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin-top: 10px;
      .item {
        width: 421px;
        height: 161px;
        background-size: cover;
        .info {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex-direction: column;
          margin: 30px 0 0 172px;
          .name {
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
            white-space: nowrap;
          }
          .value {
            font-size: 60px;
            font-family: BN;
            font-weight: 400;
          }
          .unit {
            font-size: 25px;
            white-space: nowrap;
            margin-left: 5px;
          }
        }
      }
    }
    .line2 {
      margin-top: 54px;
      width: 1048px;
      height: 176px;
      background: url("@/assets/safe/应急资源3.png");
      background-size: cover;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      .item {
        width: 242px;
        height:225px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-direction: column;
        .name {
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #FFFFFF;
        }
        .value {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          position: relative;
          top: 20px;
        }
      }
    }
  }
</style>