<template>
  <div class="innerLeft">
    <Wrapbox bg="left">
      <div class="leftWrapper animate__animated animate__fadeInLeft">
        <div class="leftTop">
          <shbz />
        </div>
        <div class="leftBot">
          <lysy />
        </div>
      </div>
    </Wrapbox>
  </div>
</template>

<script>
import Wrapbox from "@/components/wrapbox.vue"
import shbz from "@/pages/PublicService/components/leftComponents/shbz";
import lysy from "@/pages/PublicService/components/leftComponents/lysy";
export default {
  components: {
    Wrapbox,
    shbz,
    lysy
  },
  data () {
    return {

    }
  }
}
</script>

<style scoped lang="less">
.innerLeft {
  .leftWrapper {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 1fr;

    .leftTop {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      padding: 50px 0 0 0;
    }

    .leftBot {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
    }
  }
}
</style>