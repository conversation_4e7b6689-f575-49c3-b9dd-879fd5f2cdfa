<template>
  <div>
   <pie3D id="yjlxEcharts"  :scaleCustom="1.7"  :options="charts1Data" v-if="showPie3D" :legend-custom="legendOption" :zHeight="0.03"></pie3D>
  </div>
</template>

<script>
import pie3D from "@/components/pie3D";
import {getWarningType} from "@/api/home";
export default {
  name: "charts1",
  data() {
    return {
      charts1Data: [],
      legendOption: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 14,
        left: '47%',
        top: '12%',
      },
      showPie3D: false
    }
  },
  components:{
    pie3D
  },
  computed: {},
  mounted() {
    this.getChartData()
  },
  methods: {
    getChartData() {
      getWarningType().then(res => {
        if (res.status == 200) {
          this.charts1Data = res.data.data.map(item => ({
            name: item.name,
            value: item.num
          }))
          this.showPie3D = true
        }
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">

</style>