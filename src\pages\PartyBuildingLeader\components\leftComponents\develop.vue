<!--党建发展-->
<template>
  <div class="develop">
    <div class="list">
      <div class="item" v-for="(item, i) in list" :key="i">
        <div class="img" :style="{ background: 'url(' + item.img + ')' }"></div>
        <div class="name">{{ item.name }}</div>
        <div class="value blue"> {{ item.value }} <span class="unit">{{ item.unit }}</span> </div>
        <div class="percent blue">{{ item.percent }} <span class="unit">%</span>
          <div class="arrow" :class="{ up: item.isUp, down: !item.isUp }"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    filterTime: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      list: [
        {
          name: "党员总数",
          value: 19074,
          unit: "人",
          percent: 18,
          img: require("@/assets/partyBuilding/党建发展1.png"),
          isUp: true
        },
        {
          name: "本年发展党员总数",
          value: 134,
          unit: "人",
          percent: 18,
          img: require("@/assets/partyBuilding/党建发展2.png"),
          isUp: false
        },
        {
          name: "基层党组织总数",
          value: 981,
          unit: "个",
          percent: 18,
          img: require("@/assets/partyBuilding/党建发展3.png"),
          isUp: true
        }
      ]
    }
  },
  computed: {},
  created () {
    this.$nextTick(() => {
      console.log('在 created ', this.filterTime)
    })
  },
  methods: {},
  watch: {
    filterTime: {
      handler (newVal, oldVal) {
        // console.log('timefileter绑定值发生了变化', newVal, oldVal)
        console.log('在组件中收到过滤时间s', this.filterTime)

      }
    }
  }
}
</script>

<style scoped lang="less">
.develop {
  width: 100%;

  .list {
    width: 100%;
    height: 740px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 150px;

    .item {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;

      .img {
        width: 744px;
        height: 462px;
        background-size: cover;
      }

      .name {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
      }

      .value {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        margin: 65px 0 48px 0;
      }

      .percent {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .unit {
        font-size: 40px;
      }

      .arrow {
        width: 33px;
        height: 24px;
        background-size: cover;
        margin: 5px 0 0 5px;
      }

      .up {
        background: url("@/assets/partyBuilding/up.png");
      }

      .down {
        background: url("@/assets/partyBuilding/down.png");
      }
    }
  }
}
</style>