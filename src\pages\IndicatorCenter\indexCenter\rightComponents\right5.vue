<template>
  <div class="zwfwRight4">
    <div class="box1">
      <div v-for="(item, i) in data1" :key="i">
        <div>{{ item.value }}</div>
        <div>{{ item.name }}</div>
      </div>
    </div>
    <div class="box2">
      <div v-for="(item, i) in data2" :key="i">
        <div>
          <div>{{ item.name }}</div>
          <div>{{ item.value }}</div>
          <div>{{ item.dw }}</div>
        </div>
        <img v-if="i % 2 == 0" src="@/assets/home/<USER>" alt="">
        <img v-if="i % 2 != 0" src="@/assets/home/<USER>" alt="">
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "right1",
  data() {
    return {
      data1: [
        { name: "教师总数", value: 4046 },
        { name: "参保人数", value: 178521 },
        { name: "阳光厨房", value: 1659 },
      ],
      data2: [
        { name: "“一网通办办件率”", value: 97.18, dw: "%" },
        { name: "当年线上消费投诉举报量", value: 4959, dw: "次" },
        { name: "“一网通办”占比 达标率", value: 99.92, dw: "%" },
        { name: "当年线下消费投诉举报量", value: 4127, dw: "次" },
      ]
    }
  },
  computed: {},
  mounted() {
  },
  methods: {
  },
  watch: {}
}
</script>

<style scoped lang="less">
.zwfwRight4 {
  .box1 {
    display: flex;
    align-items: center;
    margin-top: 48px;

    >div {
      background-image: url("@/assets/home/<USER>");
      width: 292px;
      height: 340px;
      background-size: cover;
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: space-between;
      margin-right: 29px;

      >div:nth-child(1) {
        margin-top: 55px;
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        color: #FFFFFF;

        background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      >div:nth-child(2) {
        font-size: 36px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #FFFFFF;
        margin-bottom: 69px;
      }

    }
  }

  .box2 {
    display: flex;
    flex-wrap: wrap;
    padding: 0 47px;
    box-sizing: border-box;
    >div {
      width: 400px;
      margin-bottom: 60px;
      margin-right: 30px;
      >div {
        display: flex;
        align-items: flex-end;
        height: 69px;
        >div:nth-child(1) {
          max-width: 220px;
          margin-right: 30px;
          font-size: 32px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #FFFFFF;
          text-align: center;
        }

        >div:nth-child(2) {
          font-size: 60px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        >div:nth-child(3) {
          font-size: 32px;
          font-family: BN;
          font-weight: 400;
          color: #9AA9BF;

          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      >img {
        // margin-top: 15px;
        width: 365px;
        height: 35px;
      }
    }
  }

}
</style>