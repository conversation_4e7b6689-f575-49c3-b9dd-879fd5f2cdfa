<!--生态环境-->
<template>
  <div class="container">
    <div class="title">{{name}}</div>
    <div class="box">
      <div class="left">
          <div class="value">{{value}}</div>
          <div class="box-left-item" v-for="(item,i) in weather" :key="i">
              <div class="icon" :style="{backgroundImage:'url('+item.icon+')'}"></div>
              <div class="name">{{item.value}}</div>
          </div>
      </div>
      <div class="right">
          <div class="item" v-for="(item,i) in info" :key="i">
            <div class="item-top">
              <div class="name">{{item.name}}</div>
              <div class="value">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
            </div>
            <div class="line" :class="{yellowbg:i == 0,bluebg:i == 1,purplebg:i == 2,redbg:i == 3}"></div>
          </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Environment",
  data() {
    return {
      name:"开发区监测站",
      value:135,
      weather:[
        {
          value:"轻度污染",
          icon:require("@/assets/home/<USER>/weather1.png")
        },
        {
          value:"西南风二级",
          icon:require("@/assets/home/<USER>/weather2.png")
        },
        {
          value:"湿度25%",
          icon:require("@/assets/home/<USER>/weather3.png")
        },
        {
          value:"气压1025Pa",
          icon:require("@/assets/home/<USER>/weather4.png")
        },
        {
          value:"良",
          icon:require("@/assets/home/<USER>/weather5.png")
        }
      ],
      info:[
        {
          name:"PM2.5 细颗粒",
          value:43,
          unit:"Mg/m³"
        },
        {
          name:"PM10 可吸入颗粒",
          value:60,
          unit:"Mg/m³"
        },
        {
          name:"CO2 二氧化碳",
          value:81,
          unit:""
        },
        {
          name:"SO2 二氧化硫",
          value:8,
          unit:"Mg/m³"
        },
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .title {
      margin: 40px;
      width: 940px;
      text-align: center;
      font-size: 40px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #D6E7F9;
      background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left {
        .value {
          font-size: 50px;
          font-family: Source Han Sans CN;
          font-weight: bold;
          color: #FFFFFF;
          text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
          background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-left: 10px;
        }
        .box-left-item {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          .icon {
            width: 75px;
            height: 77px;
            background-size: cover;
          }
          .name {
            margin-left: 21px;
            font-size: 32px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #FFFFFF;
          }
        }
      }
      .right {
        .item {
          height: 90px;
          margin-bottom: 20px;
          .item-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .name {
              width: 156px;
              height: 66px;
              word-break: break-all;
              font-size: 32px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFFFF;
            }
            .value {
              font-size: 50px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFFFF;
              text-shadow: 0px 4px 9px rgba(0,0,0,0.29);
              background: linear-gradient(0deg, #CAFFFF 0%, #CAFFFF 0%, #FFFFFF 0%, #00C0FF 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              .unit {
                font-size: 32px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #FFFFFF;
              }
            }
          }
          .line {
            width: 428px;
            height: 9px;
            margin-top: 25px;
          }
          .yellowbg {
            background: linear-gradient(270deg, #FFECCB, #FFE4A8, #FFC460);
          }
          .bluebg {
            background: linear-gradient(270deg, #CBF2FF, #85E6FF, #00C0FF);
          }
          .purplebg {
            background: linear-gradient(270deg, #E2B8F6, #B76FD8);
          }
          .redbg {
            background: linear-gradient(90deg, #FFCECE, #FF4949, #FFA7A7);
          }
        }
      }
    }
  }
</style>