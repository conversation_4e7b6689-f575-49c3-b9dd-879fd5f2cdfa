<!--生活服务-->
<template>
  <div class="rightTwo">
    <Ktitle titleText="生活服务" :width="'950'" />
    <div class="shfwWrap">
      <div class="ygcfClass">
        <div class="textInner">
          <div class="numWrap">
            <div class="numClass">1659</div>
            <div class="unitClass">家</div>
          </div>
          <div class="descClass">阳光厨房接入数</div>
        </div>
      </div>
      <div class="aizpClass">
        <div class="textInnerTwo">
          <div class="numWrap">
            <div class="numClass">463</div>
            <div class="unitClass">家</div>
          </div>
          <div class="descClass">AI抓拍系统接入企业数</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Ktitle from "@/components/title.vue"
export default {
  name: "shfw",
  components:{
    Ktitle
  },
  data() {
    return {}
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
.rightTwo {
  width: 940px;
  height: fit-content;
  box-sizing: border-box;
  margin: 30px 0 0 0;
  display: flex;
  flex-direction: column;
  align-items: center;

  // background-color: bisque;
  .shfwWrap {
    flex: 1;
    width: 100%;
    // background-color: bisque;
    display: flex;
    justify-content: space-evenly;
    box-sizing: border-box;
    margin: 30px 0 0 74px;

    .ygcfClass {
      width: 460px;
      height: 170px;
      background: url('~@/assets/publicService/yg.png') no-repeat center center;
      background-size: 100% 100%;
      flex-shrink: 0;
    }

    .aizpClass {
      width: 460px;
      height: 170px;
      background: url('~@/assets/publicService/ai.png') no-repeat center center;
      background-size: 100% 100%;
      flex-shrink: 0;
    }

    .textInner {
      top: 16px;
      right: 0;
      display: flex;
      flex-direction: column;
      margin-left: 220px;
    }

    .textInnerTwo {
      top: 16px;
      right: -120px;
      display: flex;
      flex-direction: column;
      margin-left: 220px;
    }

    .numWrap {
      display: flex;
      align-items: baseline;
      font-weight: bolder;
      background: linear-gradient(180deg, #FFFFFF 0%, #FFECCB 50.244140625%, #FFC460 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      .numClass {
        font-size: 60px;
        font-family: BN;
      }

      .unitClass {
        font-size: 32px;
      }
    }

    .descClass {
      width: 199px;
      word-break: break-all;
      font-size: 32px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #FFFFFF;
      margin-top: 30px;
    }
  }
}

</style>