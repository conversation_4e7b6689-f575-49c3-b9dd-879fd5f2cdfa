<!--常住人口趋势-->
<template>
  <div class="pr">
    <titleTwo :title-text="'常住人口趋势'" :width="'1957'"></titleTwo>
    <div class="prBox">
      <div class="prChart" id="prChart"></div>
      <div class="trendList">
        <div class="trendItem" v-for="(item,i) in indexData" :key="i">
          <img :src="item.img" alt="">
          <div class="info">
            <div class="value gold">{{item.value}} <span class="unit">{{item.unit}}</span> </div>
            <div class="name">{{item.name}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
export default {
  name: "PermanentResidence",
  data() {
    return {
      chartsData:[
        {
          name:"苏孟乡",
          value:25
        },
        {
          name:"秋滨街道",
          value:40
        },
        {
          name:"三江街道",
          value:30
        },
        {
          name:"西关街道",
          value:45
        },
        {
          name:"江南街道",
          value:80
        },
        {
          name:"汤溪镇",
          value:76
        },
        {
          name:"罗埠镇",
          value:66
        },
        {
          name:"洋埠镇",
          value:34
        }
      ],
      indexData:[
        {
          name:"本年新增累计计数",
          value:563,
          unit:"人",
          img:require("@/assets/safe/trend1.png")
        },
        {
          name:"本年死亡注销累计数",
          value:1004,
          unit:"人",
          img:require("@/assets/safe/trend2.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.initChart();
  },
  methods: {
    initChart() {
      let myChart = this.$echarts.init(document.getElementById("prChart"));
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: "horizontal",
          // icon: "circle",
          itemGap: 45,
          right: 75,
          top:15,
          textStyle: {
            color: "#D6E7F9",
            fontSize: 28,
          },
        },
        grid: {
          left: "8%",
          right: "6%",
          top: "22%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.chartsData.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: "单位：万人",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 40,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "常住人口数",
            type: "line", // 直线ss
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#00C0FF",
              },
            },
            data: this.chartsData.map(item => item.value),
          }
        ],
      };
      myChart.setOption(option)
      myChart.getZr().on('mousemove', param => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .pr {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    .prBox {
      width: 1811px;
      height: 525px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .prChart {
        width: 1259px;
        height: 436px;
      }
      .trendList {
        width: fit-content;
        height: 390px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-direction: column;
        .trendItem {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          img {
            width: 301px;
            height: 172px;
          }
          .info {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            flex-direction: column;
            margin-left: 36px;
            .name {
              font-size: 32px;
              font-family: Source Han Sans CN;
              font-weight: 400;
              color: #FFFFFF;
              white-space: nowrap;
            }
            .value {
              font-size: 60px;
              font-family: BN;
              font-weight: 400;
            }
            .unit {
              font-size: 25px;
              margin-top: 15px;
              white-space: nowrap;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
</style>