<template>
  <div class="container">
    <img :src="img" alt="" class="leftPage">
    <div class="number1" @click="showDialog('生态宜居')"></div>
    <div class="number2" @click="showDialog('健康舒适')"></div>
  </div>
</template>

<script>
export default {
  name: "UrbanSignsLeft",
  data() {
    return {
      img: require("@/assets/cstz/左侧.png")
    }
  },
  computed: {},
  mounted() {

  },
  methods: {
    showDialog(str) {
      this.$emit('showDialog',str)
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
  .container {
    .leftPage {
      width: 2097px;
      height: 1927px;
    }
    .number1 {
      width: 55px;
      height: 54px;
      position: absolute;
      top: 114px;
      left: 1894px;
      cursor: pointer;
    }
    .number2 {
      width: 55px;
      height: 54px;
      position: absolute;
      top: 514px;
      left: 1894px;
      cursor: pointer;
    }
  }
</style>