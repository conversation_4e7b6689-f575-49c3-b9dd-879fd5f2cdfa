<template>
  <div id="lineChart"></div>
</template>

<script>
export default {
  data () {
    return {
      charts1Data: [
        {
          name: '招商招才',
          value: 1,
        },
        {
          name: '党群工作部',
          value: 1,
        },
        {
          name: '党政综合部',
          value: 3,
        },
        {
          name: '国资监管中心',
          value: 1,
        },
        {
          name: '建设更新部',
          value: 2,
        },
        {
          name: '经济发展部',
          value: 5,
        },
        {
          name: '社会事务部',
          value: 3,
        },
        {
          name: '社会治理部',
          value: 4,
        },
        {
          name: '乡村振兴部',
          value: 2,
        },
        {
          name: '行政服务中心',
          value: 2,
        },
        {
          name: '宣传部',
          value: 1,
        },
        {
          name: '行政执法分局',
          value: 1,
        },
        {
          name: '公安局江南分局',
          value: 3,
        },
        {
          name: '三江街道',
          value: 1,
        },
        {
          name: '苏孟乡',
          value: 1,
        }
      ]
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.initCharts1()
    })
  },
  methods: {
    initCharts1 () {
      let myChart = this.$echarts.init(document.getElementById("lineChart"))
      let option = {
        tooltip: {
          trigger: "axis",
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: "5%",
          right: "0%",
          top: "10%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: this.charts1Data.map(item => item.name),
            axisLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)", // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              rotate:45,
              textStyle: {
                color: "#D6E7F9",
                fontSize: 28,
              },
              margin: 26
            },
          },
        ],
        yAxis: [
          {
            name: "单位：个数",
            type: "value",
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: [0, 0, 10, 30]
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: "#D6E7F9",
              },
            },
          },
          {
            name: "",
            type: "value",
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: "#D6E7F9",
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: "rgb(119,179,241,.4)",
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 12,
                color: "#D6E7F9",
              },
            },
          },
        ],
        series: [
          {
            name: "单位个数",
            type: "line", // 直线ss
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: "#00C0FF",
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                    offset: 0.2,
                    color: 'rgba(0, 192, 255, 0)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255,.8)',
                  },
                  ]),
                },
              },
            },
            data: this.charts1Data.map(item => item.value),
          }
        ],
      }
      myChart.setOption(option)
    },
  }
}
</script>

<style scoped lang="less">
#lineChart {
  width: 100%;
  height: 100%;
  // background-color: blueviolet;
}
</style>