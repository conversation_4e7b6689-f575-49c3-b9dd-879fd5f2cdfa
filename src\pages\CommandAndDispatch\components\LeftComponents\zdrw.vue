<template>
  <div class="container">
    <Ktitle :titleText="'重大任务'" width="939.6" :show-date="false">
      <div
        class="yjzhImg"
        @click="
          showDialog2 = true
          dialogTitle2 = '新建任务'
        "
      ></div>
    </Ktitle>
    <!-- <div class="container-tab">
      <div
        class="tab"
        v-for="(item, i) in tabList"
        :key="i"
        :class="{ activeTab: chooseIndex == i }"
        @click="tabChange(i)"
      >
        {{ item.label }}
      </div>
    </div> -->
    <div class="container-indexes">
      <div class="index-item">
        <div class="number">{{ tfsj }}</div>
        <div class="text">突发事件</div>
      </div>
      <div class="index-item">
        <div class="number">{{ zdrw }}</div>
        <div class="text">重大活动</div>
      </div>
    </div>
    <div class="container-searchBox">
      <el-input
        placeholder="请输入任务名称"
        v-model="zdrwQueryParams.name"
        style="width: 310px; height: 55px"
      ></el-input>
      <el-select
        v-model="zdrwQueryParams.type"
        placeholder="请选择任务类型"
        @change="zdrwQuery"
        clearable
        style="width: 312px"
      >
        <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <div class="btn" @click="zdrwQuery">查询</div>
      <div class="btn" @click="reset">重置</div>
    </div>
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      @rowClick="rowClick"
    ></zhtxTable>
    <!--表格详情-->
    <Commondialog :title="dialogTitle" :dialogFlag="showDialog" @close="showDialog = false">
      <CommonDialogDetail :detailData="itemObj" :keyDesc="keyList" v-if="showDialog"></CommonDialogDetail>
    </Commondialog>
    <!--新建任务-->
    <Commondialog :title="dialogTitle2" :dialogFlag="showDialog2" @close="showDialog2 = false" :dialog-width="'1030px'">
      <addZdrw @close="showDialog2 = false" v-if="showDialog2"/>
    </Commondialog>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from '@/components/zhtxTable'
import Commondialog from '@/components/Commondialog'
import CommonDialogDetail from '@/components/CommonDialogDetail'
import addZdrw from '@/pages/CommandAndDispatch/components/LeftComponents/childComponents/addZdrw'
import {getZdrwList} from "@/api/EventTask";
export default {
  name: 'zdrw',
  data() {
    return {
      tabList: [
        {
          label: '全部任务',
          value: '1',
        },
        {
          label: '当前任务',
          value: '2',
        },
      ],
      typeList: [
        {
          label:"环境保护",
          value:"环境保护"
        },
        {
          label:"经济发展",
          value:"经济发展"
        },
        {
          label:"文化发展",
          value:"文化发展"
        },
        {
          label:"社区治理",
          value:"社区治理"
        },
        {
          label:"城市治理",
          value:"城市治理"
        },
        {
          label:"应急响应",
          value:"应急响应"
        },
        {
          label:"基础设施",
          value:"基础设施"
        },
        {
          label:"卫生健康",
          value:"卫生健康"
        }
      ],
      chooseIndex: 0,
      zdrwQueryParams: {
        name: '',
        type: '',
      },
      tfsj: '35',
      zdrw: '27',
      tableData: [],
      titleList: [
        {
          label: '标题',
          key: 'title',
          flex: 3,
        },
        {
          label: '类型',
          key: 'type',
          flex: 2,
        },
        {
          label: '事件等级',
          key: 'level',
          flex: 1,
          isColor: true,
        },
        {
          label: '状态',
          key: 'status',
          flex: 1,
          isColor: true,
        },
      ],
      dialogTitle: '',
      dialogTitle2: '',
      showDialog: false,
      showDialog2: false,
      itemObj: {},
      keyList: [
        {
          label: '任务类型',
          key: 'type',
          isHalf: true,
        },
        {
          label: '发生时间',
          key: 'date',
          isHalf: true,
        },
        {
          label: '事件等级',
          key: 'level',
          isHalf: true,
        },
        {
          label: '事件状态',
          key: 'status',
          isHalf: true,
        },
        {
          label: '内容详情',
          key: 'content',
        },
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable,
    Commondialog,
    CommonDialogDetail,
    addZdrw,
  },
  computed: {},
  mounted() {
    this.init()
    this.$EventBus.$on('commandSuccess', () => {
      this.init()
    })
  },
  methods: {
    init() {
      this.getNumber()
      this.zdrwQuery()
    },
    tabChange(i) {
      this.chooseIndex = i
      this.zdrwQueryParams.type = this.tabList[this.chooseIndex].value
      if (i == 0) {
        this.tableData = [
          {
            title: '山洪爆发',
            type: '自然灾害',
            level: '平急',
            status: '处置中',
            levelColor: 'orange',
            statusColor: 'yellow',
            date: '2024-5-12 18:43:22',
            content:
              '受持续数日的强降雨影响，XX地区山区河流水位急剧上升，于12日晚间至13日凌晨达到峰值，导致多条小流域突发山洪。山洪暴发伴随大量泥沙、石块冲刷而下，造成沿途桥梁、道路损毁，多处房屋被淹，农田受损严重。据初步统计，此次灾害影响区域达数百平方公里，直接威胁到数千名居民的生命安全。',
          },
          {
            title: '交通事故',
            type: '公共安全',
            level: '平急',
            status: '完成',
            levelColor: 'orange',
            statusColor: 'green',
            date: '2024-5-13 8:37:57',
            content:
              '市外环高速公路上发生了一起涉及多车的严重交通事故。事故起因初步判断为一辆载重货车因刹车系统故障，未能及时减速，在高速行驶中失控，与前方正在缓慢驶出服务区的一辆小型客车发生追尾碰撞。撞击瞬间产生的强大冲击力导致小型客车侧翻，并引发了连锁反应，波及周围正常行驶的多辆车辆，其中包括两辆轿车和一辆面包车，现场车辆碎片散落，交通瞬间陷入瘫痪。',
          },
          {
            title: '火灾',
            type: '应急响应',
            level: '平急',
            status: '完成',
            levelColor: 'orange',
            statusColor: 'green',
            date: '2024-5-13 14:14:28',
            content:
              '一户位于15层的出租房突发火灾。火势迅速蔓延，浓烟滚滚，火光映照着夜空。初步调查显示，火灾可能由电路老化短路引起，加上室内堆放过多易燃物品，火势得以快速扩散',
          },
          {
            title: '交通拥堵',
            type: '交通管理',
            level: '紧急',
            status: '完成',
            levelColor: 'red',
            statusColor: 'green',
            date: '2024-5-1 9:47:11',
            content:
              '金华市小冰岛作为热门旅游目的地吸引了大量国内外游客和返乡探亲的旅客。加之市民出游热情高涨，导致市内及周边交通流量激增，超出道路设计容量，引发了持续性的交通拥堵。',
          },
          {
            title: '电线杆倒塌',
            type: '城市治理',
            level: '紧急',
            status: '处置中',
            levelColor: 'red',
            statusColor: 'yellow',
            date: '2024-5-13 12:24:16',
            content:
              '连日来的暴雨侵蚀和地基松动，一根承载着高压电线的老化电线杆不堪重负，最终在强风的冲击下轰然倒塌。倒塌的电线杆不仅砸中了停靠在路边的两辆汽车，还导致周边区域的电力供应中断，严重影响了街道的交通秩序和附近居民的生活用电。',
          },
          {
            title: '人员异常聚集',
            type: '应急响应',
            level: '平急',
            status: '处置中',
            levelColor: 'orange',
            statusColor: 'yellow',
            date: '2024-5-13 16:24:37',
            content:
              '广场原计划举行一场小型文化展览活动，吸引了众多市民前来参与。然而，随着社交媒体上一条未经证实的“神秘嘉宾”到场的消息迅速传播，人群规模远超预期，短时间内异常聚集了近万人，远远超过了活动组织方的预估和场地承载能力。人群密集，流动性大，给现场安全带来了巨大压力。',
          },
          {
            title: 'PM2.5浓度过高',
            type: '环境监测',
            level: '平急',
            status: '完成',
            levelColor: 'orange',
            statusColor: 'green',
            date: '2024-5-14 11:34:25',
            content:
              '自5月1日起，随着假期出行高峰的到来，XX市多条主要道路出现了长时间、大面积的交通拥堵，车辆怠速运行时间显著延长。车辆排放的二氧化碳、氮氧化物、颗粒物等污染物大量增加，其中PM2.5浓度在某些监测站点达到了本年度最高水平，平均浓度较平日升高了约35%。空气质量指数（AQI）多次超过150，达到中度污染级别，部分时段甚至接近重度污染标准，对市民健康构成了潜在威胁。',
          },
          {
            title: '交通拥堵',
            type: '交通管理',
            level: '紧急',
            status: '完成',
            levelColor: 'red',
            statusColor: 'green',
            date: '2024-5-1 9:47:11',
            content:
              '金华市小冰岛作为热门旅游目的地吸引了大量国内外游客和返乡探亲的旅客。加之市民出游热情高涨，导致市内及周边交通流量激增，超出道路设计容量，引发了持续性的交通拥堵。',
          },
        ]
      }
    },
    getNumber() {
      getZdrwList(this.zdrwQueryParams).then(res => {
        this.zdrw = res.data.data.zdrwcnt
        this.tfsj = res.data.data.tfsjcnt
      })
    },
    zdrwQuery() {
      getZdrwList(this.zdrwQueryParams).then(res => {
        this.tableData = res.data.data.list.map(item => ({
          title: item.name,
          type: item.type,
          level: item.urgency,
          status: item.taskPhase,
          levelColor: this.getLevelColor(item.urgency),
          statusColor: this.getStatusColor(item.taskPhase),
          date: item.releaseTime,
          content: item.content
        }))
      })
    },
    reset() {
      this.zdrwQueryParams = {
        name: '',
        type: '',
      }
      this.zdrwQuery()
    },
    rowClick(item) {
      this.itemObj = item
      this.dialogTitle = '任务详情'
      this.showDialog = true
    },
    getLevelColor(str) {
      switch (str) {
        case "紧急":
          return "red"
        case "特急":
          return "pink"
        case "平急":
          return "orange"
        case "其它":
          return "green"
      }
    },
    getStatusColor(str) {
      switch (str) {
        case "待分拨":
          return "white"
        case "待认领":
          return "blue"
        case "待处理":
          return "yellow"
        case "已完成":
          return "green"
      }
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.container {
  .yjzhImg {
    width: 67px;
    height: 67px;
    background: url('~@/assets/Command/一键指挥.png');
    background-size: cover;
    cursor: pointer;
  }
  .container-tab {
    display: flex;
    justify-content: center;
    align-items: center;
    .tab {
      width: 471px;
      height: 73px;
      line-height: 65px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 32px;
      color: #ffffff;
      text-align: center;
      background: url('~@/assets/Command/tab1.png') no-repeat;
      background-size: cover;
      cursor: pointer;
    }
    .activeTab {
      background: url('~@/assets/Command/tab2.png') no-repeat;
    }
  }
  .container-indexes {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    height: 256px;
    margin: 36px 0 22px 0;
    .index-item {
      width: 220px;
      height: 256px;
      background: url('~@/assets/Command/indexBg.png') no-repeat;
      background-size: cover;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      cursor: pointer;
      .number {
        font-family: BN;
        font-weight: 400;
        font-size: 64px;
        color: #9aa9bf;
        background: linear-gradient(
          180deg,
          #ffffff 0%,
          #ffb637 50.244140625%,
          #fffcf3 53.0029296875%,
          #ffd8a1 99.365234375%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-top: 30px;
      }
      .text {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 31px;
        color: #ffffff;
        margin-top: 70px;
      }
    }
  }
  .container-searchBox {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .btn {
      width: 136px;
      height: 61px;
      line-height: 61px;
      text-align: center;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      background: url('~@/assets/Command/btn.png') no-repeat;
      background-size: cover;
      cursor: pointer;
    }
  }
}
</style>
