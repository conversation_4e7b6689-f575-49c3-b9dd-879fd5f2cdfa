<!--产销情况分析左侧-->
<template>
  <div>
    <div class="cxqkfxLeft" id="cxqkfxLeft"></div>
  </div>
</template>

<script>
import imgUrl from "@/assets/common/piebg.png";
export default {
  name: "level",
  data() {
    return {
      charts2Data:[],
    }
  },
  components:{

  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let that = this
        this.charts2Data = [
          {
            name: "石材",
            value: 37.2
          },
          {
            name: "钢筋",
            value: 24.8
          },
          {
            name: "水泥",
            value: 27.28
          },
          {
            name: "海鲜",
            value: 18.6
          },
          {
            name: "食材",
            value: 16.12
          }
        ]
        let myChart = this.$echarts.init(document.getElementById("cxqkfxLeft"));
        let imgUrl = require('@/assets/common/piebg.png')
        let option = {
          color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8', '#FD852E', '#FF4949', '#0594C3', '#009D9D', '#A47905'],

          tooltip: {
            trigger: 'item',
            // formatter: '{b}: <br/> {d}%',
            formatter: '{b}: <br/> {c}个<br/> {d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          legend: {
            orient: 'vertical',
            left: '48%',
            top: '20%',
            bottom: '0%',
            icon: 'circle',
            itemGap: 20,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: '#ffffff',
                  padding: [0, 20, 0, 15]
                },
                value: {
                  fontSize: 25,
                  color: '#FFC460',
                  // padding: [10, 0, 0, 15]
                },
              }
            },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              that.serverNum = total;
              var p = ((tarValue / total) * 100).toFixed(2)
              return '{name|' + name + '}{value|' + p + '%}'
            },
          },
          graphic: [
            {
              type: "image",
              id: "logo",
              left: "10.8%",
              top: "10.4%",
              z: -10,
              bounding: "raw",
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.8, 0.8], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: '',
              type: 'pie',
              radius: ['55%', '80%'],
              center: ['25%', '54%'],
              roseType: '',
              itemStyle: {
                borderRadius: 0,
              },
              label: {
                show: false,
              },
              data: this.charts2Data,
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
    }
  },
  watch: {}
}
</script>

<style scoped>
.cxqkfxLeft {
  width: 935px;
  height: 307px;
}
</style>