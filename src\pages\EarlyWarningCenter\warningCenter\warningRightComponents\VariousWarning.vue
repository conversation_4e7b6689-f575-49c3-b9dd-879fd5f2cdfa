<!--各类预警-->
<template>
  <div>
    <titleTwo :titleText="'各类预警'" :width="'1935.6'"></titleTwo>
    <div class="itemBox">
      <div class="item" v-for="(item,i) in warnings" :key="i">
        <div class="icon" :style="{backgroundImage:'url('+item.icon+')'}"></div>
        <div class="info">
          <div class="name">{{item.name}}</div>
          <div class="value">{{item.value}} <span class="unit">个</span> </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import titleTwo from "@/components/titleTwo";
import {getYjfxList} from "@/api/warning";
export default {
  name: "VariousWarning",
  data() {
    return {
      warnings:[
        {
          key:"zdbjTotal",
          name:"重点部件检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w1.png")
        },
        {
          key:"zdqyTotal",
          name:"重点区域检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w2.png")
        },
        {
          key:"jtcxTotal",
          name:"交通出行检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w3.png")
        },
        {
          key:"aqsctTotal",
          name:"安全生产检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w4.png")
        },
        {
          key:"zrzhTotal",
          name:"自然灾害检测预警",
          value:985,
          icon:require("@/assets/home/<USER>/w5.png")
        },
        {
          key:"sthjTotal",
          name:"生态环境检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w6.png")
        },
        {
          key:"ggwsTotal",
          name:"公共卫生检测预警",
          value:142,
          icon:require("@/assets/home/<USER>/w7.png")
        }
      ]
    }
  },
  components:{
    titleTwo
  },
  computed: {},
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      getYjfxList().then(res => {
        this.warnings.forEach((item,i) => {
          item.value = res.data.data[item.key]
        })
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
.unit {
  font-size: 32px;
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #D6E7F9;
  background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: -5px;
}
.itemBox {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  width: 1935.6px;
  height: 315px;
  margin-top: 55px;
  .item {
    width: 480px;
    height: 145px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .icon {
      width: 170px;
      height: 145px;
      background-size: cover;
    }
    .info {
      margin-left: 26px;
      .value {
        font-size: 60px;
        font-family: BN;
        font-weight: 400;
        color: #FFFFFF;
        background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .name {
        font-size: 32px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
  }
}
</style>