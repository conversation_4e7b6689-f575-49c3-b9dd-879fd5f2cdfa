<template>
  <div class="services">
    <div class="service" v-for="(item,i) in list" :key="i">
      <div class="name">{{item.name}}</div>
      <div class="value blue">{{item.value}}</div>
      <div class="img"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "services",
  data() {
    return {
      list:[
        {
          name:"本月政策办件",
          value:479
        },
        {
          name:"事项办件",
          value:3442
        },
        {
          name:"申报资金(万元)",
          value:9196
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .services {
    width:100%;
    height:409px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 105px;
    .service {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: column;
      .name {
        font-size: 32px;
        font-family: Source <PERSON>;
        font-weight: bold;
        color: #FFFFFF;
      }
      .value {
        font-size: 72px;
        font-family: DIN;
        font-weight: bold;
        margin-top: 36px;
      }
      .img {
        width: 396px;
        height: 294px;
        background: url("@/assets/partyBuilding/服务.png");
        background-size: cover;
      }
    }
  }
</style>