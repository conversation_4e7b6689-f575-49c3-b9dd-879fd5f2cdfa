<!--指令下达-->
<template>
  <div class="container">
    <el-form ref="form" :model="form" :rules="rules">
      <div class="flex">
        <el-form-item label="任务名称：" prop="name">
          <el-input
            style="width: 250px;height: 55px;"
            placeholder="请输入"
            v-model="form.name">
          </el-input>
        </el-form-item>
        <el-form-item label="发生时间：" prop="releaseTime">
          <el-date-picker
            v-model="form.releaseTime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
            style="width: 250px;height: 55px;"
            clearable
          ></el-date-picker>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="任务类型：" prop="type">
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.type"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务区域：" prop="street">
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.street"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in streetList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="处置部门：" prop="disposalDepartment" >
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.disposalDepartment"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in departOption"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="紧急程度：" prop="urgency">
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.urgency"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in urgencyList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="处置人：" prop="disposalPerson" >
          <el-input
            style="width: 250px;height: 55px;"
            v-model="form.disposalPerson"
            placeholder="请输入处置人"
          />
        </el-form-item>
        <el-form-item label="联系方式：" prop="lxfs" >
          <el-input
            style="width: 250px;height: 55px;"
            v-model="form.lxfs"
            placeholder="请输入联系方式"
          />
        </el-form-item>
      </div>
      <div class="flex">
        <el-form-item label="突发事件：" prop="tfsj" >
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.tfsj"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in BooleanList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="重大活动：" prop="zdrw" >
          <el-select
            style="width: 250px;height: 55px;"
            v-model="form.zdrw"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in BooleanList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="内容详情：" prop="content">
        <el-input
          style="width: 780px;"
          type="textarea"
          :rows="4"
          placeholder="请输入"
          v-model="form.content">
        </el-input>
      </el-form-item>
      <el-form-item style="margin-left: 740px;">
        <div class="flex" style="width: 150px">
          <div class="cancel" @click="close">取消</div>
          <div class="confirm" @click="submitForm">确定</div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import moment from "moment";
import {addTask} from "@/api/EventTask";
import {getDicts} from "@/api/common";

export default {
  name: "zlxd",
  data() {
    return {
      form: {
        name: null,
        type: null,
        street: null,
        disposalDepartment: null,
        disposalPerson: null,
        urgency: null,
        content: null,
        lxfs: null,
        flag:"1",
        releaseTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      },
      rules: {
        name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' },
        ],
        releaseTime: [
          { required: true, message: '请选择发生时间', trigger: 'change' },
        ],
        urgency: [
          { required: true, message: '请选择紧急程度', trigger: 'change' },
        ],
        type: [
          { required: true, message: '请选择任务类型', trigger: 'change' },
        ],
        street: [
          { required: true, message: '请选择任务区域', trigger: 'change' },
        ],
        taskType: [
          { required: true, message: '请选择任务类型', trigger: 'change' },
        ],
        content: [
          { required: true, message: '请输入内容详情', trigger: 'blur' },
        ],
        disposalDepartment: [
          { required: true, message: '请输入处置部门', trigger: 'blur' },
        ],
        disposalPerson: [
          { required: true, message: '请输入处置人', trigger: 'blur' },
        ],
        lxfs: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
        ],
        tfsj: [
          { required: true, message: '请选择是否突发事件', trigger: 'change' },
        ],
        zdrw: [
          { required: true, message: '请选择是否重大活动', trigger: 'change' },
        ],
      },
      urgencyList: [
        {
          label:"紧急",
          value:"紧急"
        },
        {
          label:"特急",
          value:"特急"
        },
        {
          label:"平急",
          value:"平急"
        },
        {
          label:"其它",
          value:"其它"
        }
      ],
      typeList: [
        {
          label:"环境保护",
          value:"环境保护"
        },
        {
          label:"经济发展",
          value:"经济发展"
        },
        {
          label:"文化发展",
          value:"文化发展"
        },
        {
          label:"社区治理",
          value:"社区治理"
        },
        {
          label:"城市治理",
          value:"城市治理"
        },
        {
          label:"应急响应",
          value:"应急响应"
        },
        {
          label:"基础设施",
          value:"基础设施"
        },
        {
          label:"卫生健康",
          value:"卫生健康"
        }
      ],
      streetList: [
        {
          label:"三江街道",
          value:"三江街道"
        },
        {
          label:"秋滨街道",
          value:"秋滨街道"
        },
        {
          label:"西关街道",
          value:"西关街道"
        },
        {
          label:"江南街道",
          value:"江南街道"
        },
        {
          label:"汤溪镇",
          value:"汤溪镇"
        },
        {
          label:"罗埠镇",
          value:"罗埠镇"
        },
        {
          label:"洋埠镇",
          value:"洋埠镇"
        },
        {
          label:"苏孟乡",
          value:"苏孟乡"
        }
      ],
      BooleanList: [
        {
          label:"是",
          value:"是"
        },
        {
          label:"否",
          value:"否"
        }
      ],
      departOption: []
    }
  },
  props: {
    id: {
      type: String,
      default: "",
      require: true
    }
  },
  computed: {},
  mounted() {
    this.getDepartList()
    this.reset()
    console.log(this.id,"id");
  },
  methods: {
    close() {
      this.$emit("close")
    },
    // 表单重置
    reset() {
      this.form = {
        name: null,
        type: null,
        street: null,
        disposalDepartment: null,
        disposalPerson: null,
        urgency: null,
        content: null,
        lxfs: null,
        flag:"1",
        tfsj: null,
        zdrw: null,
        releaseTime: moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      };
    },
    getDepartList() {
      getDicts("fkq_dept").then(res => {
        this.departOption = res.data.data
      })
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          addTask(this.form).then(response => {
            if (response.data.code == 200) {
              this.$EventBus.$emit("commandSuccess")
              this.$message.success("下达成功");
              this.close()
              this.getList();
            } else {
              this.$message.error("下达失败");
            }
          });
        }
      })
    }
  },
  watch: {

  }
}
</script>

<style scoped lang="less">
  .container {
    width: calc(100% - 64px);
    margin: 32px;
    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .cancel {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 32px;
      color: #CEF2FF;
      line-height: 40px;
      text-align: left;
      font-style: normal;
      cursor: pointer;
    }
    .confirm {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 32px;
      background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
      -webkit-background-clip: text;
      color: transparent;
      cursor: pointer;
    }
    /deep/ .el-form-item {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      .el-form-item__label {
        width: 180px;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28px;
        color: #CEF2FF;
        text-align: left;
        font-style: normal;
      }
    }
  }
</style>