<template>
  <div class="pie">
    <pie3D
      v-if="loadPie"
      id="disposePie"
      :options="charts1Data"
      :colors="colors"
      :internalDiameterRatio="0.8"
      :scaleCustom="0.88"
      :zHeight="0.02"
      :widthCustom="935" :height-custom="307"
      :type="false"
      :legendCustom="legendCustom"
    ></pie3D>
  </div>
</template>

<script>
import pie3D from '@/components/pie3D'
import {getEventCzfx} from "@/api/EventTask";
export default {
  name: 'charts1',
  data() {
    return {
      loadPie:false,
      charts1Data: [],
      colors: ['#FF4A48', '#FDC260', '#00BEFC', '#22E4E5'],
      legendCustom: {
        itemWidth: 15,
        itemHeight: 15,
        itemGap: 30,
        left: '55%',
        top: '25%',
      },
    }
  },
  components: {
    pie3D,
  },
  computed: {},
  mounted() {
    this.getPieData()
  },
  methods: {
    getPieData() {
      getEventCzfx({type: "2"}).then(res => {
        console.log(res,"222");
        this.charts1Data = Object.entries(res.data.data[0]).map(item => ({
          name: this.getLabel(item[0]),
          value: item[1],
        }))
        this.loadPie = true
      })
    },
    getLabel(key) {
      switch (key) {
        case 'ja':
          return '结案'
        case 'sl':
          return '受理'
        case 'bl':
          return '办理'
        case 'sb':
          return '上报'
      }
    }
  },
  watch: {},
}
</script>

<style scoped lang="less">
.pie {
  width: 800px;
  .pie-sta {
    margin-top: 50px;
    margin-left: 70px;
    color: #fff;
    font-size: 32px;
    .sta-value {
      margin-left: 60px;
      font-size: 60px;
      background: linear-gradient(to bottom, #ffeccb, #ffc460); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
      -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
      -webkit-text-fill-color: transparent; /*给文字设置成透明*/
      font-weight: bold;
    }
  }
}
</style>
