html {
  font-size: 14px;
  height: 100%;
}

body {
  margin: 0;
  height: 100%;
  font-family: 'Microsoft YaHei' !important;
  // filter: grayscale(100%);
}
//滚动条样式
// ::-webkit-scrollbar {
//   width: 0;
//   height: 0;
// }

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #0c1431;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
  border-radius: 4px;
  background-color: #0c1431;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

::-webkit-scrollbar-thumb:hover {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

//小滚动条
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.min_scroll::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  background-color: #0c1431;
}

/*定义滚动条轨道 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-track {
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);*/
  border-radius: 4px;
  background-color: #0c1431;
}

/*定义滑块 内阴影+圆角*/
.min_scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

.min_scroll::-webkit-scrollbar-thumb:hover {
  border-radius: 4px;
  /*-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);*/
  background-color: #539ff7;
}

@pageWidth: 1920px;
@pageHeight: 1080px;

// @font-face {
//   font-family: 'pangmen';
//   src: url('@/assets/font/PangMenZhengDaoBiaoTiTi-1.ttf');
// }
// @font-face {
//   font-family: 'DIN-Bold';
//   src: url('@/assets/font/DIN-Bold.otf');
// }
// @font-face {
//   font-family: 'DIN-Black';
//   src: url('@/assets/font/DIN-Black.otf');
// }
// @font-face {
//   font-family: 'DIN-Medium';
//   src: url('@/assets/font/DIN-Medium.otf');
// }
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/font/YouSheBiaoTiHei-2.ttf');
}

//黄白渐变
.yellow_linear {
  background: linear-gradient(0deg, #fff120 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

//蓝白渐变
.blue_linear {
  background: linear-gradient(0deg, #52c2f7 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//蓝白渐变1
.blue_linear1 {
  background: linear-gradient(0deg, #b9ccff 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//蓝色渐变
.blue_only_linear {
  background: linear-gradient(0deg, #52c3f7 0%, #a8e0fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//绿色渐变
.green_linear {
  background: linear-gradient(0deg, #51f67d 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//红色渐变
.red_linear {
  background: linear-gradient(0deg, #ff4f34 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//橙色渐变
.orign_linear {
  background: linear-gradient(0deg, #ff7434 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//橙色渐变
.orign_linear {
  background: linear-gradient(0deg, #ff7434 0%, #fff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//省略号
.cut_text {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  white-space: nowrap;
  outline: none;
}
//自定义弹框样式
.custom-class-dialog {
  margin-top: 240px !important;
}
//可点击
.cursor {
  cursor: pointer;
}

.gold {
  background: linear-gradient(to bottom, #ffeccb, #ffffff, #ffc460, #ffe2b0, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
  text-align: left;
}

.blue {
  background: linear-gradient(to bottom, #cbf2ff, #ffffff, #00c0ff, #80e0ff, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.red {
  background: linear-gradient(to bottom, #ffcdcd, #ffffff, #ff4949, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.green {
  background: linear-gradient(to bottom, #caffff, #ffffff, #22e8e8, #91f4f4, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}

.el-select .el-input .el-input__inner {
  border: 1px solid #acd8f4;
  background: #0e2951;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fefefe;
  border-radius: 10px;
  height: 55px;
}

.el-input.is-disabled .el-input__inner {
  border: 1px solid #acd8f4;
  background: #0e2951;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fefefe;
  border-radius: 10px;
  height: 55px;
  cursor: not-allowed;
}

.el-input .el-input__inner {
  border: 1px solid #acd8f4;
  background: #0e2951;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fefefe;
  border-radius: 10px;
  height: 100%;
}

.el-textarea__inner {
  background: #0e2951;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #ffffff;
  line-height: 32px;
  text-stroke: 1px rgba(0, 0, 0, 0);
  text-align: left;
  font-style: normal;
  border: 1px solid #acd8f4;
}

.el-textarea.is-disabled .el-textarea__inner {
  background: #0e2951;
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 32px;
  color: #ffffff;
  line-height: 32px;
  text-stroke: 1px rgba(0, 0, 0, 0);
  text-align: left;
  font-style: normal;
  border: 1px solid #acd8f4;
  cursor: not-allowed;
}

.el-select-dropdown {
  background-color: rgba(2, 16, 30, 0.9) !important;
  width: 425px;
  border: 1px solid #409eff;
  border-radius: 10px;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: #254674;
}

.el-select-dropdown__item {
  color: #ffffff;
}

.el-picker-panel {
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  line-height: 30px;
  margin: 5px 0;
  background: rgba(0, 28, 54, 0.9);
  color: #ffffff;
  border: 1px solid #409eff;
}

.el-picker-panel__icon-btn,
.el-date-picker__header-label,
el-select-dropdown__item {
  color: #ffffff;
}

th {
  color: #ffffff !important;
}

.esri-button {
  float: right;
  padding: 0 16px;
  height: 35px;
  cursor: pointer;
  /* background-image: linear-gradient(180deg,hsla(0,0%,100%,.4),hsla(0,0%,100%,0)); */
  border: 1px solid hsla(0, 0%, 100%, 0.30980392156862746);
  box-sizing: border-box;
  transition: 0.3s;
  line-height: 30px;
  font-size: 25px;
  background: linear-gradient(180deg, hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
  color: white;
}
.esri-button:hover {
  background: #2960cb;
  color: white;
}
.esri-widget {
  font-size: 30px;
  background-color: rgba(36, 36, 36, 0);
}
.esri-button {
  float: none;
}
.esri-daylight {
  width: auto;
  font-size: 14px;
}
.esri-area-measurement-3d__measurement,
.esri-direct-line-measurement-3d__measurement {
  background-color: rgba(36, 36, 36, 0);
}
.esri-area-measurement-3d__units-select,
.esri-direct-line-measurement-3d__units-select,
.esri-area-measurement-3d__units-select option,
.esri-direct-line-measurement-3d__units-select option {
  background: none;
}
.esri-building-level-picker-label--empty {
  font-size: 20px;
}
h3.esri-widget__heading {
  font-size: 25px;
}
.esri-building-disciplines-tree-node__label {
  font-size: 25px;
}
.esri-building-level-picker__label-container {
  height: 140px;
}
.esri-building-level-picker__arrow-up,
.esri-building-level-picker__arrow-down {
  font-size: 50px;
  color: white;
}

.el-message-box {
  width: 1680px;
  padding-bottom: 40px;
  font-size: 72px;
  .el-message-box__header {
    padding: 60px 60px 40px;
  }
  .el-message-box__title {
    font-size: 72px;
  }
  .el-message-box__content {
    font-size: 56px;
    padding: 40px 60px;
  }
  .el-message-box__btns {
    padding: 20px 60px 0;
    .el-button--small {
      padding: 36px 60px;
      font-size: 48px;
      border-radius: 12px;
    }
  }
  .el-message-box__headerbtn{
    font-size: 64px;
  }
}

.s-c-yellow-gradient {
  background: linear-gradient(to bottom, #ffebce, #ffffff, #ffc559, #ffffff);
  -webkit-background-clip: text;
  color: transparent;
}