<!--事件类型分析图表-->
<template>
  <div>
    <div class="typeAnalyseChart" id="typeAnalyseChart"></div>
  </div>
</template>

<script>
import {getEventTypefx} from "@/api/EventTask";
import imgUrl from "@/assets/common/piebg.png";
export default {
  name: 'TypeAnalyseLeft',
  data() {
    return {
      charts2Data: [
        { name: '公共安全', value: 142 },
        { name: '城市治理', value: 117 },
        { name: '政务服务', value: 164 },
        { name: '环境监测', value: 172 },
        { name: '市政设施', value: 112 },
        { name: '交通管理', value: 220 },
        { name: '应急响应', value: 154 },
        { name: '市场监管', value: 147 },
        { name: '城市规划与发展', value: 112 },
        { name: '市民投诉与建议', value: 132 },
      ],
    }
  },
  computed: {},
  mounted() {
    this.initCharts()
  },
  methods: {
    initCharts() {
      let that = this
      getEventTypefx().then(res => {
        this.charts2Data = res.data.data.map(item => ({
          name:item.label,
          value:item.cnt
        }))
        let myChart = this.$echarts.init(document.getElementById('typeAnalyseChart'))
        let imgUrl = require('@/assets/common/piebg.png')
        let option = {
          color: ['#00C0FF', '#22E8E8', '#FFD461', '#A9DB52', '#B76FD8'],

          tooltip: {
            trigger: 'item',
            // formatter: '{b}: <br/> {d}%',
            formatter: '{b}: <br/> {c}个<br/> {d}%',
            borderWidth: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '25',
            },
          },
          legend: {
            orient: 'vertical',
            left: '60%',
            top: '30%',
            bottom: '0%',
            icon: 'circle',
            itemGap: 30,
            textStyle: {
              rich: {
                name: {
                  fontSize: 25,
                  color: '#ffffff',
                  padding: [0, 20, 0, 15],
                },
                value: {
                  fontSize: 25,
                  color: '#FFC460',
                  // padding: [10, 0, 0, 15]
                },
              },
            },
            formatter: function (name) {
              var data = option.series[0].data //获取series中的data
              var total = 0
              var tarValue
              for (var i = 0, l = data.length; i < l; i++) {
                total += data[i].value
                if (data[i].name == name) {
                  tarValue = data[i].value
                }
              }
              that.serverNum = total
              var p = ((tarValue / total) * 100).toFixed(2)
              return '{name|' + name + '}{value|' + p + '%}'
            },
          },
          graphic: [
            {
              type: 'image',
              id: 'logo',
              left: '14.3%',
              top: '34.4%',
              z: -10,
              bounding: 'raw',
              rotation: 0, //旋转
              origin: [50, 50], //中心点
              scale: [0.4, 0.4], //缩放
              style: {
                image: imgUrl,
                opacity: 1,
              },
            },
          ],
          series: [
            {
              name: 'Radius Mode',
              type: 'pie',
              radius: [50, 160],
              center: ['25%', '50%'],
              roseType: 'radius',
              itemStyle: {
                borderRadius: 2,
              },
              label: {
                show: false,
              },
              emphasis: {
                label: {},
              },
              data: this.charts2Data,
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
  },
  watch: {},
}
</script>

<style scoped>
.typeAnalyseChart {
  height: 580px;
}
</style>
