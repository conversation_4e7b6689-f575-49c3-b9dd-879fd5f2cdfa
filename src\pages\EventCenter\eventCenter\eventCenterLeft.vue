<template>
  <div class="eventCenterLeft">
    <ktitle :titleText="'事件总体情况'"></ktitle>
    <eventSituation class="eventSituation"></eventSituation>
    <ktitle :titleText="'事件趋势'"></ktitle>
    <eventTrend class="eventTrend"></eventTrend>
    <div class="titlebox">
      <ktitle :titleText="'事件类型'" width="939.6"></ktitle>
      <ktitle :titleText="'事件来源'" width="939.6"></ktitle>
    </div>
    <div class="titlebox">
      <eventType></eventType>
      <eventSource></eventSource>
    </div>
  </div>
</template>

<script>
import ktitle from "@/components/title";
import titleTwo from "@/components/titleTwo";
import eventSituation from "./leftComponents/eventSituation.vue" //事件总体情况
import eventTrend from "./leftComponents/eventTrend.vue"  // 事件趋势
import eventSource from "./leftComponents/eventSource.vue" //事件来源
import eventType from "./leftComponents/eventType.vue" // 事件类型
export default {
  name: "eventCenterLeft",
  components: {
    ktitle,
    titleTwo,
    eventSituation,
    eventTrend,
    eventSource,
    eventType
  },
  data() {
    return {}
  },
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style lang="less" scoped>
.eventCenterLeft{
  margin: 80px 68px 0 68px;
  overflow: hidden;
  .eventSituation{
    height: 587px;
  }
  .eventTrend{
    height: 486px;
  }
  .titlebox{
    display: flex;
    align-items: center;
    .title{
     margin-right: 58px;
    }
  }
}
</style>