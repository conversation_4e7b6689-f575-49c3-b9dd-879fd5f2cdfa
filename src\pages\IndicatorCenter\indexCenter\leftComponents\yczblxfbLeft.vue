<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 09:48:42
 * @LastEditors: wjb
 * @LastEditTime: 2025-04-14 10:02:48
-->
<!--产销情况分析左侧-->
<template>
  <div>
    <div class="yczblxfbLeft" id="yczblxfbLeft"></div>
  </div>
</template>

<script>
import { getYczblxfb } from '@/api/IndicatorCenter'
export default {
  name: 'level',
  data() {
    return {
      charts2Data: [],
      myChart: null
    }
  },
  components: {},
  computed: {},
  mounted() {
    setTimeout(() => {
      this.getData()
    }, 300)
  },
  methods: {
    getData() {
      getYczblxfb().then((res) => {
        console.log('原始API响应:', res)
        if (res && res.data) {
          // 处理不同的数据结构情况
          if (res.data.data && Array.isArray(res.data.data)) {
            this.charts2Data = res.data.data
          } else if (Array.isArray(res.data)) {
            this.charts2Data = res.data
          } else {
            console.error('数据格式错误:', res.data)
            this.charts2Data = []
          }
          
          if (this.charts2Data.length > 0) {
            this.initCharts()
          } else {
            console.warn('图表数据为空')
          }
        } else {
          console.error('API返回数据为空')
        }
      }).catch(err => {
        console.error('获取数据出错:', err)
      })
    },
    initCharts() {
      let that = this
      let yczblxfbLeftEl = document.getElementById('yczblxfbLeft')
      console.log('DOM元素存在:', yczblxfbLeftEl !== null)
      console.log('charts2Data:', this.charts2Data)
      
      if (!yczblxfbLeftEl) {
        console.error('找不到DOM元素: yczblxfbLeft')
        return
      }
      
      // 如果已有图表实例，先销毁
      if (this.myChart) {
        this.myChart.dispose()
      }
      
      this.myChart = this.$echarts.init(yczblxfbLeftEl)
      let totalValue = 0
      
      if (!this.charts2Data || this.charts2Data.length === 0) {
        console.error('图表数据为空')
        return
      }
      
      // 确保数据格式正确
      let formattedData = this.charts2Data.map(item => {
        return {
          name: item.name,
          value: item.num
        }
      })
      
      formattedData.forEach((item) => {
        totalValue += item.value
      })
      
      console.log('处理后的数据:', formattedData)
      console.log('总值:', totalValue)
      
      let option = {
        color: [
          '#00C0FF',
          '#22E8E8',
          '#FFD461',
          '#A9DB52',
          '#B76FD8',
          '#FD852E',
          '#FF4949',
          '#0594C3',
          '#009D9D',
          '#A47905',
        ],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'vertical',
          left: '70%',
          top: '20%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 20,
          textStyle: {
            rich: {
              name: {
                fontSize: 25,
                color: '#ffffff',
                padding: [0, 20, 0, 15],
              },
              value: {
                fontSize: 25,
                color: '#FFC460',
                padding: [0, 0, 0, 15],
              },
              value1: {
                fontSize: 25,
                color: '#ffffff',
                // padding: [10, 0, 0, 15]
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue = 0
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = tarValue == 0 ? 0 : ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '}{value1|' + p + '%}{value|' + tarValue + '}'
          },
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['55%', '70%'],
            center: ['35%', '45%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: false,
            },
            data: formattedData,
          },
        ],
        graphic: [
          {
            type: 'text',
            left: '25%',
            top: '35%',
            style: {
              text: '异常指标数量',
              textAlign: 'center',
              fill: '#ffffff',
              fontSize: 32,
              width: 200,
            },
          },
          {
            type: 'text',
            left: '30%',
            top: '45%',
            style: {
              text: totalValue,
              textAlign: 'center',
              fill: '#0AB7FF',
              fontSize: 88,
              width: 200,
            },
          },
        ],
      }
      this.myChart.setOption(option)
      console.log('echarts配置项:', option)
      
      this.resizeHandler = () => {
        this.myChart && this.myChart.resize()
      }
      
      window.addEventListener('resize', this.resizeHandler)
      
      this.myChart.getZr().on('mousemove', (param) => {
        this.myChart.getZr().setCursorStyle('default')
      })
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
    this.myChart && this.myChart.dispose()
    this.myChart = null
  },
  watch: {},
}
</script>

<style scoped>
.yczblxfbLeft {
  width: 100%;
  height: 800px;
  position: relative;
  overflow: hidden;
}
</style>