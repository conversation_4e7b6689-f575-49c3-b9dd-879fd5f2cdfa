<template>
  <div>
    <div class="select">
      <el-select v-model="value" placeholder="请选择" @change="initCharts3">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="charts13" id="charts13"></div>
  </div>
</template>

<script>
import {getWarningQs} from "@/api/home";
export default {
  name: "charts3",
  data() {
    return {
      value:2, //各类预警默认
      options:[
        {label:"年度",value:1},
        {label:"季度",value:4},
        {label:"月度",value:2},
      ], //各类预警下拉列表
      charts2Data: []
    }
  },
  computed: {},
  mounted() {
    this.initCharts3()
  },
  methods: {
    initCharts3() {
      getWarningQs({type: this.value}).then(res => {
        this.charts2Data = res.data.data.map(item => ({
          name: item.name,
          value1: item.hjjcsb,
          value2: item.jhdc,
          value3: item.kqzsb,
          value4: item.qtyj,
          value5: item.sdrl,
          value6: item.szsb,
          value7: item.zhdg,
          value8: item.zhjg,
          value9: item.zhlgt,
        }))
        let myChart = this.$echarts.init(document.getElementById("charts13"));
        let option = {
          tooltip: {
            trigger: "axis",
            borderWidth: 0,
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            textStyle: {
              color: 'white',
              fontSize: '28',
            },
          },
          legend: {
            orient: "horizontal",
            // icon: "circle",
            icon: 'circle', //形状  类型包括 circle，rect,line，roundRect，triangle，diamond，pin，arrow，none
            itemWidth: 14, // 设置宽度
            itemHeight: 14, // 设置高度
            itemGap: 16, // 设置间距
            textStyle: {
              color: "#D6E7F9",
              fontSize: 18,
            },
            left:"8%",
            top:"13%"
          },
          grid: {
            left: "0%",
            right: "0%",
            top: "34%",
            bottom: "-2%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.charts2Data.map(item => item.name),
              axisLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)", // 颜色
                  width: 1, // 粗细
                },
              },
              axisTick: {
                show: false,
              },
              axisLabel: {
                interval: 0,
                textStyle: {
                  color: "#D6E7F9",
                  fontSize: 28,
                },
              },
            },
          ],
          yAxis: [
            {
              name: "预警数量/个",
              type: "value",
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: [10,-10,60,10],
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
            {
              name: "",
              type: "value",
              max: 100,
              nameTextStyle: {
                fontSize: 24,
                color: "#D6E7F9",
                padding: 5,
              },
              splitLine: {
                lineStyle: {
                  color: "rgb(119,179,241,.4)",
                },
              },
              axisLabel: {
                formatter: '{value}%',
                textStyle: {
                  fontSize: 28,
                  color: "#D6E7F9",
                },
              },
            },
          ],
          series: [
            {
              name: "环境监测设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#00C0FF",
                },
              },
              data: this.charts2Data.map(item => item.value1),
            },
            {
              name: "金华地磁",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#22E8E8",
                },
              },
              data: this.charts2Data.map(item => item.value2),
            },
            {
              name: "空气站设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#A9DB52",
                },
              },
              data: this.charts2Data.map(item => item.value3),
            },
            {
              name: "其它预警",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#FFC460",
                },
              },
              data: this.charts2Data.map(item => item.value4),
            },
            {
              name: "手动录入",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#fa0423",
                },
              },
              data: this.charts2Data.map(item => item.value5),
            },
            {
              name: "水质设备",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: "#f66203",
                },
              },
              data: this.charts2Data.map(item => item.value6),
            },
            {
              name: "智慧灯杆",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#B76FD8',
                },
              },
              data: this.charts2Data.map(item => item.value7),
            },
            {
              name: "智慧井盖",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#e8c991',
                },
              },
              data: this.charts2Data.map(item => item.value8),
            },
            {
              name: "智慧垃圾桶",
              type: "line",
              barWidth:30,
              smooth: false,
              symbolSize: 10,
              itemStyle: {
                normal: {
                  color: '#D2D2D2',
                },
              },
              data: this.charts2Data.map(item => item.value9),
            },
          ]
        };
        myChart.setOption(option)
        myChart.getZr().on('mousemove', param => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    }
  },
  watch: {}
}
</script>

<style scoped lang="less">
/deep/ .select {
  position: absolute;
   right: 48px;
  top: 60px;
  z-index: 999;

  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #FEFEFE;
  }
}
  .charts13 {
    width: 931px;
    height: 300px;
    margin-top: 26px;
  }
</style>