<template>
  <div>
    <div class="select">
      <div class="setImg" @click="popShow = !popShow"></div>
    </div>
    <div class="radios">
      <el-radio-group v-model="radio">
        <el-radio :label="item.value" v-for="(item, i) in radioOptions" :key="i">{{ item.name }}</el-radio>
      </el-radio-group>
    </div>
    <div class="charts15" id="charts15" v-show="radio == 1"></div>
    <div class="charts15" id="charts16" v-show="radio == 2"></div>
    <div class="charts15" id="charts17" v-show="radio == 3"></div>
    <div class="panel">
      <resourceAllocation :list="List" @change="listChange" v-show="popShow" labelKey="name" :maxCheckSize="4"/>
    </div>
  </div>
</template>

<script>
import resourceAllocation from "@/components/ResourceAllocation";
export default {
  name: 'charts5',
  data() {
    return {
      value: 1,
      radio: 1,
      popShow: false,
      radioOptions: [
        {
          name: '饼图',
          value: 1,
        },
        {
          name: '折线图',
          value: 2,
        },
        {
          name: '柱状图',
          value: 3,
        },
      ],
      options: [
        { label: '市政建设部', value: 1 },
        { label: '城市规划部', value: 2 },
        { label: '城市管理部', value: 3 },
        { label: '综合管理部', value: 4 },
        { label: '行政执法部', value: 5 },
      ],
      chartsData: [
        {
          name: '市政建设部',
          value: 22,
        },
        {
          name: '城市规划部',
          value: 30,
        },
        {
          name: '综合管理部',
          value: 50,
        },
        {
          name: '行政执法部',
          value: 41,
        },
      ],
      List: [
        {
          checked: true,
          name: '招商招才',
          value: 1,
        },
        {
          checked: true,
          name: '党群工作部',
          value: 1,
        },
        {
          checked: true,
          name: '党政综合部',
          value: 3,
        },
        {
          checked: true,
          name: '国资监管中心',
          value: 1,
        },
        {
          name: '建设更新部',
          value: 2,
        },
        {
          name: '经济发展部',
          value: 5,
        },
        {
          name: '社会事务部',
          value: 3,
        },
        {
          name: '社会治理部',
          value: 4,
        },
        {
          name: '乡村振兴部',
          value: 2,
        },
        {
          name: '行政服务中心',
          value: 2,
        },
        {
          name: '宣传部',
          value: 1,
        },
        {
          name: '行政执法分局',
          value: 1,
        },
        {
          name: '公安局江南分局',
          value: 3,
        },
        {
          name: '三江街道',
          value: 1,
        },
        {
          name: '苏孟乡',
          value: 1,
        }
      ]
    }
  },
  components: {
    resourceAllocation
  },
  computed: {},
  mounted() {
    this.initAllCharts()
  },
  methods: {
    initAllCharts() {
      this.initChartsPie()
      this.initChartsLine()
      this.initChartsBar()
    },
    initChartsPie() {
      let that = this
      let myChart = this.$echarts.init(document.getElementById('charts15'))
      let imgUrl = require('@/assets/common/piebg.png')
      let option = {
        color: [
          '#00C0FF',
          '#22E8E8',
          '#FFD461',
          '#A9DB52',
          '#B76FD8',
          '#FD852E',
          '#FF4949',
          '#0594C3',
          '#009D9D',
          '#A47905',
        ],

        tooltip: {
          trigger: 'item',
          // formatter: '{b}: <br/> {d}%',
          formatter: '{b}: <br/> {c}个<br/> {d}%',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '25',
          },
        },
        legend: {
          orient: 'horizontal',
          left: '42%',
          top: '25%',
          bottom: '0%',
          icon: 'circle',
          itemGap: 10,
          textStyle: {
            rich: {
              name: {
                fontSize: 30,
                color: '#ffffff',
                padding: [0, 20, 0, 15],
              },
              value: {
                fontSize: 36,
                color: '#FFC460',
                // padding: [10, 0, 0, 15]
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += data[i].value
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            that.serverNum = total
            var p = ((tarValue / total) * 100).toFixed(2)
            return '{name|' + name + '} \n \n {value|' + p + '%}'
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'logo',
            left: '13.6%',
            top: '10.4%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [0.8, 0.8], //缩放
            style: {
              image: imgUrl,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['28%', '51%'],
            roseType: '',
            itemStyle: {
              borderRadius: 0,
            },
            label: {
              show: false,
            },
            data: this.chartsData,
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    initChartsLine() {
      let myChart = this.$echarts.init(document.getElementById('charts16'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '20%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位: 个',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'line',
            barWidth: 30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#00C0FF',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,192,255,0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.value),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    initChartsBar() {
      let myChart = this.$echarts.init(document.getElementById('charts17'))
      let option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          left: '20%',
          right: '6%',
          top: '22%',
          bottom: '1%',
          containLabel: true,
        },
        xAxis: [
          {
            type: 'category',
            data: this.chartsData.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: '#D6E7F9',
                fontSize: 28,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位: 个',
            type: 'value',
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: [10, -100, 10, 10],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
          {
            name: '',
            type: 'value',
            max: 100,
            nameTextStyle: {
              fontSize: 24,
              color: '#D6E7F9',
              padding: 5,
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              formatter: '{value}%',
              textStyle: {
                fontSize: 28,
                color: '#D6E7F9',
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 30,
            smooth: false,
            symbolSize: 10,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: '#00C0FF',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0,192,255,0)',
                  },
                ]),
                barBorderRadius: 4,
              },
            },
            data: this.chartsData.map((item) => item.value),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    listChange(res) {
      this.chartsData = res;
      this.initAllCharts()
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
/deep/ .radios {
  position: absolute;
  left: 5px;
  top: 140px;
  z-index: 999;
  .el-radio-group {
    display: flex;
    justify-content: space-evenly;
    align-items: flex-start;
    flex-direction: column;
    height: 200px;
    .el-radio {
      .el-radio__input {
        .el-radio__inner {
          width: 17px;
          height: 17px;
          margin-top: -10px;
        }
      }
      .el-radio__label {
        font-size: 28px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #d6e7f9;
      }
    }
  }
}

/deep/ .select {
  position: absolute;
  right: 48px;
  top: 40px;
  z-index: 999;
  .setImg {
    width: 68px;
    height: 67px;
    background: url("~@/assets/Command/peizhi.png");
    background-size: cover;
    cursor: pointer;
  }
  .el-input__inner {
    width: 186px;
    height: 60px;
    border-radius: 40px;
    background: transparent;
    font-size: 30px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    color: #fefefe;
  }
}
.panel {
  position: fixed;
  top: 1055px;
  left: 775px;
}
.charts15 {
  width: 931px;
  height: 330px;
  margin-top: 10px;
}
</style>
