<!--经济生态-运行监测-->
<template>
  <div class="container">
    <div class="item" v-for="(item,i) in values" :key="i">
      <div class="item-container">
        <div class="title">{{item.name}}</div>
        <div class="value" :class="{green:item.value == 100}">{{item.value}} <span class="unit" v-if="i != 2">亿元</span> <span class="unit" v-else>%</span></div>
        <div class="progress">
          <progress :value="item.value" max="100" style="width: 243px;"></progress>
        </div>
<!--        <div class="status" :class="{green:item.value == 100}">{{item.value == 100?'已完成':'未完成'}}</div>-->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "OperationMonitoring",
  data() {
    return {
      values:[
        {
          name:"生产总值（GDP）",
          value:203.73
        },
        {
          name:"社会消费品零售总额",
          value:90.07
        },
        {
          name:"社会消费品零售总额增速",
          value:3.6
        }
      ]
    }
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">
  .unit {
    font-size: 30px;
    margin: 0 0 0 -15px;
  }
  .green {
    background: linear-gradient(180deg, #FFFFFF 0%, #A9DB52 50.244140625%, #F4F1FF 53.0029296875%, #F0FFD7 99.365234375%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
  }
  .container {
    width: 939px;
    height: 287px;
    margin-top: 63px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .item {
      width: 303px;
      height: 287px;
      background: url("@/assets/home/<USER>/itembg.png");
      background-size: cover;
      .item-container {
        margin: 20px 0 0 10px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        flex-direction: column;
        .title {
          width: 280px;
          height: 73px;
          word-break: break-all;
          font-size: 35px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #D6E7F9;
        }
        .value {
          font-size: 50px;
          font-family: Bebas Neue;
          font-weight: 400;
          color: #9AA9BF;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: center;
          margin-top: 49px;
        }
        .progress {
          margin-top: 5px;
        }
        .status {
          font-size: 37px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #D6E7F9;
          background: linear-gradient(180deg, #FFFFFF 0%, #FFC460 50.244140625%, #FFFFFF 53.0029296875%, #FFECCB 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: center;
          margin-top: 10px;
        }
      }
    }
  }
</style>