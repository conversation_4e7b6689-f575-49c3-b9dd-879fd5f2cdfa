<template>
  <div class="container">
    <zhtxTable
      :tableData="tableData"
      :title-with-key="titleList"
      style="margin-top: 20px"
      :height="275"
    ></zhtxTable>
  </div>
</template>

<script>
import Ktitle from '@/components/titleCommon.vue'
import zhtxTable from "@/components/zhtxTable";
export default {
  name: "ccqkfxLeft",
  data() {
    return {
      tableData: [
        {
          index: "1",
          value1:"金华市大地物流有限公司仓储基地",
          value2:"浙江省金华市婺城区干西工贸区易通路216号",
          value3:"石材、钢筋",
          value4:"9590"
        },
        {
          index: "2",
          value1:"金华市金江物资有限公司仓库",
          value2:"浙江省金华市婺城区凉帽山路332号",
          value3:"食材",
          value4:"2580"
        },
        {
          index: "3",
          value1:"建佳物流中心",
          value2:"浙江省金华市婺城区秋沈路309号东南方向70米",
          value3:"海鲜",
          value4:"5928"
        }
      ],
      titleList: [
        {
          label: '序号',
          key: 'index',
          flex: 1,
        },
        {
          label: '仓库',
          key: 'value1',
          flex: 3,
        },
        {
          label: '仓储地点',
          key: 'value2',
          flex: 4,
        },
        {
          label: '储物概况',
          key: 'value3',
          flex: 2,
        },
        {
          label: '现存量',
          key: 'value4',
          flex: 2,
        }
      ],
    }
  },
  components: {
    Ktitle,
    zhtxTable
  },
  computed: {},
  mounted() {

  },
  methods: {},
  watch: {}
}
</script>

<style scoped lang="less">

</style>