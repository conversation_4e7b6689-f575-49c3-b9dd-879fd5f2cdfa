<template>
  <div>
    <div class="charts2" id="charts2"></div>
  </div>
</template>

<script>
import {getEventCzfx} from "@/api/EventTask";
export default {
  data() {
    return {
      chartData: [
        {
          id: '1',
          name: '综合执法分局',
          sb: 100,
          sl: 80,
          bl: 70,
          ja: 150,
        },
        {
          id: '2',
          name: '网格办',
          sb: 70,
          sl: 60,
          bl: 50,
          ja: 220,
        },
        {
          id: '3',
          name: '应急管理局',
          sb: 120,
          sl: 100,
          bl: 90,
          ja: 160,
        },
        {
          id: '4',
          name: '行政服务中心',
          sb: 220,
          sl: 160,
          bl: 110,
          ja: 170,
        },
        {
          id: '5',
          name: '金华市公安局江南分局',
          sb: 200,
          sl: 210,
          bl: 140,
          ja: 200,
        },
      ],
    }
  },
  mounted() {
    this.initChartLocal()
  },
  methods: {
    initChart() {
      getEventCzfx({type: "1"}).then(res => {
        this.chartData = res.data.data.map(item => ({
          name: item.majorOrgId,
          sb: item.sb,
          sl: item.sl,
          bl: item.bl,
          ja: item.ja,
        }))
        let myChart = this.$echarts.init(document.getElementById('charts2'))
        const option = {
          color: ['#FF4A48', '#FDC260', '#00BEFC', '#22E4E5'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              // Use axis to trigger tooltip
              type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
            },
          },
          legend: {
            top: '4%',
            itemGap: 20,
            itemWidth: 40,
            height: 22,
            textStyle: {
              fontSize: 20,
              color: '#D6E7F9',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              textStyle: {
                fontSize: 20,
                color: '#D6E7F9',
              },
            },
          },
          yAxis: {
            type: 'category',
            axisLabel: {
              textStyle: {
                fontSize: 20,
                color: '#00F0FF',
              },
            },
            data: this.chartData.map((item) => item.name),
          },
          series: [
            {
              name: '上报',
              type: 'bar',
              stack: 'total',
              label: {
                show: false,
              },
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.map((item) => item.sb),
            },
            {
              name: '受理',
              type: 'bar',
              stack: 'total',
              label: {
                show: false,
              },
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.map((item) => item.sl),
            },
            {
              name: '办理',
              type: 'bar',
              stack: 'total',
              label: {
                show: false,
              },
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.map((item) => item.bl),
            },
            {
              name: '结案',
              type: 'bar',
              stack: 'total',
              label: {
                show: false,
              },
              emphasis: {
                focus: 'series',
              },
              data: this.chartData.map((item) => item.ja),
            },
          ],
        }
        myChart.setOption(option)
        myChart.getZr().on('mousemove', (param) => {
          myChart.getZr().setCursorStyle('default')
        })
      })
    },
    initChartLocal() {
      let myChart = this.$echarts.init(document.getElementById('charts2'))
      const option = {
        color: ['#FF4A48', '#FDC260', '#00BEFC', '#22E4E5'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
          },
        },
        legend: {
          top: '4%',
          itemGap: 20,
          itemWidth: 40,
          height: 22,
          textStyle: {
            fontSize: 20,
            color: '#D6E7F9',
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: '#D6E7F9',
            },
          },
        },
        yAxis: {
          type: 'category',
          axisLabel: {
            textStyle: {
              fontSize: 20,
              color: '#00F0FF',
            },
          },
          data: this.chartData.map((item) => item.name),
        },
        series: [
          {
            name: '上报',
            type: 'bar',
            stack: 'total',
            label: {
              show: false,
            },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.map((item) => item.sb),
          },
          {
            name: '受理',
            type: 'bar',
            stack: 'total',
            label: {
              show: false,
            },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.map((item) => item.sl),
          },
          {
            name: '办理',
            type: 'bar',
            stack: 'total',
            label: {
              show: false,
            },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.map((item) => item.bl),
          },
          {
            name: '结案',
            type: 'bar',
            stack: 'total',
            label: {
              show: false,
            },
            emphasis: {
              focus: 'series',
            },
            data: this.chartData.map((item) => item.ja),
          },
        ],
      }
      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    }
  },
}
</script>

<style scoped lang="less">
.charts2 {
  width: 1000px;
  height: 450px;
}
</style>
